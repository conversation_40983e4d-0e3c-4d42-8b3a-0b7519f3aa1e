"""
数据脱敏模块
实现姓名、电话、身份证等敏感信息的脱敏处理
"""
import hashlib
import random
import re
import pandas as pd
from typing import Dict, List, Optional, Any
from utils.logger import logger


class NameAnonymizer:
    """姓名脱敏器"""
    
    def __init__(self):
        # 常见姓氏
        self.surnames = [
            '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
            '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
            '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧'
        ]
        
        # 常见名字字符
        self.name_chars = [
            '伟', '芳', '娜', '秀', '敏', '静', '丽', '强', '磊', '军',
            '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀', '霞',
            '平', '刚', '桂', '英', '华', '玉', '萍', '红', '娥', '玲',
            '梅', '莹', '雪', '琳', '晶', '妍', '茜', '秋', '珊', '莎'
        ]
    
    def anonymize_name(self, name: str, method: str = 'mask') -> str:
        """脱敏姓名"""
        if pd.isna(name) or not name:
            return ''
        
        name = str(name).strip()
        
        if method == 'mask':
            return self._mask_name(name)
        elif method == 'replace':
            return self._replace_name(name)
        elif method == 'hash':
            return self._hash_name(name)
        else:
            return self._mask_name(name)
    
    def _mask_name(self, name: str) -> str:
        """掩码方式脱敏"""
        if len(name) <= 1:
            return '*'
        elif len(name) == 2:
            return name[0] + '*'
        else:
            return name[0] + '*' * (len(name) - 2) + name[-1]
    
    def _replace_name(self, name: str) -> str:
        """替换方式脱敏"""
        # 保持姓氏，替换名字
        if len(name) >= 2:
            surname = name[0]
            name_length = len(name) - 1
            new_name = surname + ''.join(random.choices(self.name_chars, k=name_length))
            return new_name
        else:
            return random.choice(self.surnames) + random.choice(self.name_chars)
    
    def _hash_name(self, name: str) -> str:
        """哈希方式脱敏"""
        hash_obj = hashlib.md5(name.encode('utf-8'))
        hash_hex = hash_obj.hexdigest()
        return f"患者{hash_hex[:6].upper()}"


class PhoneAnonymizer:
    """电话号码脱敏器"""
    
    def anonymize_phone(self, phone: str, method: str = 'mask') -> str:
        """脱敏电话号码"""
        if pd.isna(phone) or not phone:
            return ''
        
        phone = str(phone).strip()
        
        if method == 'mask':
            return self._mask_phone(phone)
        elif method == 'replace':
            return self._replace_phone(phone)
        elif method == 'hash':
            return self._hash_phone(phone)
        else:
            return self._mask_phone(phone)
    
    def _mask_phone(self, phone: str) -> str:
        """掩码方式脱敏"""
        if len(phone) == 11:
            return phone[:3] + '****' + phone[7:]
        elif len(phone) >= 7:
            return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]
        else:
            return '*' * len(phone)
    
    def _replace_phone(self, phone: str) -> str:
        """替换方式脱敏"""
        if len(phone) == 11:
            # 保持手机号格式
            prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                       '150', '151', '152', '153', '155', '156', '157', '158', '159',
                       '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
            new_prefix = random.choice(prefixes)
            new_suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            return new_prefix + new_suffix
        else:
            return ''.join([str(random.randint(0, 9)) for _ in range(len(phone))])
    
    def _hash_phone(self, phone: str) -> str:
        """哈希方式脱敏"""
        hash_obj = hashlib.md5(phone.encode('utf-8'))
        hash_hex = hash_obj.hexdigest()
        return f"TEL{hash_hex[:8].upper()}"


class IdCardAnonymizer:
    """身份证号脱敏器"""
    
    def anonymize_id_card(self, id_card: str, method: str = 'mask') -> str:
        """脱敏身份证号"""
        if pd.isna(id_card) or not id_card:
            return ''
        
        id_card = str(id_card).strip()
        
        if method == 'mask':
            return self._mask_id_card(id_card)
        elif method == 'replace':
            return self._replace_id_card(id_card)
        elif method == 'hash':
            return self._hash_id_card(id_card)
        else:
            return self._mask_id_card(id_card)
    
    def _mask_id_card(self, id_card: str) -> str:
        """掩码方式脱敏"""
        if len(id_card) == 18:
            return id_card[:6] + '********' + id_card[14:]
        elif len(id_card) >= 10:
            return id_card[:4] + '*' * (len(id_card) - 8) + id_card[-4:]
        else:
            return '*' * len(id_card)
    
    def _replace_id_card(self, id_card: str) -> str:
        """替换方式脱敏"""
        if len(id_card) == 18:
            # 保持地区码和校验码格式
            area_code = id_card[:6]
            birth_year = str(random.randint(1950, 2010))
            birth_month = str(random.randint(1, 12)).zfill(2)
            birth_day = str(random.randint(1, 28)).zfill(2)
            sequence = str(random.randint(100, 999))
            check_digit = random.choice(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X'])
            return area_code + birth_year + birth_month + birth_day + sequence + check_digit
        else:
            return ''.join([str(random.randint(0, 9)) for _ in range(len(id_card))])
    
    def _hash_id_card(self, id_card: str) -> str:
        """哈希方式脱敏"""
        hash_obj = hashlib.md5(id_card.encode('utf-8'))
        hash_hex = hash_obj.hexdigest()
        return f"ID{hash_hex[:12].upper()}"


class DataAnonymizer:
    """数据脱敏主类"""
    
    def __init__(self):
        self.name_anonymizer = NameAnonymizer()
        self.phone_anonymizer = PhoneAnonymizer()
        self.id_card_anonymizer = IdCardAnonymizer()
        logger.info("数据脱敏器初始化完成")
    
    def anonymize_dataframe(self, df: pd.DataFrame, 
                          anonymize_config: Dict[str, Dict[str, str]] = None) -> pd.DataFrame:
        """脱敏整个DataFrame"""
        logger.info(f"开始脱敏数据，共 {len(df)} 行")
        
        # 创建副本避免修改原数据
        anonymized_df = df.copy()
        
        # 默认脱敏配置
        if anonymize_config is None:
            anonymize_config = {
                '患者姓名': {'type': 'name', 'method': 'mask'},
                '联系电话': {'type': 'phone', 'method': 'mask'},
                '身份证号': {'type': 'id_card', 'method': 'mask'},
                'patient_name': {'type': 'name', 'method': 'mask'},
                'phone': {'type': 'phone', 'method': 'mask'},
                'id_card': {'type': 'id_card', 'method': 'mask'}
            }
        
        # 应用脱敏规则
        for field_name, config in anonymize_config.items():
            if field_name in anonymized_df.columns:
                anonymize_type = config.get('type', 'name')
                method = config.get('method', 'mask')
                
                if anonymize_type == 'name':
                    anonymized_df[f'{field_name}_anonymized'] = anonymized_df[field_name].apply(
                        lambda x: self.name_anonymizer.anonymize_name(x, method)
                    )
                elif anonymize_type == 'phone':
                    anonymized_df[f'{field_name}_anonymized'] = anonymized_df[field_name].apply(
                        lambda x: self.phone_anonymizer.anonymize_phone(x, method)
                    )
                elif anonymize_type == 'id_card':
                    anonymized_df[f'{field_name}_anonymized'] = anonymized_df[field_name].apply(
                        lambda x: self.id_card_anonymizer.anonymize_id_card(x, method)
                    )
        
        logger.info("数据脱敏完成")
        return anonymized_df
    
    def create_anonymization_mapping(self, df: pd.DataFrame, 
                                   fields: List[str]) -> Dict[str, Dict[str, str]]:
        """创建脱敏映射表（用于数据恢复）"""
        mapping = {}
        
        for field in fields:
            if field in df.columns:
                field_mapping = {}
                unique_values = df[field].dropna().unique()
                
                for value in unique_values:
                    if field in ['患者姓名', 'patient_name']:
                        anonymized = self.name_anonymizer.anonymize_name(value, 'replace')
                    elif field in ['联系电话', 'phone']:
                        anonymized = self.phone_anonymizer.anonymize_phone(value, 'replace')
                    elif field in ['身份证号', 'id_card']:
                        anonymized = self.id_card_anonymizer.anonymize_id_card(value, 'replace')
                    else:
                        anonymized = value
                    
                    field_mapping[str(value)] = anonymized
                
                mapping[field] = field_mapping
        
        return mapping
    
    def apply_anonymization_mapping(self, df: pd.DataFrame, 
                                  mapping: Dict[str, Dict[str, str]]) -> pd.DataFrame:
        """应用脱敏映射表"""
        anonymized_df = df.copy()
        
        for field, field_mapping in mapping.items():
            if field in anonymized_df.columns:
                anonymized_df[f'{field}_anonymized'] = anonymized_df[field].map(
                    lambda x: field_mapping.get(str(x), x) if pd.notna(x) else x
                )
        
        return anonymized_df
    
    def get_anonymization_summary(self, original_df: pd.DataFrame, 
                                anonymized_df: pd.DataFrame) -> Dict[str, Any]:
        """获取脱敏摘要"""
        summary = {
            'total_rows': len(original_df),
            'anonymized_rows': len(anonymized_df),
            'fields_anonymized': [],
            'anonymization_stats': {}
        }
        
        # 统计脱敏的字段
        for col in anonymized_df.columns:
            if col.endswith('_anonymized'):
                original_field = col.replace('_anonymized', '')
                summary['fields_anonymized'].append(original_field)
                
                # 统计脱敏前后的唯一值数量
                if original_field in original_df.columns:
                    original_unique = original_df[original_field].nunique()
                    anonymized_unique = anonymized_df[col].nunique()
                    summary['anonymization_stats'][original_field] = {
                        'original_unique_count': original_unique,
                        'anonymized_unique_count': anonymized_unique
                    }
        
        return summary
