#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据导入过程
逐步检查数据处理的每个环节
"""

import pandas as pd
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from services.data_service import DataImportService

def debug_column_standardization():
    """调试列名标准化过程"""
    print("🔍 调试列名标准化过程")
    print("=" * 50)
    
    # 创建测试数据
    test_data = pd.DataFrame([
        {
            '患者ID': '**********',
            '患者姓名': '张三',
            '手术日期': '2025-06-30',
            '手术名称': '阑尾切除术',
            '麻醉方法': '全身麻醉',
            '年龄': 35,
            '性别': '男'
        }
    ])
    
    print("📋 原始数据列名:")
    for i, col in enumerate(test_data.columns):
        print(f"   {i+1}. {col}")
    
    # 初始化数据服务
    data_service = DataImportService()
    
    # 测试列名标准化
    print("\n🔄 执行列名标准化...")
    try:
        standardized_df = data_service._standardize_column_names(test_data)
        
        print("✅ 标准化后的列名:")
        for i, col in enumerate(standardized_df.columns):
            print(f"   {i+1}. {col}")
        
        print("\n📊 标准化后的数据:")
        print(standardized_df.to_string())
        
        # 检查必需字段
        required_fields = ['patient_id', 'patient_name', 'surgery_date']
        missing_fields = []
        for field in required_fields:
            if field not in standardized_df.columns:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"\n❌ 缺少必需字段: {missing_fields}")
        else:
            print(f"\n✅ 所有必需字段都存在")
            
        return standardized_df
        
    except Exception as e:
        print(f"❌ 列名标准化失败: {e}")
        return None

def debug_data_validation(df):
    """调试数据验证过程"""
    print("\n🔍 调试数据验证过程")
    print("=" * 50)
    
    if df is None:
        print("❌ 没有数据可验证")
        return
    
    # 初始化数据服务
    data_service = DataImportService()
    
    try:
        validation_result = data_service._validate_data(df)
        
        print("📊 验证结果:")
        print(f"   错误数量: {len(validation_result['errors'])}")
        print(f"   错误行数: {len(validation_result['error_rows'])}")
        print(f"   有效行数: {validation_result['valid_rows']}")
        
        if validation_result['errors']:
            print("\n❌ 验证错误:")
            for i, error in enumerate(validation_result['errors']):
                print(f"   {i+1}. {error}")
        else:
            print("\n✅ 数据验证通过")
            
        return validation_result
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return None

def debug_data_cleaning(df):
    """调试数据清洗过程"""
    print("\n🔍 调试数据清洗过程")
    print("=" * 50)
    
    if df is None:
        print("❌ 没有数据可清洗")
        return None
    
    # 初始化数据服务
    data_service = DataImportService()
    
    try:
        cleaned_df = data_service._clean_data(df)
        
        print("📊 清洗结果:")
        print(f"   原始行数: {len(df)}")
        print(f"   清洗后行数: {len(cleaned_df)}")
        
        print("\n📋 清洗后的列名:")
        for i, col in enumerate(cleaned_df.columns):
            print(f"   {i+1}. {col}")
        
        print("\n📊 清洗后的数据:")
        print(cleaned_df.to_string())
        
        return cleaned_df
        
    except Exception as e:
        print(f"❌ 数据清洗失败: {e}")
        return None

def debug_field_mapping():
    """调试字段映射逻辑"""
    print("\n🔍 调试字段映射逻辑")
    print("=" * 50)
    
    # 测试字段映射规则
    test_columns = ['患者ID', '患者姓名', '手术日期', '手术名称', '麻醉方法', '年龄', '性别']
    
    # 后端的字段映射规则
    required_fields_mapping = {
        'patient_id': ['patient_id', '患者ID', '病人ID', '住院号'],
        'patient_name': ['patient_name', '患者姓名', '病人姓名', '姓名'],
        'surgery_date': ['surgery_date', '手术日期', '手术时间', '日期']
    }
    
    print("📋 测试列名:", test_columns)
    print("\n🔍 字段映射检查:")
    
    for field_key, possible_names in required_fields_mapping.items():
        found = False
        for name in possible_names:
            if name in test_columns:
                print(f"   ✅ {field_key}: 找到匹配字段 '{name}'")
                found = True
                break
        if not found:
            print(f"   ❌ {field_key}: 未找到匹配字段 (期望: {possible_names})")

def debug_complete_process():
    """调试完整的数据处理过程"""
    print("\n🔍 调试完整的数据处理过程")
    print("=" * 50)
    
    # 创建测试数据
    test_data = pd.DataFrame([
        {
            '患者ID': '**********',
            '患者姓名': '张三',
            '手术日期': '2025-06-30',
            '手术名称': '阑尾切除术',
            '麻醉方法': '全身麻醉',
            '年龄': 35,
            '性别': '男'
        },
        {
            '患者ID': '2025123457', 
            '患者姓名': '李四',
            '手术日期': '2025-07-01',
            '手术名称': '胆囊切除术',
            '麻醉方法': '腰硬联合麻醉',
            '年龄': 42,
            '性别': '女'
        }
    ])
    
    print("📋 原始测试数据:")
    print(test_data.to_string())
    
    # 步骤1: 列名标准化
    print("\n" + "="*30)
    print("步骤1: 列名标准化")
    standardized_df = debug_column_standardization()
    
    if standardized_df is None:
        print("❌ 列名标准化失败，无法继续")
        return
    
    # 步骤2: 数据清洗
    print("\n" + "="*30)
    print("步骤2: 数据清洗")
    cleaned_df = debug_data_cleaning(standardized_df)
    
    if cleaned_df is None:
        print("❌ 数据清洗失败，无法继续")
        return
    
    # 步骤3: 数据验证
    print("\n" + "="*30)
    print("步骤3: 数据验证")
    validation_result = debug_data_validation(cleaned_df)
    
    # 总结
    print("\n" + "="*50)
    print("🎯 调试总结")
    print("="*50)
    
    if validation_result and len(validation_result['error_rows']) == 0:
        print("✅ 所有步骤都成功，数据应该可以正常导入")
    else:
        print("❌ 发现问题，需要修复:")
        if validation_result:
            for error in validation_result['errors']:
                print(f"   - {error}")

def debug_database_import():
    """调试数据库导入过程"""
    print("\n🔍 调试数据库导入过程")
    print("=" * 50)

    # 创建测试数据
    test_data = pd.DataFrame([
        {
            'patient_id': '**********',
            'patient_name': '张三',
            'surgery_date': '2025-06-30',
            'surgery_type': '阑尾切除术',
            'anesthesia_method': '全身麻醉',
            'age': 35,
            'gender': '男'
        }
    ])

    print("📋 准备导入的数据:")
    print(test_data.to_string())

    # 初始化数据服务
    data_service = DataImportService()

    try:
        # 模拟导入配置
        import_config = {
            'enablePrivacy': True,
            'enableCleaning': True,
            'skipDuplicates': True,
            'validateData': True,
            'duplicateStrategy': 'skip',
            'errorStrategy': 'skip',
            'batchSize': 500
        }

        batch_id = "debug_test_batch"

        print("\n🔄 执行数据库导入...")
        result = data_service._process_and_import_data(
            test_data, batch_id, True, import_config
        )

        print("📊 导入结果:")
        for key, value in result.items():
            print(f"   {key}: {value}")

        return result

    except Exception as e:
        print(f"❌ 数据库导入失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🚀 开始调试数据导入过程")
    print("=" * 60)

    # 1. 调试字段映射逻辑
    debug_field_mapping()

    # 2. 调试完整处理过程
    debug_complete_process()

    # 3. 调试数据库导入
    debug_database_import()

    print("\n" + "=" * 60)
    print("🎯 调试完成")

if __name__ == "__main__":
    main()
