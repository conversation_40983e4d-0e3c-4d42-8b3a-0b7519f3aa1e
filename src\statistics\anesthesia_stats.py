"""
麻醉统计模块
"""
from typing import Dict, Any, List, Tuple
from database.models import DatabaseManager
from utils.logger import logger


class AnesthesiaStatistics:
    """麻醉相关统计分析"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_anesthesia_method_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取麻醉方法分布统计"""
        logger.info("获取麻醉方法分布统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    COALESCE(anesthesia_method, '未知') as method,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY anesthesia_method
                ORDER BY count DESC
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def get_anesthesia_method_by_age(self, start_date: str = None, end_date: str = None) -> Dict[str, Dict[str, int]]:
        """获取不同年龄段的麻醉方法分布"""
        logger.info("获取不同年龄段的麻醉方法分布")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    CASE 
                        WHEN CAST(age AS INTEGER) < 18 THEN '未成年(<18岁)'
                        WHEN CAST(age AS INTEGER) BETWEEN 18 AND 30 THEN '青年(18-30岁)'
                        WHEN CAST(age AS INTEGER) BETWEEN 31 AND 50 THEN '中年(31-50岁)'
                        WHEN CAST(age AS INTEGER) BETWEEN 51 AND 70 THEN '中老年(51-70岁)'
                        WHEN CAST(age AS INTEGER) > 70 THEN '老年(>70岁)'
                        ELSE '未知'
                    END as age_group,
                    COALESCE(anesthesia_method, '未知') as method,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                AND age IS NOT NULL AND age != ''
                GROUP BY age_group, anesthesia_method
                ORDER BY age_group, count DESC
            ''', params)
            
            result = {}
            for row in cursor.fetchall():
                age_group, method, count = row
                if age_group not in result:
                    result[age_group] = {}
                result[age_group][method] = count
            
            return result
    
    def get_anesthesia_method_by_surgery_type(self, start_date: str = None, end_date: str = None) -> Dict[str, Dict[str, int]]:
        """获取不同手术类型的麻醉方法分布"""
        logger.info("获取不同手术类型的麻醉方法分布")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    COALESCE(surgery_type, '未知') as surgery_type,
                    COALESCE(anesthesia_method, '未知') as method,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY surgery_type, anesthesia_method
                ORDER BY surgery_type, count DESC
            ''', params)
            
            result = {}
            for row in cursor.fetchall():
                surgery_type, method, count = row
                if surgery_type not in result:
                    result[surgery_type] = {}
                result[surgery_type][method] = count
            
            return result
    
    def get_anesthesiologist_workload(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """获取麻醉医生工作量统计"""
        logger.info("获取麻醉医生工作量统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    anesthesiologist,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT DATE(surgery_date)) as work_days,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration
                FROM surgery_records 
                {where_clause}
                AND anesthesiologist IS NOT NULL AND anesthesiologist != ''
                GROUP BY anesthesiologist
                ORDER BY surgery_count DESC
            ''', params)
            
            result = []
            for row in cursor.fetchall():
                result.append({
                    'anesthesiologist': row[0],
                    'surgery_count': row[1],
                    'work_days': row[2],
                    'avg_duration': row[3] or 0,
                    'total_duration': row[4] or 0,
                    'avg_surgeries_per_day': round(row[1] / max(row[2], 1), 2)
                })
            
            return result
    
    def get_anesthesia_duration_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取麻醉时长分析"""
        logger.info("获取麻醉时长分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 麻醉时长统计
            cursor = conn.execute(f'''
                SELECT 
                    anesthesia_method,
                    COUNT(*) as count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    MIN(CAST(duration_minutes AS REAL)) as min_duration,
                    MAX(CAST(duration_minutes AS REAL)) as max_duration
                FROM surgery_records 
                {where_clause}
                AND duration_minutes IS NOT NULL AND duration_minutes != ''
                AND CAST(duration_minutes AS REAL) > 0
                GROUP BY anesthesia_method
                ORDER BY avg_duration DESC
            ''', params)
            
            method_duration = {}
            for row in cursor.fetchall():
                method_duration[row[0]] = {
                    'count': row[1],
                    'avg_duration': row[2] or 0,
                    'min_duration': row[3] or 0,
                    'max_duration': row[4] or 0
                }
            
            # 时长分布
            duration_cursor = conn.execute(f'''
                SELECT 
                    CASE 
                        WHEN CAST(duration_minutes AS REAL) <= 60 THEN '≤1小时'
                        WHEN CAST(duration_minutes AS REAL) <= 120 THEN '1-2小时'
                        WHEN CAST(duration_minutes AS REAL) <= 180 THEN '2-3小时'
                        WHEN CAST(duration_minutes AS REAL) <= 240 THEN '3-4小时'
                        WHEN CAST(duration_minutes AS REAL) <= 300 THEN '4-5小时'
                        ELSE '>5小时'
                    END as duration_range,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                AND duration_minutes IS NOT NULL AND duration_minutes != ''
                AND CAST(duration_minutes AS REAL) > 0
                GROUP BY duration_range
                ORDER BY 
                    CASE 
                        WHEN duration_range = '≤1小时' THEN 1
                        WHEN duration_range = '1-2小时' THEN 2
                        WHEN duration_range = '2-3小时' THEN 3
                        WHEN duration_range = '3-4小时' THEN 4
                        WHEN duration_range = '4-5小时' THEN 5
                        ELSE 6
                    END
            ''', params)
            
            duration_distribution = {row[0]: row[1] for row in duration_cursor.fetchall()}
            
            return {
                'method_duration': method_duration,
                'duration_distribution': duration_distribution
            }
    
    def get_anesthesia_complications(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取麻醉并发症统计"""
        logger.info("获取麻醉并发症统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 并发症总体统计
            cursor = conn.execute(f'''
                SELECT 
                    COUNT(*) as total_cases,
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complication_cases
                FROM surgery_records 
                {where_clause}
            ''', params)
            
            total_row = cursor.fetchone()
            total_cases = total_row[0]
            complication_cases = total_row[1]
            complication_rate = (complication_cases / max(total_cases, 1)) * 100
            
            # 按麻醉方法统计并发症
            method_cursor = conn.execute(f'''
                SELECT 
                    anesthesia_method,
                    COUNT(*) as total,
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complications
                FROM surgery_records 
                {where_clause}
                GROUP BY anesthesia_method
                ORDER BY anesthesia_method
            ''', params)
            
            method_complications = {}
            for row in method_cursor.fetchall():
                method, total, complications = row
                rate = (complications / max(total, 1)) * 100
                method_complications[method] = {
                    'total': total,
                    'complications': complications,
                    'rate': round(rate, 2)
                }
            
            return {
                'total_cases': total_cases,
                'complication_cases': complication_cases,
                'complication_rate': round(complication_rate, 2),
                'method_complications': method_complications
            }
    
    def _build_date_filter(self, start_date: str = None, end_date: str = None) -> Tuple[str, List]:
        """构建日期过滤条件"""
        conditions = []
        params = []
        
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
    
    def _build_where_clause(self, start_date: str = None, end_date: str = None, additional_conditions: List[str] = None) -> Tuple[str, List]:
        """构建完整的WHERE子句"""
        conditions = []
        params = []
        
        # 添加日期条件
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        # 添加额外条件
        if additional_conditions:
            conditions.extend(additional_conditions)
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
