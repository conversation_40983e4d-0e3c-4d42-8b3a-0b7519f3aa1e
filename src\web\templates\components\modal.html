<!-- 通用模态框组件 -->

<!-- 确认对话框 -->
<div id="confirm-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg" id="confirm-title">确认操作</h3>
        <p class="py-4" id="confirm-message">您确定要执行此操作吗？</p>
        <div class="modal-action">
            <button class="btn btn-outline" onclick="closeConfirmModal()">取消</button>
            <button class="btn btn-primary" id="confirm-button" onclick="confirmAction()">确认</button>
        </div>
    </div>
</div>

<!-- 信息对话框 -->
<div id="info-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg" id="info-title">信息</h3>
        <div class="py-4" id="info-content">
            <!-- 信息内容将在这里显示 -->
        </div>
        <div class="modal-action">
            <button class="btn btn-primary" onclick="closeInfoModal()">确定</button>
        </div>
    </div>
</div>

<!-- 表单对话框 -->
<div id="form-modal" class="modal">
    <div class="modal-box max-w-2xl">
        <h3 class="font-bold text-lg" id="form-title">表单</h3>
        <div class="py-4" id="form-content">
            <!-- 表单内容将在这里显示 -->
        </div>
        <div class="modal-action">
            <button class="btn btn-outline" onclick="closeFormModal()">取消</button>
            <button class="btn btn-primary" id="form-submit-button" onclick="submitFormModal()">提交</button>
        </div>
    </div>
</div>

<!-- 图片预览对话框 -->
<div id="image-modal" class="modal">
    <div class="modal-box max-w-4xl">
        <h3 class="font-bold text-lg" id="image-title">图片预览</h3>
        <div class="py-4">
            <img id="image-preview" src="" alt="预览图片" class="w-full h-auto rounded-lg">
        </div>
        <div class="modal-action">
            <button class="btn btn-outline" onclick="downloadImage()">
                <i class="fas fa-download mr-2"></i>下载
            </button>
            <button class="btn btn-primary" onclick="closeImageModal()">关闭</button>
        </div>
    </div>
</div>

<!-- 详情查看对话框 -->
<div id="detail-modal" class="modal">
    <div class="modal-box max-w-4xl max-h-[80vh] overflow-y-auto">
        <h3 class="font-bold text-lg" id="detail-title">详情</h3>
        <div class="py-4" id="detail-content">
            <!-- 详情内容将在这里显示 -->
        </div>
        <div class="modal-action">
            <button class="btn btn-outline" onclick="printDetail()">
                <i class="fas fa-print mr-2"></i>打印
            </button>
            <button class="btn btn-primary" onclick="closeDetailModal()">关闭</button>
        </div>
    </div>
</div>

<!-- 选择器对话框 -->
<div id="selector-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg" id="selector-title">请选择</h3>
        <div class="py-4">
            <div id="selector-options" class="space-y-2">
                <!-- 选项将在这里显示 -->
            </div>
        </div>
        <div class="modal-action">
            <button class="btn btn-outline" onclick="closeSelectorModal()">取消</button>
            <button class="btn btn-primary" id="selector-confirm-button" onclick="confirmSelection()">确认</button>
        </div>
    </div>
</div>

<script>
// 模态框管理器
class ModalManager {
    constructor() {
        this.currentModal = null;
        this.confirmCallback = null;
        this.formCallback = null;
        this.selectorCallback = null;
        this.selectedOptions = new Set();
    }

    // 显示确认对话框
    showConfirm(title, message, callback, confirmText = '确认', confirmClass = 'btn-primary') {
        const modal = document.getElementById('confirm-modal');
        const titleElement = document.getElementById('confirm-title');
        const messageElement = document.getElementById('confirm-message');
        const confirmButton = document.getElementById('confirm-button');

        titleElement.textContent = title;
        messageElement.textContent = message;
        confirmButton.textContent = confirmText;
        confirmButton.className = `btn ${confirmClass}`;

        this.confirmCallback = callback;
        this.currentModal = 'confirm';
        modal.classList.add('modal-open');
    }

    // 显示信息对话框
    showInfo(title, content, isHtml = false) {
        const modal = document.getElementById('info-modal');
        const titleElement = document.getElementById('info-title');
        const contentElement = document.getElementById('info-content');

        titleElement.textContent = title;
        
        if (isHtml) {
            contentElement.innerHTML = content;
        } else {
            contentElement.textContent = content;
        }

        this.currentModal = 'info';
        modal.classList.add('modal-open');
    }

    // 显示表单对话框
    showForm(title, formHtml, callback, submitText = '提交') {
        const modal = document.getElementById('form-modal');
        const titleElement = document.getElementById('form-title');
        const contentElement = document.getElementById('form-content');
        const submitButton = document.getElementById('form-submit-button');

        titleElement.textContent = title;
        contentElement.innerHTML = formHtml;
        submitButton.textContent = submitText;

        this.formCallback = callback;
        this.currentModal = 'form';
        modal.classList.add('modal-open');
    }

    // 显示图片预览
    showImage(title, imageSrc, imageAlt = '预览图片') {
        const modal = document.getElementById('image-modal');
        const titleElement = document.getElementById('image-title');
        const imageElement = document.getElementById('image-preview');

        titleElement.textContent = title;
        imageElement.src = imageSrc;
        imageElement.alt = imageAlt;

        this.currentModal = 'image';
        modal.classList.add('modal-open');
    }

    // 显示详情对话框
    showDetail(title, content, isHtml = true) {
        const modal = document.getElementById('detail-modal');
        const titleElement = document.getElementById('detail-title');
        const contentElement = document.getElementById('detail-content');

        titleElement.textContent = title;
        
        if (isHtml) {
            contentElement.innerHTML = content;
        } else {
            contentElement.textContent = content;
        }

        this.currentModal = 'detail';
        modal.classList.add('modal-open');
    }

    // 显示选择器对话框
    showSelector(title, options, callback, multiple = false) {
        const modal = document.getElementById('selector-modal');
        const titleElement = document.getElementById('selector-title');
        const optionsElement = document.getElementById('selector-options');

        titleElement.textContent = title;
        this.selectedOptions.clear();

        // 生成选项
        optionsElement.innerHTML = options.map((option, index) => {
            const inputType = multiple ? 'checkbox' : 'radio';
            const inputName = multiple ? 'selector-option' : 'selector-option';
            
            return `
                <label class="cursor-pointer label justify-start space-x-3">
                    <input type="${inputType}" 
                           name="${inputName}" 
                           value="${option.value || index}"
                           class="${inputType} ${inputType}-primary"
                           onchange="updateSelection(this, ${multiple})">
                    <span class="label-text">${option.label || option}</span>
                </label>
            `;
        }).join('');

        this.selectorCallback = callback;
        this.currentModal = 'selector';
        modal.classList.add('modal-open');
    }

    // 关闭所有模态框
    closeAll() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.classList.remove('modal-open');
        });
        this.currentModal = null;
        this.confirmCallback = null;
        this.formCallback = null;
        this.selectorCallback = null;
        this.selectedOptions.clear();
    }

    // 关闭当前模态框
    closeCurrent() {
        if (this.currentModal) {
            const modal = document.getElementById(`${this.currentModal}-modal`);
            modal.classList.remove('modal-open');
            this.currentModal = null;
        }
    }
}

// 创建全局模态框管理器
window.modalManager = new ModalManager();

// 确认对话框相关函数
function closeConfirmModal() {
    modalManager.closeCurrent();
}

function confirmAction() {
    if (modalManager.confirmCallback) {
        modalManager.confirmCallback();
    }
    closeConfirmModal();
}

// 信息对话框相关函数
function closeInfoModal() {
    modalManager.closeCurrent();
}

// 表单对话框相关函数
function closeFormModal() {
    modalManager.closeCurrent();
}

function submitFormModal() {
    if (modalManager.formCallback) {
        const formData = new FormData();
        const inputs = document.querySelectorAll('#form-content input, #form-content select, #form-content textarea');
        
        inputs.forEach(input => {
            if (input.type === 'checkbox' || input.type === 'radio') {
                if (input.checked) {
                    formData.append(input.name, input.value);
                }
            } else {
                formData.append(input.name, input.value);
            }
        });

        modalManager.formCallback(formData);
    }
    closeFormModal();
}

// 图片预览相关函数
function closeImageModal() {
    modalManager.closeCurrent();
}

function downloadImage() {
    const img = document.getElementById('image-preview');
    const link = document.createElement('a');
    link.href = img.src;
    link.download = 'image.jpg';
    link.click();
}

// 详情对话框相关函数
function closeDetailModal() {
    modalManager.closeCurrent();
}

function printDetail() {
    const content = document.getElementById('detail-content').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>打印详情</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .no-print { display: none; }
                </style>
            </head>
            <body>
                ${content}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// 选择器对话框相关函数
function closeSelectorModal() {
    modalManager.closeCurrent();
}

function updateSelection(input, multiple) {
    if (multiple) {
        if (input.checked) {
            modalManager.selectedOptions.add(input.value);
        } else {
            modalManager.selectedOptions.delete(input.value);
        }
    } else {
        modalManager.selectedOptions.clear();
        if (input.checked) {
            modalManager.selectedOptions.add(input.value);
        }
    }
}

function confirmSelection() {
    if (modalManager.selectorCallback) {
        const selectedArray = Array.from(modalManager.selectedOptions);
        modalManager.selectorCallback(selectedArray);
    }
    closeSelectorModal();
}

// 便捷函数
function showConfirm(title, message, callback) {
    modalManager.showConfirm(title, message, callback);
}

function showInfo(title, content, isHtml = false) {
    modalManager.showInfo(title, content, isHtml);
}

function showForm(title, formHtml, callback) {
    modalManager.showForm(title, formHtml, callback);
}

function showImage(title, imageSrc) {
    modalManager.showImage(title, imageSrc);
}

function showDetail(title, content) {
    modalManager.showDetail(title, content);
}

function showSelector(title, options, callback, multiple = false) {
    modalManager.showSelector(title, options, callback, multiple);
}

// 键盘事件处理
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && modalManager.currentModal) {
        modalManager.closeCurrent();
    }
});

// 点击背景关闭模态框
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal') && modalManager.currentModal) {
        modalManager.closeCurrent();
    }
});

// 示例用法函数
function showDeleteConfirm(itemName, callback) {
    showConfirm(
        '确认删除',
        `确定要删除 "${itemName}" 吗？此操作不可撤销。`,
        callback
    );
}

function showSuccessInfo(message) {
    showInfo('操作成功', message);
}

function showErrorInfo(message) {
    showInfo('操作失败', message);
}

function showQuickForm(title, fields, callback) {
    const formHtml = fields.map(field => {
        switch (field.type) {
            case 'text':
            case 'email':
            case 'password':
            case 'number':
                return `
                    <div class="form-control-custom mb-4">
                        <label class="label">
                            <span class="label-text">${field.label}</span>
                        </label>
                        <input type="${field.type}" 
                               name="${field.name}" 
                               class="input input-bordered" 
                               placeholder="${field.placeholder || ''}"
                               ${field.required ? 'required' : ''}>
                    </div>
                `;
            case 'select':
                const options = field.options.map(opt => 
                    `<option value="${opt.value}">${opt.label}</option>`
                ).join('');
                return `
                    <div class="form-control-custom mb-4">
                        <label class="label">
                            <span class="label-text">${field.label}</span>
                        </label>
                        <select name="${field.name}" class="select select-bordered" ${field.required ? 'required' : ''}>
                            <option value="">请选择</option>
                            ${options}
                        </select>
                    </div>
                `;
            case 'textarea':
                return `
                    <div class="form-control-custom mb-4">
                        <label class="label">
                            <span class="label-text">${field.label}</span>
                        </label>
                        <textarea name="${field.name}" 
                                  class="textarea textarea-bordered" 
                                  placeholder="${field.placeholder || ''}"
                                  rows="${field.rows || 3}"
                                  ${field.required ? 'required' : ''}></textarea>
                    </div>
                `;
            default:
                return '';
        }
    }).join('');

    showForm(title, formHtml, callback);
}
</script>
