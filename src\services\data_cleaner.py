"""
数据清洗模块
实现麻醉方法标准化、数据格式清理等功能
"""
import re
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from utils.logger import logger


class AnesthesiaMethodCleaner:
    """麻醉方法清洗器"""
    
    def __init__(self):
        # 麻醉方法映射规则
        self.anesthesia_mapping = {
            '全身': '全身麻醉',
            '硬': '椎管内麻醉',
            '神经': '神经阻滞麻醉',
            '静脉': '静脉麻醉',
            '骶管': '椎管内麻醉',
            '局部': '局部麻醉',
            '椎管': '椎管内麻醉',
            '吸入': '全身麻醉',
            '静吸': '全身麻醉',
            '蛛网膜': '椎管内麻醉',
            '腰麻': '椎管内麻醉',
            '硬膜外': '椎管内麻醉',
            '腰硬联合': '椎管内麻醉',
            '臂丛': '神经阻滞麻醉',
            '坐骨': '神经阻滞麻醉',
            '股神经': '神经阻滞麻醉'
        }
        
        # 需要忽略的术语
        self.ignore_terms = ['超声', '表面', '引导', '监测']
    
    def remove_brackets_and_content(self, text: str) -> str:
        """移除括号及其内容"""
        if pd.isna(text):
            return ''
        
        text = str(text).strip()
        
        # 移除各种括号及其内容
        patterns = [
            r'\（.*?\)',   # 中文左括号 + 英文右括号
            r'\（.*?\）',   # 中文括号
            r'\(.*?\)',    # 英文括号
            r'\(.*?\）',   # 英文左括号 + 中文右括号
            r'\[.*?\]',    # 方括号
            r'\{.*?\}',    # 花括号
            r'【.*?】'      # 中文方括号
        ]
        
        for pattern in patterns:
            text = re.sub(pattern, '', text)
        
        return text.strip()
    
    def split_anesthesia_method(self, text: str) -> Tuple[str, str]:
        """分割麻醉方法为主要方法和复合方法"""
        if not text or pd.isna(text):
            return '', ''
        
        text = str(text).strip()
        
        # 分割符号
        split_patterns = [
            r'[+＋]',      # 加号
            r'[,，]',      # 逗号
            r'[;；]',      # 分号
            r'联合',       # 联合
            r'复合',       # 复合
            r'配合',       # 配合
            r'辅助'        # 辅助
        ]
        
        for pattern in split_patterns:
            parts = re.split(pattern, text, maxsplit=1)
            if len(parts) > 1:
                return parts[0].strip(), parts[1].strip()
        
        # 如果没有分割符，尝试按非字母数字字符分割
        parts = re.split(r'(\W+)', text, maxsplit=1)
        if len(parts) >= 3:  # 有分割符的情况
            return parts[0].strip(), ''.join(parts[2:]).strip()
        
        return text.strip(), ''
    
    def standardize_anesthesia_term(self, text: str) -> str:
        """标准化麻醉术语"""
        if not text or pd.isna(text):
            return ''
        
        text = str(text).strip()
        
        # 检查是否包含需要忽略的术语
        for ignore_term in self.ignore_terms:
            if ignore_term in text:
                return ''
        
        # 按优先级匹配麻醉方法
        for keyword, standard_term in self.anesthesia_mapping.items():
            if keyword in text:
                return standard_term
        
        # 如果没有匹配到，返回原文本（可能是已经标准化的）
        standard_methods = set(self.anesthesia_mapping.values())
        if text in standard_methods:
            return text
        
        # 其他情况返回空字符串
        return ''
    
    def clean_anesthesia_method(self, method: str) -> Dict[str, str]:
        """清洗单个麻醉方法"""
        # 1. 移除括号内容
        cleaned = self.remove_brackets_and_content(method)
        
        # 2. 分割主要和复合方法
        primary, compound = self.split_anesthesia_method(cleaned)
        
        # 3. 标准化术语
        primary_standardized = self.standardize_anesthesia_term(primary)
        compound_standardized = self.standardize_anesthesia_term(compound)
        
        return {
            'original': method,
            'cleaned': cleaned,
            'primary_anesthesia': primary_standardized,
            'compound_anesthesia': compound_standardized,
            'primary_raw': primary,
            'compound_raw': compound
        }


class DataFieldCleaner:
    """数据字段清洗器"""
    
    def __init__(self):
        self.phone_pattern = re.compile(r'^1[3-9]\d{9}$')
        self.id_card_pattern = re.compile(r'^\d{17}[\dXx]$')
    
    def clean_phone_number(self, phone: str) -> str:
        """清洗电话号码"""
        if pd.isna(phone):
            return ''
        
        phone = str(phone).strip()
        # 移除所有非数字字符
        phone = re.sub(r'\D', '', phone)
        
        # 验证手机号格式
        if self.phone_pattern.match(phone):
            return phone
        
        return ''
    
    def clean_id_card(self, id_card: str) -> str:
        """清洗身份证号"""
        if pd.isna(id_card):
            return ''
        
        id_card = str(id_card).strip().upper()
        # 移除空格和特殊字符
        id_card = re.sub(r'[\s\-]', '', id_card)
        
        # 验证身份证格式
        if self.id_card_pattern.match(id_card):
            return id_card
        
        return ''
    
    def clean_name(self, name: str) -> str:
        """清洗姓名"""
        if pd.isna(name):
            return ''
        
        name = str(name).strip()
        # 移除数字和特殊符号，保留中文、英文字母
        name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z\s]', '', name)
        # 移除多余空格
        name = re.sub(r'\s+', ' ', name).strip()
        
        return name
    
    def clean_age(self, age: Any) -> Optional[int]:
        """清洗年龄"""
        if pd.isna(age):
            return None
        
        # 尝试提取数字
        age_str = str(age)
        age_match = re.search(r'\d+', age_str)
        
        if age_match:
            age_num = int(age_match.group())
            # 年龄合理性检查
            if 0 <= age_num <= 150:
                return age_num
        
        return None
    
    def clean_gender(self, gender: str) -> str:
        """清洗性别"""
        if pd.isna(gender):
            return ''
        
        gender = str(gender).strip()
        
        # 性别映射
        male_terms = ['男', 'M', 'Male', '1']
        female_terms = ['女', 'F', 'Female', '2']
        
        if gender in male_terms:
            return '男'
        elif gender in female_terms:
            return '女'
        
        return ''
    
    def clean_date(self, date_str: str) -> str:
        """清洗日期格式"""
        if pd.isna(date_str):
            return ''
        
        date_str = str(date_str).strip()
        
        # 常见日期格式模式
        date_patterns = [
            r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})',  # YYYY-MM-DD 或 YYYY/MM/DD
            r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})',  # MM/DD/YYYY 或 DD/MM/YYYY
            r'(\d{4})(\d{2})(\d{2})',              # YYYYMMDD
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                groups = match.groups()
                if len(groups[0]) == 4:  # 第一个是年份
                    year, month, day = groups
                else:  # 最后一个是年份
                    month, day, year = groups
                
                try:
                    # 验证日期有效性
                    pd.to_datetime(f"{year}-{month.zfill(2)}-{day.zfill(2)}")
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                except:
                    continue
        
        return ''


class DataCleaner:
    """数据清洗主类"""
    
    def __init__(self):
        self.anesthesia_cleaner = AnesthesiaMethodCleaner()
        self.field_cleaner = DataFieldCleaner()
        logger.info("数据清洗器初始化完成")
    
    def clean_dataframe(self, df: pd.DataFrame, field_mapping: Dict[str, str] = None) -> pd.DataFrame:
        """清洗整个DataFrame"""
        logger.info(f"开始清洗数据，共 {len(df)} 行")
        
        # 创建副本避免修改原数据
        cleaned_df = df.copy()
        
        # 默认字段映射
        if field_mapping is None:
            field_mapping = {
                '麻醉方法': 'anesthesia_method',
                '患者姓名': 'patient_name',
                '年龄': 'age',
                '性别': 'gender',
                '联系电话': 'phone',
                '身份证号': 'id_card',
                '手术日期': 'surgery_date'
            }
        
        # 清洗各个字段
        for original_field, clean_field in field_mapping.items():
            if original_field in cleaned_df.columns:
                if clean_field == 'anesthesia_method':
                    self._clean_anesthesia_column(cleaned_df, original_field)
                elif clean_field == 'patient_name':
                    cleaned_df[f'{original_field}_cleaned'] = cleaned_df[original_field].apply(
                        self.field_cleaner.clean_name
                    )
                elif clean_field == 'age':
                    cleaned_df[f'{original_field}_cleaned'] = cleaned_df[original_field].apply(
                        self.field_cleaner.clean_age
                    )
                elif clean_field == 'gender':
                    cleaned_df[f'{original_field}_cleaned'] = cleaned_df[original_field].apply(
                        self.field_cleaner.clean_gender
                    )
                elif clean_field == 'phone':
                    cleaned_df[f'{original_field}_cleaned'] = cleaned_df[original_field].apply(
                        self.field_cleaner.clean_phone_number
                    )
                elif clean_field == 'id_card':
                    cleaned_df[f'{original_field}_cleaned'] = cleaned_df[original_field].apply(
                        self.field_cleaner.clean_id_card
                    )
                elif clean_field == 'surgery_date':
                    cleaned_df[f'{original_field}_cleaned'] = cleaned_df[original_field].apply(
                        self.field_cleaner.clean_date
                    )
        
        logger.info("数据清洗完成")
        return cleaned_df
    
    def _clean_anesthesia_column(self, df: pd.DataFrame, column_name: str):
        """清洗麻醉方法列"""
        logger.info(f"清洗麻醉方法列: {column_name}")
        
        # 应用麻醉方法清洗
        anesthesia_results = df[column_name].apply(self.anesthesia_cleaner.clean_anesthesia_method)
        
        # 展开结果到多个列
        df[f'{column_name}_cleaned'] = anesthesia_results.apply(lambda x: x['cleaned'])
        df[f'{column_name}_primary'] = anesthesia_results.apply(lambda x: x['primary_anesthesia'])
        df[f'{column_name}_compound'] = anesthesia_results.apply(lambda x: x['compound_anesthesia'])
        df[f'{column_name}_primary_raw'] = anesthesia_results.apply(lambda x: x['primary_raw'])
        df[f'{column_name}_compound_raw'] = anesthesia_results.apply(lambda x: x['compound_raw'])
    
    def get_cleaning_summary(self, original_df: pd.DataFrame, cleaned_df: pd.DataFrame) -> Dict[str, Any]:
        """获取清洗摘要"""
        summary = {
            'total_rows': len(original_df),
            'cleaned_rows': len(cleaned_df),
            'fields_cleaned': [],
            'anesthesia_stats': {}
        }
        
        # 统计清洗的字段
        for col in cleaned_df.columns:
            if col.endswith('_cleaned') or col.endswith('_primary') or col.endswith('_compound'):
                summary['fields_cleaned'].append(col)
        
        # 麻醉方法统计
        if '麻醉方法_primary' in cleaned_df.columns:
            anesthesia_counts = cleaned_df['麻醉方法_primary'].value_counts()
            summary['anesthesia_stats'] = anesthesia_counts.to_dict()
        
        return summary
