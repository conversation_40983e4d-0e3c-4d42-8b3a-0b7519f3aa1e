"""
缓存工具模块
"""
import time
import hashlib
import json
from functools import wraps
from typing import Any, Dict, Optional
from utils.logger import logger


class MemoryCache:
    """内存缓存类"""
    
    def __init__(self, default_ttl: int = 300):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
    
    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = {
            'args': args,
            'kwargs': kwargs
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self.cache:
            entry = self.cache[key]
            if time.time() < entry['expires_at']:
                logger.debug(f"缓存命中: {key}")
                return entry['value']
            else:
                # 缓存过期，删除
                del self.cache[key]
                logger.debug(f"缓存过期: {key}")
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        self.cache[key] = {
            'value': value,
            'expires_at': time.time() + ttl,
            'created_at': time.time()
        }
        logger.debug(f"缓存设置: {key}, TTL: {ttl}s")
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        if key in self.cache:
            del self.cache[key]
            logger.debug(f"缓存删除: {key}")
            return True
        return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        count = len(self.cache)
        self.cache.clear()
        logger.info(f"清空缓存: {count} 个条目")
    
    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time >= entry['expires_at']
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.info(f"清理过期缓存: {len(expired_keys)} 个条目")
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        active_count = 0
        expired_count = 0
        
        for entry in self.cache.values():
            if current_time < entry['expires_at']:
                active_count += 1
            else:
                expired_count += 1
        
        return {
            'total_entries': len(self.cache),
            'active_entries': active_count,
            'expired_entries': expired_count,
            'memory_usage_mb': self._estimate_memory_usage()
        }
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        try:
            import sys
            total_size = sys.getsizeof(self.cache)
            for key, entry in self.cache.items():
                total_size += sys.getsizeof(key)
                total_size += sys.getsizeof(entry)
                total_size += sys.getsizeof(entry['value'])
            return total_size / (1024 * 1024)
        except:
            return 0.0


# 全局缓存实例
cache = MemoryCache(default_ttl=300)  # 5分钟默认TTL


def cached(ttl: int = 300, key_prefix: str = ""):
    """
    缓存装饰器
    
    Args:
        ttl: 缓存时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            func_name = f"{key_prefix}{func.__name__}" if key_prefix else func.__name__
            cache_key = f"{func_name}:{cache._generate_key(*args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            try:
                result = func(*args, **kwargs)
                cache.set(cache_key, result, ttl)
                return result
            except Exception as e:
                logger.error(f"函数执行失败: {func.__name__}, 错误: {e}")
                raise
        
        # 添加缓存控制方法
        wrapper.cache_clear = lambda: cache.clear()
        wrapper.cache_delete = lambda *args, **kwargs: cache.delete(
            f"{func_name}:{cache._generate_key(*args, **kwargs)}"
        )
        
        return wrapper
    return decorator


def cache_invalidate_pattern(pattern: str):
    """
    根据模式删除缓存
    
    Args:
        pattern: 缓存键模式（支持通配符*）
    """
    import fnmatch
    
    keys_to_delete = [
        key for key in cache.cache.keys()
        if fnmatch.fnmatch(key, pattern)
    ]
    
    for key in keys_to_delete:
        cache.delete(key)
    
    logger.info(f"根据模式 '{pattern}' 删除了 {len(keys_to_delete)} 个缓存条目")
    return len(keys_to_delete)


def warm_up_cache():
    """预热缓存"""
    logger.info("开始预热缓存...")
    
    try:
        # 这里可以添加预热逻辑
        # 例如：预加载常用的统计数据
        pass
    except Exception as e:
        logger.error(f"缓存预热失败: {e}")


def cache_middleware():
    """缓存中间件，定期清理过期缓存"""
    import threading
    import time
    
    def cleanup_worker():
        while True:
            try:
                time.sleep(60)  # 每分钟清理一次
                cache.cleanup_expired()
            except Exception as e:
                logger.error(f"缓存清理失败: {e}")
    
    thread = threading.Thread(target=cleanup_worker, daemon=True)
    thread.start()
    logger.info("缓存清理中间件已启动")


# 启动缓存中间件
cache_middleware()
