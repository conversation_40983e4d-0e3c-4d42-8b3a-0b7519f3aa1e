#!/usr/bin/env python3
"""
运行数据导入功能测试
"""

import sys
import subprocess
import time
import threading
from pathlib import Path

def start_server():
    """启动Flask服务器"""
    try:
        # 切换到项目目录
        project_root = Path(__file__).parent
        
        # 启动服务器
        process = subprocess.Popen([
            sys.executable, 'start.py'
        ], cwd=project_root, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        return process
    except Exception as e:
        print(f"启动服务器失败: {e}")
        return None

def run_tests():
    """运行测试"""
    try:
        # 等待服务器启动
        print("等待服务器启动...")
        time.sleep(5)
        
        # 运行测试
        result = subprocess.run([sys.executable, 'test_import_functionality.py'], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"运行测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动数据导入功能测试")
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        print("❌ 无法启动服务器")
        return False
    
    try:
        # 运行测试
        success = run_tests()
        
        if success:
            print("✅ 所有测试通过")
        else:
            print("❌ 测试失败")
        
        return success
        
    finally:
        # 停止服务器
        if server_process:
            server_process.terminate()
            server_process.wait()
            print("🛑 服务器已停止")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
