<!-- 左侧导航栏组件 - 可收缩侧边栏 -->
<!-- Logo区域 - 与标题栏保持完全一致的高度和样式 -->
<div class="h-16 flex items-center border-b border-base-300 bg-primary px-6 min-h-[4rem]">
    <div class="flex items-center space-x-3 w-full">
        <div class="w-8 h-8 bg-primary-content rounded-lg flex items-center justify-center menu-icon flex-shrink-0">
            <i class="fas fa-heartbeat text-primary text-lg"></i>
        </div>
        <span class="text-xl font-bold text-primary-content logo-text menu-text truncate">麻醉质控系统</span>
    </div>
</div>
<!-- 导航菜单 -->
<ul class="menu p-4 w-full min-h-full bg-base-200 text-base-content flex-1 overflow-y-auto space-y-1">
    <!-- 仪表盘 -->
    <li>
        <a href="/dashboard"
           class="{% if request.endpoint == 'dashboard' %}bg-primary text-primary-content font-semibold{% else %}hover:bg-primary hover:text-primary-content{% endif %} text-base font-medium py-3 px-4 rounded-xl transition-all duration-200 group flex items-center"
           onclick="router.navigate('/dashboard'); return false;">
            <div class="w-6 h-6 flex items-center justify-center menu-icon flex-shrink-0">
                <i class="fas fa-tachometer-alt text-lg"></i>
            </div>
            <span class="menu-text ml-3 text-base">仪表盘</span>
        </a>
    </li>

    <!-- 数据管理 -->
    <li>
        <details {% if request.endpoint in ['data_import', 'import_history'] %}open{% endif %} class="group">
            <summary class="text-base font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:bg-base-300 cursor-pointer flex items-center">
                <div class="w-6 h-6 flex items-center justify-center menu-icon flex-shrink-0">
                    <i class="fas fa-database text-lg"></i>
                </div>
                <span class="menu-text ml-3 text-base">数据管理</span>
            </summary>
            <ul class="ml-6 mt-2 space-y-1">
                <li>
                    <a href="/data-import"
                       class="{% if request.endpoint == 'data_import' %}bg-primary text-primary-content font-semibold{% else %}hover:bg-primary hover:text-primary-content{% endif %} text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center"
                       onclick="router.navigate('/data-import'); return false;">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <span class="menu-text ml-3">数据导入</span>
                    </a>
                </li>
                <li>
                    <a href="/import-history"
                       class="{% if request.endpoint == 'import_history' %}bg-primary text-primary-content font-semibold{% else %}hover:bg-primary hover:text-primary-content{% endif %} text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center"
                       onclick="router.navigate('/import-history'); return false;">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <span class="menu-text ml-3">导入历史</span>
                    </a>
                </li>
            </ul>
        </details>
    </li>

    <!-- 麻醉列表 -->
    <li>
        <a href="/anesthesia-list"
           class="{% if request.endpoint == 'anesthesia_list' %}bg-primary text-primary-content font-semibold{% else %}hover:bg-primary hover:text-primary-content{% endif %} text-base font-medium py-3 px-4 rounded-xl transition-all duration-200 flex items-center"
           onclick="router.navigate('/anesthesia-list'); return false;">
            <div class="w-6 h-6 flex items-center justify-center menu-icon flex-shrink-0">
                <i class="fas fa-procedures text-lg"></i>
            </div>
            <span class="menu-text ml-3 text-base">麻醉列表</span>
        </a>
    </li>

    <!-- 统计分析 -->
    <li>
        <a href="/statistics"
           class="{% if request.endpoint == 'statistics' %}bg-primary text-primary-content font-semibold{% else %}hover:bg-primary hover:text-primary-content{% endif %} text-base font-medium py-3 px-4 rounded-xl transition-all duration-200 group flex items-center"
           onclick="router.navigate('/statistics'); return false;">
            <div class="w-6 h-6 flex items-center justify-center menu-icon flex-shrink-0">
                <i class="fas fa-chart-bar text-lg"></i>
            </div>
            <span class="menu-text ml-3 text-base">统计分析</span>
        </a>
    </li>

    <!-- 质控管理 -->
    <li>
        <details class="group">
            <summary class="text-base font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:bg-base-300 cursor-pointer flex items-center">
                <div class="w-6 h-6 flex items-center justify-center menu-icon flex-shrink-0">
                    <i class="fas fa-shield-alt text-lg"></i>
                </div>
                <span class="menu-text ml-3 text-base">质控管理</span>
            </summary>
            <ul class="ml-6 mt-2 space-y-1">
                <li>
                    <a href="#" class="text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 hover:bg-primary hover:text-primary-content flex items-center">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <span class="menu-text ml-3">质控指标</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 hover:bg-primary hover:text-primary-content flex items-center">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <span class="menu-text ml-3">异常事件</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 hover:bg-primary hover:text-primary-content flex items-center">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-file-medical-alt"></i>
                        </div>
                        <span class="menu-text ml-3">质控报告</span>
                    </a>
                </li>
            </ul>
        </details>
    </li>

    <!-- 系统管理 -->
    <li>
        <details {% if request.endpoint in ['system_settings'] %}open{% endif %} class="group">
            <summary class="text-base font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:bg-base-300 cursor-pointer flex items-center">
                <div class="w-6 h-6 flex items-center justify-center menu-icon flex-shrink-0">
                    <i class="fas fa-cogs text-lg"></i>
                </div>
                <span class="menu-text ml-3 text-base">系统管理</span>
            </summary>
            <ul class="ml-6 mt-2 space-y-1">
                <li>
                    <a href="/system-settings"
                       class="{% if request.endpoint == 'system_settings' %}bg-primary text-primary-content font-semibold{% else %}hover:bg-primary hover:text-primary-content{% endif %} text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center"
                       onclick="router.navigate('/system-settings'); return false;">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="menu-text ml-3">系统设置</span>
                    </a>
                </li>
                <li>
                    <a href="/theme-settings"
                       class="{% if request.endpoint == 'theme_settings' %}bg-primary text-primary-content font-semibold{% else %}hover:bg-primary hover:text-primary-content{% endif %} text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center"
                       onclick="router.navigate('/theme-settings'); return false;">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <span class="menu-text ml-3">主题设置</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 hover:bg-primary hover:text-primary-content flex items-center">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <span class="menu-text ml-3">用户管理</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="text-sm font-medium py-3 px-4 rounded-lg transition-all duration-200 hover:bg-primary hover:text-primary-content flex items-center">
                        <div class="w-5 h-5 flex items-center justify-center menu-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <span class="menu-text ml-3">权限管理</span>
                    </a>
                </li>
            </ul>
        </details>
    </li>

    <!-- 分隔线 -->
    <li class="mt-6 mb-4">
        <div class="border-t border-base-300"></div>
    </li>

    <!-- 测试页面 -->
    <li>
        <a href="/test-daisyui"
           class="{% if request.endpoint == 'test_daisyui' %}bg-accent text-accent-content font-semibold{% else %}hover:bg-accent hover:text-accent-content{% endif %} text-base font-medium py-3 px-4 rounded-xl transition-all duration-200 group flex items-center"
           onclick="router.navigate('/test-daisyui'); return false;">
            <div class="w-6 h-6 flex items-center justify-center menu-icon flex-shrink-0">
                <i class="fas fa-flask text-lg"></i>
            </div>
            <span class="menu-text ml-3 text-base">组件测试</span>
        </a>
    </li>
</ul>
