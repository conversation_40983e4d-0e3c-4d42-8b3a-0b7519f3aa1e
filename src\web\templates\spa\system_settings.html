<!-- SPA版本的系统设置内容 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">系统设置</h1>
            <p class="text-base-content/60 mt-1">配置系统参数和选项</p>
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline btn-sm" onclick="resetToDefaults()">
                <i class="fas fa-undo mr-2"></i>
                恢复默认
            </button>
            <button class="btn btn-primary btn-sm" onclick="saveAllSettings()">
                <i class="fas fa-save mr-2"></i>
                保存设置
            </button>
        </div>
    </div>

    <!-- 设置选项卡 -->
    <div class="tabs tabs-boxed">
        <a class="tab tab-active" onclick="showTab('general')">常规设置</a>
        <a class="tab" onclick="showTab('data')">数据设置</a>
        <a class="tab" onclick="showTab('security')">安全设置</a>
        <a class="tab" onclick="showTab('notification')">通知设置</a>
    </div>

    <!-- 常规设置 -->
    <div id="general-tab" class="tab-content">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body space-y-6">
                <h3 class="card-title">常规设置</h3>
                <p class="text-sm text-base-content/60 mb-6">系统基本配置选项</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">系统名称</span>
                        </label>
                        <input type="text" class="input input-bordered" value="麻醉质控数据管理系统">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">默认主题</span>
                        </label>
                        <select class="select select-bordered">
                            <option value="medical">医疗主题</option>
                            <option value="light">浅色主题</option>
                            <option value="dark">深色主题</option>
                        </select>
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">时区</span>
                        </label>
                        <select class="select select-bordered">
                            <option value="Asia/Shanghai">北京时间 (UTC+8)</option>
                            <option value="UTC">协调世界时 (UTC)</option>
                        </select>
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">语言</span>
                        </label>
                        <select class="select select-bordered">
                            <option value="zh-CN">简体中文</option>
                            <option value="en-US">English</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-control">
                    <label class="cursor-pointer label">
                        <span class="label-text">启用自动保存</span>
                        <input type="checkbox" class="toggle toggle-primary" checked>
                    </label>
                </div>
                
                <div class="form-control">
                    <label class="cursor-pointer label">
                        <span class="label-text">显示操作提示</span>
                        <input type="checkbox" class="toggle toggle-primary" checked>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据设置 -->
    <div id="data-tab" class="tab-content hidden">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body space-y-6">
                <h3 class="card-title">数据设置</h3>
                <p class="text-sm text-base-content/60 mb-6">数据处理和存储相关配置</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">数据保留期限（天）</span>
                        </label>
                        <input type="number" class="input input-bordered" value="365" min="30" max="3650">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">备份频率</span>
                        </label>
                        <select class="select select-bordered">
                            <option value="daily">每日</option>
                            <option value="weekly">每周</option>
                            <option value="monthly">每月</option>
                        </select>
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">最大文件上传大小（MB）</span>
                        </label>
                        <input type="number" class="input input-bordered" value="50" min="1" max="500">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">数据导出格式</span>
                        </label>
                        <select class="select select-bordered">
                            <option value="xlsx">Excel (.xlsx)</option>
                            <option value="csv">CSV</option>
                            <option value="json">JSON</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-control">
                    <label class="cursor-pointer label">
                        <span class="label-text">启用数据脱敏</span>
                        <input type="checkbox" class="toggle toggle-primary" checked>
                    </label>
                </div>
                
                <div class="form-control">
                    <label class="cursor-pointer label">
                        <span class="label-text">自动数据验证</span>
                        <input type="checkbox" class="toggle toggle-primary" checked>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- 安全设置 -->
    <div id="security-tab" class="tab-content hidden">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body space-y-6">
                <h3 class="card-title">安全设置</h3>
                <p class="text-sm text-base-content/60 mb-6">系统安全和隐私保护配置</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">会话超时时间（分钟）</span>
                        </label>
                        <input type="number" class="input input-bordered" value="30" min="5" max="480">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">密码最小长度</span>
                        </label>
                        <input type="number" class="input input-bordered" value="8" min="6" max="20">
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="form-control">
                        <label class="cursor-pointer label">
                            <span class="label-text">启用双因素认证</span>
                            <input type="checkbox" class="toggle toggle-primary">
                        </label>
                    </div>
                    
                    <div class="form-control">
                        <label class="cursor-pointer label">
                            <span class="label-text">记录操作日志</span>
                            <input type="checkbox" class="toggle toggle-primary" checked>
                        </label>
                    </div>
                    
                    <div class="form-control">
                        <label class="cursor-pointer label">
                            <span class="label-text">启用IP白名单</span>
                            <input type="checkbox" class="toggle toggle-primary">
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知设置 -->
    <div id="notification-tab" class="tab-content hidden">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body space-y-6">
                <h3 class="card-title">通知设置</h3>
                <p class="text-sm text-base-content/60 mb-6">系统通知和提醒配置</p>
                
                <div class="space-y-4">
                    <div class="form-control">
                        <label class="cursor-pointer label">
                            <span class="label-text">启用邮件通知</span>
                            <input type="checkbox" class="toggle toggle-primary" checked>
                        </label>
                    </div>
                    
                    <div class="form-control">
                        <label class="cursor-pointer label">
                            <span class="label-text">启用短信通知</span>
                            <input type="checkbox" class="toggle toggle-primary">
                        </label>
                    </div>
                    
                    <div class="form-control">
                        <label class="cursor-pointer label">
                            <span class="label-text">数据导入完成通知</span>
                            <input type="checkbox" class="toggle toggle-primary" checked>
                        </label>
                    </div>
                    
                    <div class="form-control">
                        <label class="cursor-pointer label">
                            <span class="label-text">系统异常通知</span>
                            <input type="checkbox" class="toggle toggle-primary" checked>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // 隐藏所有选项卡内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // 移除所有选项卡的active状态
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('tab-active');
    });
    
    // 显示选中的选项卡内容
    document.getElementById(tabName + '-tab').classList.remove('hidden');
    
    // 添加选中选项卡的active状态
    event.target.classList.add('tab-active');
}

function saveAllSettings() {
    // 模拟保存设置
    window.utils.showNotification('设置保存中...', 'info');
    
    setTimeout(() => {
        window.utils.showNotification('设置保存成功', 'success');
    }, 1000);
}

function resetToDefaults() {
    if (confirm('确定要恢复所有设置到默认值吗？此操作不可撤销。')) {
        window.utils.showNotification('设置已恢复到默认值', 'success');
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('系统设置页面加载完成');
});
</script>
