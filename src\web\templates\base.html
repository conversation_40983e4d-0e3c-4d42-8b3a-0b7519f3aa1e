<!DOCTYPE html>
<html lang="zh-CN" data-theme="corporate">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}麻醉质控数据管理系统{% endblock %}</title>
    
    <!-- 离线 DaisyUI CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/daisyui.min.css') }}">

    <!-- 离线 Font Awesome 图标 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome-complete.min.css') }}">

    <!-- 离线 Inter 字体 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/inter-font.css') }}">

    <!-- 离线 Tailwind CSS JavaScript -->
    <script src="{{ url_for('static', filename='js/tailwindcss.min.js') }}"></script>

    <!-- SheetJS 库 (用于Excel文件处理) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Socket.IO 库 (用于实时通信) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>

    <!-- Tailwind 配置 -->
    <script>
        // 配置 Tailwind CSS (离线版本)
        if (typeof tailwind !== 'undefined') {
            tailwind.config = {
                theme: {
                    extend: {
                        fontFamily: {
                            'sans': ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
                        },
                        colors: {
                            primary: {
                                50: '#f0f9ff',
                                500: '#3b82f6',
                                600: '#2563eb',
                                700: '#1d4ed8',
                            }
                        }
                    }
                },
                daisyui: {
                    themes: [
                        "light",
                        "dark",
                        "cupcake",
                        "bumblebee",
                        "emerald",
                        "corporate",
                        "synthwave",
                        "retro",
                        "cyberpunk",
                        "valentine",
                        "halloween",
                        "garden",
                        "forest",
                        "aqua",
                        "lofi",
                        "pastel",
                        "fantasy",
                        "wireframe",
                        "black",
                        "luxury",
                        "dracula",
                        "cmyk",
                        "autumn",
                        "business",
                        "acid",
                        "lemonade",
                        "night",
                        "coffee",
                        "winter"
                    ],
                    darkTheme: "dark",
                    base: true,
                    styled: true,
                    utils: true,
                    rtl: false,
                    prefix: "",
                    logs: false,
                }
            }
        }
    </script>

    
    <!-- 图标库 (使用内置 Unicode 图标，无需外部依赖) -->
    
    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
    
    <!-- 额外的头部内容 -->
    {% block head %}{% endblock %}
</head>
<body class="bg-base-100">
    <!-- 主布局容器 - 可收缩侧边栏 -->
    <div class="app-container flex h-screen overflow-hidden">
        <!-- 侧边栏 -->
        <aside id="sidebar" class="sidebar bg-base-200 transition-all duration-300 ease-in-out z-40 flex flex-col">
            {% include 'components/navigation.html' %}
        </aside>

        <!-- 主内容区域 -->
        <div class="main-content flex flex-col flex-1 overflow-hidden">
            <!-- 顶部标题栏组件 -->
            {% include 'components/header.html' %}

            <!-- 内容区域 -->
            <main id="main-content" class="content-area flex-1 p-6 bg-base-50 overflow-auto">
                {% if spa_template %}
                    {% include 'spa/' + spa_template %}
                {% else %}
                    {% block content %}{% endblock %}
                {% endif %}
            </main>
        </div>

        <!-- 移动端遮罩层 -->
        <div id="sidebar-overlay" class="sidebar-overlay fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden hidden" onclick="toggleSidebar()"></div>
    </div>

    <!-- 通用组件 -->
    {% include 'components/loading.html' %}
    {% include 'components/modal.html' %}
    {% include 'components/notification.html' %}

    <!-- 页面加载指示器 -->
    <div id="page-loading" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-base-100 rounded-lg p-6 shadow-xl">
            <div class="flex items-center space-x-3">
                <div class="loading loading-spinner loading-md"></div>
                <span class="text-base-content">页面加载中...</span>
            </div>
        </div>
    </div>

    <!-- 基础 JavaScript 功能 -->
    <script>
        // SPA路由管理器
        class SPARouter {
            constructor() {
                this.routes = {};
                this.currentRoute = null;
                this.contentContainer = document.getElementById('main-content');
                this.init();
            }

            init() {
                // 监听浏览器前进后退
                window.addEventListener('popstate', (e) => {
                    if (e.state && e.state.route) {
                        this.loadRoute(e.state.route, false);
                    }
                });

                // 设置路由映射
                this.setupRoutes();
            }

            setupRoutes() {
                this.routes = {
                    '/': {
                        title: '仪表盘',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' }
                        ]
                    },
                    '/dashboard': {
                        title: '仪表盘',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' }
                        ]
                    },
                    '/data-import': {
                        title: '数据导入',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' },
                            { text: '数据管理', url: '#', icon: 'fas fa-database' },
                            { text: '数据导入', url: '/data-import', icon: 'fas fa-upload' }
                        ]
                    },
                    '/import-history': {
                        title: '导入历史',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' },
                            { text: '数据管理', url: '#', icon: 'fas fa-database' },
                            { text: '导入历史', url: '/import-history', icon: 'fas fa-history' }
                        ]
                    },
                    '/anesthesia-list': {
                        title: '麻醉列表',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' },
                            { text: '麻醉列表', url: '/anesthesia-list', icon: 'fas fa-procedures' }
                        ]
                    },
                    '/statistics': {
                        title: '统计分析',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' },
                            { text: '统计分析', url: '/statistics', icon: 'fas fa-chart-bar' }
                        ]
                    },
                    '/system-settings': {
                        title: '系统设置',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' },
                            { text: '系统管理', url: '#', icon: 'fas fa-cogs' },
                            { text: '系统设置', url: '/system-settings', icon: 'fas fa-cog' }
                        ]
                    },
                    '/theme-settings': {
                        title: '主题设置',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' },
                            { text: '系统管理', url: '#', icon: 'fas fa-cogs' },
                            { text: '主题设置', url: '/theme-settings', icon: 'fas fa-palette' }
                        ]
                    },
                    '/test-daisyui': {
                        title: 'DaisyUI测试',
                        breadcrumbs: [
                            { text: '首页', url: '/dashboard', icon: 'fas fa-home' },
                            { text: 'DaisyUI测试', url: '/test-daisyui', icon: 'fas fa-flask' }
                        ]
                    }
                };
            }

            async loadRoute(path, pushState = true) {
                try {
                    // 显示加载状态
                    this.showLoading();

                    // 获取内容
                    const response = await fetch(path + '?spa=1', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });

                    if (!response.ok) {
                        throw new Error('页面加载失败');
                    }

                    const html = await response.text();

                    // 更新内容
                    this.contentContainer.innerHTML = html;

                    // 调用页面特定的初始化函数
                    this.initializePage(path);

                    // 更新浏览器历史
                    if (pushState) {
                        history.pushState({ route: path }, '', path);
                    }

                    // 更新页面信息
                    this.updatePageInfo(path);
                    this.updateNavigation(path);
                    this.updateBreadcrumbs(path);
                    this.executePageScripts();
                    this.hideLoading();

                    this.currentRoute = path;

                } catch (error) {
                    console.error('路由加载失败:', error);
                    this.showError('页面加载失败，请重试');
                    this.hideLoading();
                }
            }

            updatePageInfo(path) {
                const route = this.routes[path];
                if (route) {
                    document.title = `${route.title} - 麻醉质控数据管理系统`;
                }
            }

            updateNavigation(path) {
                // 更新导航状态 - 移除所有active状态
                document.querySelectorAll('.menu a').forEach(link => {
                    link.classList.remove('bg-primary', 'text-primary-content', 'font-semibold');
                    link.classList.add('hover:bg-primary', 'hover:text-primary-content');
                });

                // 设置当前页面的active状态
                const currentLink = document.querySelector(`.menu a[href="${path}"]`);
                if (currentLink) {
                    currentLink.classList.remove('hover:bg-primary', 'hover:text-primary-content');
                    currentLink.classList.add('bg-primary', 'text-primary-content', 'font-semibold');

                    // 展开父级菜单
                    const parentDetails = currentLink.closest('details');
                    if (parentDetails) {
                        parentDetails.open = true;
                    }
                }
            }

            updateBreadcrumbs(path) {
                let route = this.routes[path];

                // 如果没有找到精确匹配，尝试匹配动态路由
                if (!route) {
                    route = this.matchDynamicRoute(path);
                }

                if (!route || !route.breadcrumbs) return;

                const breadcrumbContainer = document.querySelector('.breadcrumbs ol');
                if (!breadcrumbContainer) return;

                // 清空现有面包屑
                breadcrumbContainer.innerHTML = '';

                // 生成新的面包屑
                route.breadcrumbs.forEach((crumb, index) => {
                    const li = document.createElement('li');

                    if (index === route.breadcrumbs.length - 1) {
                        // 最后一个面包屑（当前页面）
                        li.innerHTML = `
                            <span class="text-base-content/60 flex items-center">
                                <i class="${crumb.icon} mr-1"></i>${crumb.text}
                            </span>
                        `;
                    } else {
                        // 可点击的面包屑
                        const isClickable = crumb.url !== '#';
                        if (isClickable) {
                            li.innerHTML = `
                                <a href="${crumb.url}"
                                   class="link link-hover text-primary flex items-center"
                                   onclick="router.navigate('${crumb.url}'); return false;">
                                    <i class="${crumb.icon} mr-1"></i>${crumb.text}
                                </a>
                            `;
                        } else {
                            li.innerHTML = `
                                <span class="text-base-content/80 flex items-center">
                                    <i class="${crumb.icon} mr-1"></i>${crumb.text}
                                </span>
                            `;
                        }
                    }

                    breadcrumbContainer.appendChild(li);
                });
            }

            matchDynamicRoute(path) {
                // 暂无动态路由
                return null;
            }

            executePageScripts() {
                const scripts = this.contentContainer.querySelectorAll('script');
                scripts.forEach(script => {
                    try {
                        if (script.src) {
                            // 外部脚本，检查是否已加载
                            const existingScript = document.querySelector(`script[src="${script.src}"]`);
                            if (!existingScript) {
                                const newScript = document.createElement('script');
                                newScript.src = script.src;
                                document.head.appendChild(newScript);
                            }
                        } else {
                            // 内联脚本，使用安全的执行方式
                            const scriptContent = script.textContent;
                            if (scriptContent.trim()) {
                                try {
                                    // 直接执行脚本，但捕获重复声明错误
                                    eval(scriptContent);
                                } catch (error) {
                                    // 忽略重复声明错误，但记录其他错误
                                    if (error.name === 'SyntaxError' && error.message.includes('already been declared')) {
                                        console.log('忽略重复声明错误:', error.message);
                                    } else {
                                        console.error('页面脚本执行错误:', error);
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error('页面脚本执行错误:', error);
                    }
                });
            }

            // 页面特定的初始化函数
            initializePage(path) {
                console.log('🔄 初始化页面:', path);

                // 等待DOM更新完成
                setTimeout(() => {
                    if (path === '/anesthesia-list' && typeof window.initAnesthesiaList === 'function') {
                        console.log('🚀 调用麻醉列表初始化函数');
                        window.initAnesthesiaList();
                    } else if (path === '/data-import' && typeof window.initDataImport === 'function') {
                        console.log('🚀 调用数据导入初始化函数');
                        window.initDataImport();
                    } else if (path === '/import-history' && typeof window.initImportHistory === 'function') {
                        console.log('🚀 调用导入历史初始化函数');
                        window.initImportHistory();
                    }
                    // 可以继续添加其他页面的初始化函数
                }, 50);
            }

            showLoading() {
                const loadingEl = document.getElementById('page-loading');
                if (loadingEl) {
                    loadingEl.classList.remove('hidden');
                }
            }

            hideLoading() {
                const loadingEl = document.getElementById('page-loading');
                if (loadingEl) {
                    loadingEl.classList.add('hidden');
                }
            }

            showError(message) {
                if (window.utils) {
                    window.utils.showNotification(message, 'error');
                } else {
                    alert(message);
                }
            }

            navigate(path) {
                this.loadRoute(path);
            }
        }

        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.overlay = document.getElementById('sidebar-overlay');
                this.isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
                this.isMobile = window.innerWidth < 1024;
                this.init();
            }

            init() {
                this.updateSidebarState();
                this.setupSidebarStyles();

                window.addEventListener('resize', () => {
                    const wasMobile = this.isMobile;
                    this.isMobile = window.innerWidth < 1024;
                    if (wasMobile !== this.isMobile) {
                        this.updateSidebarState();
                    }
                });
            }

            setupSidebarStyles() {
                const style = document.createElement('style');
                style.textContent = `
                    /* 侧边栏基础样式 - 使用DaisyUI变量 */
                    .sidebar {
                        width: 320px;
                        min-width: 320px;
                        transition: all 0.3s ease-in-out;
                    }
                    .sidebar.collapsed {
                        width: 72px;
                        min-width: 72px;
                    }

                    /* 面包屑导航 - 使用DaisyUI内置样式 */
                    /* 移动端响应式 */
                    @media (max-width: 1023px) {
                        .sidebar {
                            position: fixed;
                            height: 100vh;
                            z-index: 50;
                            transform: translateX(-100%);
                        }
                        .sidebar.mobile-visible {
                            transform: translateX(0);
                        }
                    }

                    /* 收缩状态样式 - 简化版 */
                    .sidebar.collapsed .menu-text,
                    .sidebar.collapsed .logo-text {
                        display: none;
                    }

                    .sidebar.collapsed .menu li > a,
                    .sidebar.collapsed .menu li > summary {
                        justify-content: center;
                        padding: 0.75rem;
                    }

                    .sidebar.collapsed .menu li ul,
                    .sidebar.collapsed details > summary::after {
                        display: none;
                    }

                    /* 简化的过渡效果 */
                    .menu li > a,
                    .menu li > summary {
                        transition: all 0.2s ease-in-out;
                    }
                `;
                document.head.appendChild(style);
            }

            updateSidebarState() {
                if (this.isMobile) {
                    this.sidebar.classList.remove('collapsed');
                    this.sidebar.classList.add('mobile-hidden');
                    this.sidebar.classList.remove('mobile-visible');
                    this.overlay.classList.add('hidden');
                } else {
                    this.sidebar.classList.remove('mobile-hidden', 'mobile-visible');
                    this.sidebar.classList.toggle('collapsed', this.isCollapsed);
                    document.body.classList.toggle('sidebar-collapsed', this.isCollapsed);
                }
            }

            toggle() {
                if (this.isMobile) {
                    const isVisible = this.sidebar.classList.contains('mobile-visible');
                    if (isVisible) {
                        this.hide();
                    } else {
                        this.show();
                    }
                } else {
                    this.isCollapsed = !this.isCollapsed;
                    localStorage.setItem('sidebar-collapsed', this.isCollapsed);
                    this.updateSidebarState();
                }
            }

            show() {
                if (this.isMobile) {
                    this.sidebar.classList.remove('mobile-hidden');
                    this.sidebar.classList.add('mobile-visible');
                    this.overlay.classList.remove('hidden');
                }
            }

            hide() {
                if (this.isMobile) {
                    this.sidebar.classList.add('mobile-hidden');
                    this.sidebar.classList.remove('mobile-visible');
                    this.overlay.classList.add('hidden');
                }
            }
        }

        // 全局函数
        function toggleSidebar() {
            if (window.sidebarManager) {
                window.sidebarManager.toggle();
            }
        }

        // DaisyUI标准主题切换函数（向后兼容）
        function setTheme(theme) {
            // 使用DaisyUI标准方式
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);

            // 触发主题变更事件
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme: theme }
            }));
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'corporate';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            setTheme(newTheme);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化主题
            const savedTheme = localStorage.getItem('theme') || 'corporate';
            document.documentElement.setAttribute('data-theme', savedTheme);

            // 初始化SPA路由和侧边栏
            window.router = new SPARouter();
            window.sidebarManager = new SidebarManager();

            // 初始化当前页面的面包屑和导航状态
            const currentPath = window.location.pathname;
            window.router.updateBreadcrumbs(currentPath);
            window.router.updateNavigation(currentPath);

            // 监听主题变更事件
            window.addEventListener('themeChanged', function(event) {
                console.log('主题已切换到:', event.detail.theme);
            });
        });

        // 通用工具函数
        window.utils = {
            // 显示通知 - 使用DaisyUI toast
            showNotification: function(message, type = 'info') {
                // 创建toast容器（如果不存在）
                let toastContainer = document.getElementById('toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.id = 'toast-container';
                    toastContainer.className = 'toast toast-top toast-end z-50';
                    document.body.appendChild(toastContainer);
                }

                // 创建toast
                const toast = document.createElement('div');
                toast.className = `alert alert-${type} shadow-lg`;

                // DaisyUI图标映射
                const iconMap = {
                    'info': 'fas fa-info-circle',
                    'success': 'fas fa-check-circle',
                    'warning': 'fas fa-exclamation-triangle',
                    'error': 'fas fa-times-circle'
                };

                toast.innerHTML = `
                    <div>
                        <i class="${iconMap[type] || iconMap.info}"></i>
                        <span>${message}</span>
                    </div>
                `;

                toastContainer.appendChild(toast);

                // 自动移除
                setTimeout(() => {
                    toast.remove();
                    // 如果容器为空，也移除容器
                    if (toastContainer.children.length === 0) {
                        toastContainer.remove();
                    }
                }, 3000);
            },

            // 确认对话框
            confirm: function(message, callback) {
                if (confirm(message)) {
                    callback();
                }
            },

            // 格式化日期
            formatDate: function(date) {
                return new Date(date).toLocaleDateString('zh-CN');
            },

            // 格式化时间
            formatDateTime: function(date) {
                return new Date(date).toLocaleString('zh-CN');
            }
        };
    </script>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
