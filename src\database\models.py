"""
数据库模型定义
"""
import sqlite3
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import json

from utils.logger import logger


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/anesthesia_qc.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.init_database()
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        return conn
    
    def init_database(self):
        """初始化数据库表"""
        with self.get_connection() as conn:
            # 患者信息表 - 保存原始和脱敏后的数据
            conn.execute('''
                CREATE TABLE IF NOT EXISTS patients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    original_id TEXT,     -- 原始患者ID/住院号
                    masked_id TEXT,       -- 脱敏后的患者ID
                    original_name TEXT,   -- 原始患者姓名
                    masked_name TEXT,     -- 脱敏后的患者姓名
                    phone TEXT,           -- 脱敏后的联系电话
                    id_card TEXT,         -- 脱敏后的身份证号
                    age INTEGER,          -- 年龄（非敏感信息）
                    gender TEXT,          -- 性别（非敏感信息）
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 手术记录表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS surgery_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patient_id TEXT,
                    patient_name TEXT,
                    surgery_date DATE,
                    surgery_name TEXT,
                    anesthesia_method TEXT,
                    primary_anesthesia TEXT,
                    compound_anesthesia TEXT,
                    surgery_type TEXT,
                    postoperative_analgesia TEXT,
                    anesthesiologist TEXT,
                    surgeon TEXT,
                    department TEXT,
                    duration_minutes INTEGER,
                    asa_grade TEXT,
                    complications TEXT,
                    notes TEXT,
                    data_hash TEXT UNIQUE,
                    import_batch_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 导入批次表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS import_batches (
                    id TEXT PRIMARY KEY,
                    filename TEXT,
                    total_records INTEGER,
                    new_records INTEGER,
                    duplicate_records INTEGER,
                    error_records INTEGER,
                    privacy_enabled BOOLEAN,
                    status TEXT,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP
                )
            ''')
            
            # 系统配置表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS system_config (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 用户操作日志表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS operation_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    operation_type TEXT,
                    operation_detail TEXT,
                    user_ip TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            # 手术记录表索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_surgery_date ON surgery_records(surgery_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_anesthesia_method ON surgery_records(anesthesia_method)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_data_hash ON surgery_records(data_hash)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_import_batch ON surgery_records(import_batch_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_id ON surgery_records(patient_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_department ON surgery_records(department)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_surgery_type ON surgery_records(surgery_type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_created_at_surgery ON surgery_records(created_at)')

            # 患者表索引 - 安全创建
            try:
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_original_id ON patients(original_id)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_masked_id ON patients(masked_id)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_original_name ON patients(original_name)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_masked_name ON patients(masked_name)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patients(created_at)')
            except sqlite3.OperationalError as e:
                logger.warning(f"创建患者表索引时出现错误，可能是表结构不匹配: {e}")
                # 如果索引创建失败，可能是表结构问题，重新创建表
                conn.execute('DROP TABLE IF EXISTS patients')
                conn.execute('''
                    CREATE TABLE patients (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        original_id TEXT,     -- 原始患者ID/住院号
                        masked_id TEXT,       -- 脱敏后的患者ID
                        original_name TEXT,   -- 原始患者姓名
                        masked_name TEXT,     -- 脱敏后的患者姓名
                        phone TEXT,           -- 脱敏后的联系电话
                        id_card TEXT,         -- 脱敏后的身份证号
                        age INTEGER,          -- 年龄（非敏感信息）
                        gender TEXT,          -- 性别（非敏感信息）
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                # 重新创建索引
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_original_id ON patients(original_id)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_masked_id ON patients(masked_id)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_original_name ON patients(original_name)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_masked_name ON patients(masked_name)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patients(created_at)')

            # 导入批次表索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_batch_created_at ON import_batches(created_at)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_batch_status ON import_batches(status)')

            # 复合索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_surgery_patient_date ON surgery_records(patient_id, surgery_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_surgery_date_method ON surgery_records(surgery_date, anesthesia_method)')
            
            conn.commit()

            # 执行数据库迁移
            self._migrate_database(conn)

            logger.info("数据库初始化完成")

    def _migrate_database(self, conn):
        """执行数据库迁移"""
        try:
            # 检查是否需要添加patient_name字段
            cursor = conn.execute("PRAGMA table_info(surgery_records)")
            columns = [col[1] for col in cursor.fetchall()]

            if 'patient_name' not in columns:
                logger.info("添加patient_name字段...")
                conn.execute("ALTER TABLE surgery_records ADD COLUMN patient_name TEXT")

            if 'surgery_name' not in columns:
                logger.info("添加surgery_name字段...")
                conn.execute("ALTER TABLE surgery_records ADD COLUMN surgery_name TEXT")

            conn.commit()
            logger.info("数据库迁移完成")

        except Exception as e:
            logger.error(f"数据库迁移失败: {str(e)}")

    def generate_data_hash(self, record: Dict[str, Any]) -> str:
        """生成数据哈希用于重复检测"""
        # 使用关键字段生成哈希
        key_fields = [
            str(record.get('original_id', '')),
            str(record.get('surgery_date', '')),
            str(record.get('anesthesia_method', '')),
            str(record.get('surgery_type', ''))
        ]
        hash_string = '|'.join(key_fields)
        return hashlib.md5(hash_string.encode('utf-8')).hexdigest()
    
    def check_duplicate(self, data_hash: str) -> bool:
        """检查是否为重复数据"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                'SELECT COUNT(*) FROM surgery_records WHERE data_hash = ?',
                (data_hash,)
            )
            count = cursor.fetchone()[0]
            return count > 0
    
    def insert_patient(self, patient_data: Dict[str, Any]) -> int:
        """插入患者信息 - 优化的数据结构处理"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT INTO patients (
                    original_id, masked_id, original_name, masked_name,
                    phone, id_card, age, gender
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                patient_data.get('original_id', ''),     # 原始ID
                patient_data.get('masked_id', ''),       # 脱敏后的ID
                patient_data.get('original_name', ''),   # 原始姓名
                patient_data.get('masked_name', ''),     # 脱敏后的姓名
                patient_data.get('phone', ''),           # 脱敏后的电话
                patient_data.get('id_card', ''),         # 脱敏后的身份证
                patient_data.get('age'),                 # 年龄
                patient_data.get('gender', '')           # 性别
            ))
            conn.commit()
            return cursor.lastrowid
    
    def find_or_create_patient(self, patient_data: Dict[str, Any]) -> int:
        """查找或创建患者 - 使用实际的数据库结构"""
        # 通过脱敏后的患者ID查找 (在 masked_id 字段中)
        patient_id = patient_data.get('patient_id')
        if patient_id:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    'SELECT id FROM patients WHERE masked_id = ?',
                    (patient_id,)
                )
                result = cursor.fetchone()
                if result:
                    return result[0]

        # 如果没找到，也尝试通过原始ID查找
        original_id = patient_data.get('original_id')
        if original_id:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    'SELECT id FROM patients WHERE original_id = ?',
                    (original_id,)
                )
                result = cursor.fetchone()
                if result:
                    return result[0]

        # 如果没找到，创建新患者
        return self.insert_patient(patient_data)
    
    def insert_surgery_record(self, record_data: Dict[str, Any]) -> int:
        """插入手术记录"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT INTO surgery_records (
                    patient_id, patient_name, surgery_date, surgery_name,
                    anesthesia_method, primary_anesthesia, compound_anesthesia,
                    surgery_type, postoperative_analgesia, anesthesiologist,
                    surgeon, department, duration_minutes, asa_grade,
                    complications, notes, data_hash, import_batch_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record_data.get('patient_id'),
                record_data.get('patient_name'),
                record_data.get('surgery_date'),
                record_data.get('surgery_name'),
                record_data.get('anesthesia_method'),
                record_data.get('primary_anesthesia'),
                record_data.get('compound_anesthesia'),
                record_data.get('surgery_type'),
                record_data.get('postoperative_analgesia'),
                record_data.get('anesthesiologist'),
                record_data.get('surgeon'),
                record_data.get('department'),
                record_data.get('duration_minutes'),
                record_data.get('asa_grade'),
                record_data.get('complications'),
                record_data.get('notes'),
                record_data.get('data_hash'),
                record_data.get('import_batch_id')
            ))
            conn.commit()
            return cursor.lastrowid
    
    def create_import_batch(self, batch_data: Dict[str, Any]) -> str:
        """创建导入批次"""
        with self.get_connection() as conn:
            conn.execute('''
                INSERT INTO import_batches (
                    id, filename, total_records, new_records, 
                    duplicate_records, error_records, privacy_enabled, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                batch_data.get('id'),
                batch_data.get('filename'),
                batch_data.get('total_records', 0),
                batch_data.get('new_records', 0),
                batch_data.get('duplicate_records', 0),
                batch_data.get('error_records', 0),
                batch_data.get('privacy_enabled', False),
                batch_data.get('status', 'processing')
            ))
            conn.commit()
            return batch_data.get('id')
    
    def update_import_batch(self, batch_id: str, updates: Dict[str, Any]):
        """更新导入批次"""
        with self.get_connection() as conn:
            set_clause = ', '.join([f"{key} = ?" for key in updates.keys()])
            values = list(updates.values()) + [batch_id]

            conn.execute(f'''
                UPDATE import_batches
                SET {set_clause}
                WHERE id = ?
            ''', values)
            conn.commit()
    
    def get_statistics(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取统计数据"""
        with self.get_connection() as conn:
            # 基础统计
            where_clause = ""
            params = []
            
            if start_date and end_date:
                where_clause = "WHERE surgery_date BETWEEN ? AND ?"
                params = [start_date, end_date]
            
            # 总记录数
            cursor = conn.execute(f'SELECT COUNT(*) FROM surgery_records {where_clause}', params)
            total_records = cursor.fetchone()[0]
            
            # 患者数
            cursor = conn.execute(f'''
                SELECT COUNT(DISTINCT patient_id) FROM surgery_records {where_clause}
            ''', params)
            total_patients = cursor.fetchone()[0]
            
            # 麻醉方式统计
            cursor = conn.execute(f'''
                SELECT primary_anesthesia, COUNT(*) as count 
                FROM surgery_records {where_clause}
                GROUP BY primary_anesthesia
                ORDER BY count DESC
            ''', params)
            anesthesia_stats = dict(cursor.fetchall())
            
            # 手术类型统计
            cursor = conn.execute(f'''
                SELECT surgery_type, COUNT(*) as count 
                FROM surgery_records {where_clause}
                GROUP BY surgery_type
                ORDER BY count DESC
            ''', params)
            surgery_type_stats = dict(cursor.fetchall())
            
            # 按月统计
            cursor = conn.execute(f'''
                SELECT strftime('%Y-%m', surgery_date) as month, COUNT(*) as count
                FROM surgery_records {where_clause}
                GROUP BY month
                ORDER BY month
            ''', params)
            monthly_stats = dict(cursor.fetchall())
            
            return {
                'total_records': total_records,
                'total_patients': total_patients,
                'anesthesia_stats': anesthesia_stats,
                'surgery_type_stats': surgery_type_stats,
                'monthly_stats': monthly_stats
            }
    
    def get_recent_imports(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的导入记录"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM import_batches 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def log_operation(self, operation_type: str, operation_detail: str, user_ip: str = None):
        """记录操作日志"""
        with self.get_connection() as conn:
            conn.execute('''
                INSERT INTO operation_logs (operation_type, operation_detail, user_ip)
                VALUES (?, ?, ?)
            ''', (operation_type, operation_detail, user_ip))
            conn.commit()

    def get_surgery_record_count(self) -> int:
        """获取手术记录总数"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT COUNT(*) FROM surgery_records')
            return cursor.fetchone()[0]

    def get_patient_count(self) -> int:
        """获取患者总数"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT COUNT(*) FROM patients')
            return cursor.fetchone()[0]

    def get_import_batch_count(self) -> int:
        """获取导入批次总数"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT COUNT(*) FROM import_batches')
            return cursor.fetchone()[0]

    def get_database_size(self) -> float:
        """获取数据库大小（MB）"""
        try:
            size_bytes = self.db_path.stat().st_size
            return size_bytes / (1024 * 1024)  # 转换为MB
        except:
            return 0.0

    def get_surgery_record_by_id(self, record_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取手术记录详情"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT sr.*, p.original_name as patient_name, p.masked_name
                FROM surgery_records sr
                LEFT JOIN patients p ON sr.patient_id = p.id
                WHERE sr.id = ?
            ''', (record_id,))

            row = cursor.fetchone()
            return dict(row) if row else None

    def get_record_navigation(self, record_id: int) -> Dict[str, Optional[int]]:
        """获取记录导航信息（上一条/下一条）"""
        with self.get_connection() as conn:
            # 获取上一条记录
            prev_cursor = conn.execute('''
                SELECT id FROM surgery_records
                WHERE id < ?
                ORDER BY id DESC
                LIMIT 1
            ''', (record_id,))
            prev_row = prev_cursor.fetchone()
            prev_id = prev_row[0] if prev_row else None

            # 获取下一条记录
            next_cursor = conn.execute('''
                SELECT id FROM surgery_records
                WHERE id > ?
                ORDER BY id ASC
                LIMIT 1
            ''', (record_id,))
            next_row = next_cursor.fetchone()
            next_id = next_row[0] if next_row else None

            return {
                'prev_id': prev_id,
                'next_id': next_id
            }

    def delete_surgery_record(self, record_id: int) -> bool:
        """删除手术记录"""
        with self.get_connection() as conn:
            cursor = conn.execute('DELETE FROM surgery_records WHERE id = ?', (record_id,))
            conn.commit()
            return cursor.rowcount > 0

    def batch_delete_surgery_records(self, record_ids: List[int]) -> int:
        """批量删除手术记录"""
        if not record_ids:
            return 0

        with self.get_connection() as conn:
            placeholders = ','.join(['?'] * len(record_ids))
            cursor = conn.execute(f'DELETE FROM surgery_records WHERE id IN ({placeholders})', record_ids)
            conn.commit()
            return cursor.rowcount
