"""
数据处理服务
"""
import uuid
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

from database.models import DatabaseManager
from utils.data_privacy import DataPrivacyProcessor, get_default_privacy_config
from utils.logger import logger
from utils.performance import timing_decorator, PerformanceContext
from .data_pipeline import DataProcessingPipeline, create_default_config


class DataImportService:
    """数据导入服务"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.privacy_processor = DataPrivacyProcessor()
        self.processing_pipeline = DataProcessingPipeline()
        self.upload_dir = Path("uploads")
        self.upload_dir.mkdir(exist_ok=True)
        self.progress_callback = None  # 进度回调函数
        logger.info("数据导入服务初始化完成")

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def _send_progress(self, import_id: str, progress_data: Dict[str, Any]):
        """发送进度更新"""
        if self.progress_callback:
            try:
                self.progress_callback(import_id, progress_data)
            except Exception as e:
                logger.error(f"发送进度更新失败: {str(e)}")

    def _send_log(self, import_id: str, message: str, level: str = 'info'):
        """发送日志消息"""
        if self.progress_callback:
            try:
                log_data = {
                    'message': message,
                    'level': level,
                    'timestamp': datetime.now().isoformat()
                }
                self.progress_callback(import_id, {'type': 'log', 'data': log_data})
            except Exception as e:
                logger.error(f"发送日志失败: {str(e)}")
    
    @timing_decorator
    def import_excel_file(self, file_path: str, privacy_enabled: bool = True,
                         privacy_config: Dict[str, Any] = None,
                         field_mappings: Dict[str, int] = None,
                         import_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        优化的数据导入流程 - 增强版本

        Args:
            file_path: Excel文件路径
            privacy_enabled: 是否启用数据脱敏
            privacy_config: 脱敏配置
            field_mappings: 字段映射配置 {field_key: column_index}
            import_config: 导入配置

        Returns:
            导入结果
        """
        batch_id = str(uuid.uuid4())
        filename = Path(file_path).name

        # 设置默认配置
        if import_config is None:
            import_config = {
                'enableCleaning': True,
                'skipDuplicates': True,
                'validateData': True,
                'duplicateStrategy': 'skip',
                'errorStrategy': 'skip',
                'batchSize': 500
            }

        logger.info(f"🚀 开始导入文件: {filename}, 批次ID: {batch_id}, 脱敏: {privacy_enabled}")
        logger.info(f"📋 导入配置: {import_config}")
        logger.info(f"🗺️ 字段映射: {field_mappings}")

        # 发送开始导入的进度更新
        self._send_progress(batch_id, {
            'type': 'progress',
            'data': {
                'percent': 0,
                'status': '开始导入',
                'current_step': '初始化',
                'total_records': 0,
                'processed_records': 0
            }
        })
        self._send_log(batch_id, f"开始导入文件: {filename}", 'info')

        try:
            # 1. 创建导入批次记录
            batch_data = {
                'id': batch_id,
                'filename': filename,
                'privacy_enabled': privacy_enabled,
                'import_config': import_config,
                'field_mappings': field_mappings,
                'status': 'processing'
            }
            self.db.create_import_batch(batch_data)

            # 2. 读取和验证Excel文件
            with PerformanceContext("读取Excel文件"):
                df = pd.read_excel(file_path)
                logger.info(f"📊 读取到 {len(df)} 条记录，{len(df.columns)} 列")

                # 如果提供了字段映射，使用映射验证
                if field_mappings:
                    missing_mappings = []
                    required_fields = ['patient_id', 'patient_name', 'surgery_date']

                    for field in required_fields:
                        if field not in field_mappings:
                            missing_mappings.append(field)

                    if missing_mappings:
                        raise ValueError(f"缺少必填字段映射: {missing_mappings}")

                    # 验证映射的列索引是否有效
                    for field, col_index in field_mappings.items():
                        if col_index >= len(df.columns):
                            raise ValueError(f"字段 {field} 映射的列索引 {col_index} 超出范围")

                    # 应用字段映射，重命名列
                    df = self._apply_field_mappings(df, field_mappings)

                else:
                    # 使用传统的字段名匹配
                    required_fields_mapping = {
                        'patient_id': ['patient_id', '患者ID', '病人ID', '住院号'],
                        'patient_name': ['patient_name', '患者姓名', '病人姓名', '姓名'],
                        'surgery_date': ['surgery_date', '手术日期', '手术时间', '日期']
                    }

                    missing_fields = []
                    for field_key, possible_names in required_fields_mapping.items():
                        if not any(name in df.columns for name in possible_names):
                            missing_fields.append(f"{field_key} (可能的字段名: {possible_names})")

                    if missing_fields:
                        logger.error(f"文件字段: {list(df.columns)}")
                        raise ValueError(f"缺少必要字段: {missing_fields}")

                    # 标准化列名
                    df = self._standardize_column_names(df)

            # 3. 数据清洗和标准化（如果启用）
            if import_config.get('enableCleaning', True):
                with PerformanceContext("数据清洗"):
                    cleaned_df = self._clean_data(df)
                    logger.info(f"🧹 数据清洗完成，有效记录: {len(cleaned_df)}")
            else:
                cleaned_df = df
                logger.info("⏭️ 跳过数据清洗")

            # 4. 数据验证（如果启用）
            if import_config.get('validateData', True):
                with PerformanceContext("数据验证"):
                    validation_result = self._validate_data(cleaned_df)
                    if validation_result['errors']:
                        error_strategy = import_config.get('errorStrategy', 'skip')
                        if error_strategy == 'stop':
                            raise ValueError(f"数据验证失败: {validation_result['errors']}")
                        elif error_strategy == 'skip':
                            # 过滤掉有错误的行
                            cleaned_df = cleaned_df.drop(validation_result['error_rows'])
                            logger.warning(f"⚠️ 跳过 {len(validation_result['error_rows'])} 行有错误的数据")

            # 5. 统一的数据导入处理
            with PerformanceContext("导入数据库"):
                import_result = self._process_and_import_data(
                    cleaned_df, batch_id, privacy_enabled, import_config
                )

            # 5. 更新批次状态
            self.db.update_import_batch(batch_id, {
                'total_records': len(df),
                'new_records': import_result['new_records'],
                'duplicate_records': import_result['duplicate_records'],
                'error_records': import_result['error_records'],
                'status': 'completed',
                'completed_at': datetime.now().isoformat()
            })

            # 6. 记录操作日志
            self.db.log_operation(
                'data_import',
                f"导入文件 {filename}: {import_result['new_records']} 新记录, "
                f"{import_result['duplicate_records']} 重复记录, "
                f"{import_result['error_records']} 错误记录"
            )

            result = {
                'success': True,
                'batch_id': batch_id,
                'filename': filename,
                'total_records': len(df),
                'new_records': import_result['new_records'],
                'duplicate_records': import_result['duplicate_records'],
                'error_records': import_result['error_records'],
                'privacy_enabled': privacy_enabled,
                'privacy_summary': self.privacy_processor.get_privacy_summary() if privacy_enabled else None
            }

            logger.info(f"✅ 文件导入完成: 新增{result['new_records']}条, 重复{result['duplicate_records']}条, 错误{result['error_records']}条")
            return result

        except Exception as e:
            logger.error(f"❌ 文件导入失败: {str(e)}")

            # 更新批次状态为失败
            self.db.update_import_batch(batch_id, {
                'status': 'failed',
                'error_message': str(e),
                'completed_at': datetime.now().isoformat()
            })

            return {
                'success': False,
                'batch_id': batch_id,
                'error': str(e)
            }
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        # 复制数据避免修改原始数据
        cleaned_df = df.copy()
        
        # 标准化列名
        column_mapping = {
            # 患者基本信息
            '患者ID': 'patient_id',
            '住院号': 'patient_id',
            '病案号': 'patient_id',
            '患者姓名': 'patient_name',
            '姓名': 'patient_name',
            '性别': 'gender',
            '年龄': 'age',

            # 手术信息
            '手术日期': 'surgery_date',
            '手术室': 'operating_room',
            '手术间': 'operating_room_number',
            '手术名称': 'surgery_name',
            '手术类型': 'surgery_type',
            '手术级别': 'surgery_level',
            '切口等级': 'incision_level',
            '术前诊断': 'preoperative_diagnosis',

            # 麻醉信息
            '麻醉方法': 'anesthesia_method',
            '麻醉方式': 'anesthesia_method',
            '主麻': 'anesthesiologist',
            '副麻': 'assistant_anesthesiologist',
            '麻醉医生': 'anesthesiologist',
            '麻醉助手': 'anesthesia_assistant',
            '术后镇痛': 'postoperative_analgesia',

            # 手术人员
            '手术医生': 'surgeon',
            '一助': 'first_assistant',
            '二助': 'second_assistant',
            '三助': 'third_assistant',
            '灌注医生': 'perfusionist',
            '洗手护士1': 'scrub_nurse_1',
            '洗手护士2': 'scrub_nurse_2',
            '巡回护士1': 'circulating_nurse_1',
            '巡回护士2': 'circulating_nurse_2',
            '接替器械护士': 'relief_scrub_nurse',
            '接替巡回护士': 'relief_circulating_nurse',

            # 时间信息
            '入室时间': 'room_entry_time',
            '麻醉开始时间': 'anesthesia_start_time',
            '手术开始时间': 'surgery_start_time',
            '手术结束时间': 'surgery_end_time',
            '麻醉结束时间': 'anesthesia_end_time',
            '出室时间': 'room_exit_time',
            '入PACU时间': 'pacu_entry_time',
            '出PACU时间': 'pacu_exit_time',

            # 其他信息
            '临床科室': 'department',
            '体位': 'position',
            '压疮评估': 'pressure_ulcer_assessment',
            '手术部位标识': 'surgical_site_marking',
            '术中导尿': 'intraoperative_catheterization',
            '尿管护理': 'catheter_care',
            '病理标本': 'pathological_specimen',
            '科室': 'department',
            '手术时长': 'duration_minutes',
            'ASA分级': 'asa_grade',
            '并发症': 'complications',
            '备注': 'notes',
            '手机号': 'phone',
            '联系电话': 'phone',
            '身份证号': 'id_card'
        }
        
        # 重命名列
        for old_name, new_name in column_mapping.items():
            if old_name in cleaned_df.columns:
                cleaned_df = cleaned_df.rename(columns={old_name: new_name})
        
        # 处理麻醉方法 - 使用新的清洗器
        if 'anesthesia_method' in cleaned_df.columns:
            logger.info("开始清洗麻醉方法...")

            # 使用新的麻醉方法清洗器
            anesthesia_results = cleaned_df['anesthesia_method'].apply(
                self.processing_pipeline.cleaner.anesthesia_cleaner.clean_anesthesia_method
            )

            # 展开清洗结果
            cleaned_df['anesthesia_method_cleaned'] = anesthesia_results.apply(lambda x: x['cleaned'])
            cleaned_df['primary_anesthesia'] = anesthesia_results.apply(lambda x: x['primary_anesthesia'])
            cleaned_df['compound_anesthesia'] = anesthesia_results.apply(lambda x: x['compound_anesthesia'])
            cleaned_df['primary_anesthesia_raw'] = anesthesia_results.apply(lambda x: x['primary_raw'])
            cleaned_df['compound_anesthesia_raw'] = anesthesia_results.apply(lambda x: x['compound_raw'])

            # 更新主要的麻醉方法字段
            cleaned_df['anesthesia_method'] = cleaned_df['primary_anesthesia']

            # 统计清洗结果
            original_unique = cleaned_df['anesthesia_method'].nunique()
            logger.info(f"麻醉方法清洗完成，标准化为 {original_unique} 种方法")
        
        # 处理日期
        if 'surgery_date' in cleaned_df.columns:
            cleaned_df['surgery_date'] = pd.to_datetime(cleaned_df['surgery_date'], errors='coerce').dt.strftime('%Y-%m-%d')
        
        # 处理数值字段
        if 'duration_minutes' in cleaned_df.columns:
            cleaned_df['duration_minutes'] = pd.to_numeric(cleaned_df['duration_minutes'], errors='coerce')
        
        # 填充空值
        cleaned_df = cleaned_df.fillna('')
        
        return cleaned_df

    def _process_and_import_data(self, df: pd.DataFrame, batch_id: str, privacy_enabled: bool) -> Dict[str, int]:
        """
        统一的数据处理和导入方法
        整合脱敏、重复检测和数据库插入逻辑
        """
        logger.info(f"🔄 开始处理和导入数据，共 {len(df)} 条记录，脱敏模式: {privacy_enabled}")

        new_records = 0
        duplicate_records = 0
        error_records = 0

        for index, row in df.iterrows():
            try:
                # 1. 提取原始数据
                original_patient_id = str(row.get('patient_id', '')).strip()
                original_patient_name = str(row.get('patient_name', '')).strip()
                original_phone = str(row.get('phone', '')).strip()
                original_id_card = str(row.get('id_card', '')).strip()

                # 验证必要字段
                if not original_patient_id or not original_patient_name:
                    logger.warning(f"第 {index + 1} 行缺少必要字段，跳过")
                    error_records += 1
                    continue

                # 2. 数据脱敏处理
                if privacy_enabled:
                    # 使用*替换的脱敏方法
                    masked_patient_id = self.privacy_processor.mask_hospital_id(original_patient_id)
                    masked_patient_name = self.privacy_processor.mask_name(original_patient_name)
                    masked_phone = self.privacy_processor.mask_phone_number(original_phone) if original_phone else ''
                    masked_id_card = self.privacy_processor.mask_id_card(original_id_card) if original_id_card else ''
                else:
                    # 不脱敏，直接使用原始数据
                    masked_patient_id = original_patient_id
                    masked_patient_name = original_patient_name
                    masked_phone = original_phone
                    masked_id_card = original_id_card

                # 3. 准备患者数据
                patient_data = {
                    # 原始数据（用于重复检测）
                    'original_id': original_patient_id,
                    'original_name': original_patient_name,
                    # 脱敏数据（用于存储）
                    'masked_id': masked_patient_id,
                    'masked_name': masked_patient_name,
                    'phone': masked_phone,
                    'id_card': masked_id_card,
                    'age': row.get('age'),
                    'gender': str(row.get('gender', '')).strip()
                }

                # 4. 查找或创建患者
                patient_db_id = self._find_or_create_patient_optimized(patient_data)

                # 5. 准备手术记录数据
                surgery_data = {
                    'patient_id': patient_db_id,
                    'surgery_date': str(row.get('surgery_date', '')),
                    'anesthesia_method': str(row.get('anesthesia_method', '')).strip(),
                    'primary_anesthesia': str(row.get('primary_anesthesia', '')).strip(),
                    'compound_anesthesia': str(row.get('compound_anesthesia', '')).strip(),
                    'surgery_type': str(row.get('surgery_type', '')).strip(),
                    'postoperative_analgesia': str(row.get('postoperative_analgesia', '')).strip(),
                    'anesthesiologist': self._mask_staff_name(str(row.get('anesthesiologist', '')).strip()),
                    'surgeon': self._mask_staff_name(str(row.get('surgeon', '')).strip()),
                    'department': str(row.get('department', '')).strip(),
                    'duration_minutes': row.get('duration_minutes'),
                    'asa_grade': str(row.get('asa_grade', '')).strip(),
                    'complications': str(row.get('complications', '')).strip(),
                    'notes': str(row.get('notes', '')).strip(),
                    'import_batch_id': batch_id
                }

                # 6. 重复检测（基于原始数据生成哈希）
                duplicate_hash = self._generate_record_hash(
                    original_patient_id,
                    surgery_data['surgery_date'],
                    surgery_data['anesthesia_method'],
                    surgery_data['surgery_type']
                )
                surgery_data['data_hash'] = duplicate_hash

                # 7. 检查重复并插入
                if self.db.check_duplicate(duplicate_hash):
                    duplicate_records += 1
                    logger.debug(f"发现重复记录: 患者{original_patient_id}, 日期{surgery_data['surgery_date']}")
                else:
                    # 插入新记录
                    self.db.insert_surgery_record(surgery_data)
                    new_records += 1

            except Exception as e:
                logger.error(f"处理第 {index + 1} 行数据时出错: {str(e)}")
                error_records += 1

        logger.info(f"✅ 数据处理完成: 新增{new_records}条, 重复{duplicate_records}条, 错误{error_records}条")
        return {
            'new_records': new_records,
            'duplicate_records': duplicate_records,
            'error_records': error_records
        }

    def _find_or_create_patient_optimized(self, patient_data: Dict[str, Any]) -> int:
        """
        优化的患者查找或创建方法
        如果患者存在但缺少脱敏数据，则更新脱敏信息
        """
        original_id = patient_data.get('original_id')
        masked_id = patient_data.get('masked_id')

        if original_id:
            with self.db.get_connection() as conn:
                # 查找现有患者
                cursor = conn.execute(
                    'SELECT id, masked_id, masked_name FROM patients WHERE original_id = ?',
                    (original_id,)
                )
                result = cursor.fetchone()

                if result:
                    patient_id, existing_masked_id, existing_masked_name = result

                    # 检查是否需要更新脱敏信息
                    needs_update = False
                    update_fields = []
                    update_values = []

                    if not existing_masked_id or existing_masked_id == original_id:
                        # 脱敏ID缺失或未脱敏，需要更新
                        update_fields.append('masked_id = ?')
                        update_values.append(masked_id)
                        needs_update = True

                    if not existing_masked_name or existing_masked_name == patient_data.get('original_name'):
                        # 脱敏姓名缺失或未脱敏，需要更新
                        update_fields.append('masked_name = ?')
                        update_values.append(patient_data.get('masked_name'))
                        needs_update = True

                    if needs_update:
                        # 更新脱敏信息
                        update_sql = f"UPDATE patients SET {', '.join(update_fields)} WHERE id = ?"
                        update_values.append(patient_id)
                        conn.execute(update_sql, update_values)
                        conn.commit()
                        logger.info(f"更新患者 {original_id} 的脱敏信息")

                    return patient_id

        # 先通过脱敏ID查找
        if masked_id:
            with self.db.get_connection() as conn:
                cursor = conn.execute(
                    'SELECT id FROM patients WHERE masked_id = ?',
                    (masked_id,)
                )
                result = cursor.fetchone()
                if result:
                    return result[0]

        # 如果没找到，创建新患者
        return self.db.insert_patient(patient_data)

    def _mask_staff_name(self, name: str) -> str:
        """
        医护人员姓名脱敏
        """
        if not name or name.strip() == '':
            return ''
        return self.privacy_processor.mask_name(name.strip())

    def _generate_record_hash(self, patient_id: str, surgery_date: str,
                            anesthesia_method: str, surgery_type: str) -> str:
        """
        生成记录哈希用于重复检测
        """
        hash_data = {
            'patient_id': patient_id,
            'surgery_date': surgery_date,
            'anesthesia_method': anesthesia_method,
            'surgery_type': surgery_type
        }
        return self.db.generate_data_hash(hash_data)

    def _apply_privacy_protection(self, record_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用数据脱敏保护

        Args:
            record_data: 原始记录数据

        Returns:
            脱敏后的记录数据
        """
        try:
            protected_data = record_data.copy()

            # 脱敏患者ID
            if 'patient_id' in protected_data and protected_data['patient_id']:
                protected_data['patient_id'] = self.privacy_processor.mask_hospital_id(
                    str(protected_data['patient_id'])
                )

            # 脱敏患者姓名
            if 'patient_name' in protected_data and protected_data['patient_name']:
                protected_data['patient_name'] = self.privacy_processor.mask_name(
                    str(protected_data['patient_name'])
                )

            # 脱敏手机号
            if 'phone' in protected_data and protected_data['phone']:
                protected_data['phone'] = self.privacy_processor.mask_phone_number(
                    str(protected_data['phone'])
                )

            # 脱敏身份证号
            if 'id_card' in protected_data and protected_data['id_card']:
                protected_data['id_card'] = self.privacy_processor.mask_id_card(
                    str(protected_data['id_card'])
                )

            # 脱敏医护人员姓名
            if 'anesthesiologist' in protected_data and protected_data['anesthesiologist']:
                protected_data['anesthesiologist'] = self.privacy_processor.mask_name(
                    str(protected_data['anesthesiologist'])
                )

            if 'surgeon' in protected_data and protected_data['surgeon']:
                protected_data['surgeon'] = self.privacy_processor.mask_name(
                    str(protected_data['surgeon'])
                )

            # 脱敏其他医护人员字段
            medical_staff_fields = [
                'assistant_1', 'assistant_2', 'assistant_3',  # 助手
                'anesthesia_assistant',  # 麻醉助手
                'perfusionist',  # 灌注医生
                'scrub_nurse_1', 'scrub_nurse_2',  # 洗手护士
                'circulating_nurse_1', 'circulating_nurse_2',  # 巡回护士
                'relief_scrub_nurse', 'relief_circulating_nurse'  # 接替护士
            ]

            for field in medical_staff_fields:
                if field in protected_data and protected_data[field]:
                    protected_data[field] = self.privacy_processor.mask_name(
                        str(protected_data[field])
                    )

            return protected_data

        except Exception as e:
            logger.error(f"数据脱敏失败: {str(e)}")
            return record_data

    def _check_duplicate_record(self, record_data: Dict[str, Any]) -> bool:
        """
        检查记录是否重复

        Args:
            record_data: 记录数据

        Returns:
            是否重复
        """
        try:
            # 生成记录哈希
            hash_data = {
                'patient_id': record_data.get('patient_id', ''),
                'surgery_date': record_data.get('surgery_date', ''),
                'anesthesia_method': record_data.get('anesthesia_method', ''),
                'surgery_type': record_data.get('surgery_type', '')
            }

            data_hash = self.db.generate_data_hash(hash_data)

            # 检查数据库中是否存在相同哈希
            return self.db.check_duplicate(data_hash)

        except Exception as e:
            logger.error(f"重复检查失败: {str(e)}")
            return False

    def _import_to_database(self, df: pd.DataFrame, batch_id: str, privacy_enabled: bool) -> Dict[str, int]:
        """导入数据到数据库"""
        new_records = 0
        duplicate_records = 0
        error_records = 0
        
        for index, row in df.iterrows():
            try:
                # 准备患者数据 - 包含原始数据和脱敏数据
                original_patient_id = row.get('patient_id', '')
                original_patient_name = row.get('patient_name', '')
                original_phone = row.get('phone', '')
                original_id_card = row.get('id_card', '')

                # 使用正确的脱敏方法（*替换）
                patient_data = {
                    # 原始数据
                    'original_id': original_patient_id,
                    'original_name': original_patient_name,
                    # 脱敏数据
                    'patient_id': self.privacy_processor.mask_hospital_id(original_patient_id),  # 使用*替换的方法
                    'name': self.privacy_processor.mask_name(original_patient_name),  # 使用*替换的方法
                    'phone': self.privacy_processor.mask_phone_number_consistent(original_phone),
                    'id_card': self.privacy_processor.mask_id_card(original_id_card),  # 使用现有方法
                    'age': row.get('age'),                       # 年龄（非敏感）
                    'gender': row.get('gender', '')              # 性别（非敏感）
                }

                # 查找或创建患者
                patient_id = self.db.find_or_create_patient(patient_data)
                
                # 准备手术记录数据
                record_data = {
                    'patient_id': patient_id,
                    'surgery_date': str(row.get('surgery_date', '')),
                    'anesthesia_method': str(row.get('anesthesia_method', '')),
                    'primary_anesthesia': str(row.get('primary_anesthesia', '')),
                    'compound_anesthesia': str(row.get('compound_anesthesia', '')),
                    'surgery_type': str(row.get('surgery_type', '')),
                    'postoperative_analgesia': str(row.get('postoperative_analgesia', '')),
                    'anesthesiologist': str(row.get('anesthesiologist', '')),
                    'surgeon': str(row.get('surgeon', '')),
                    'department': str(row.get('department', '')),
                    'duration_minutes': int(row.get('duration_minutes', 0)) if pd.notna(row.get('duration_minutes')) else None,
                    'asa_grade': str(row.get('asa_grade', '')),
                    'complications': str(row.get('complications', '')),
                    'notes': str(row.get('notes', '')),
                    'import_batch_id': batch_id
                }
                
                # 生成数据哈希
                hash_data = {
                    'original_id': patient_data['original_id'],
                    'surgery_date': record_data['surgery_date'],
                    'anesthesia_method': record_data['anesthesia_method'],
                    'surgery_type': record_data['surgery_type']
                }
                data_hash = self.db.generate_data_hash(hash_data)
                record_data['data_hash'] = data_hash
                
                # 检查重复
                if self.db.check_duplicate(data_hash):
                    duplicate_records += 1
                    logger.debug(f"发现重复记录: {data_hash}")
                else:
                    # 插入新记录
                    self.db.insert_surgery_record(record_data)
                    new_records += 1
                
            except Exception as e:
                logger.error(f"处理第 {index + 1} 行数据时出错: {str(e)}")
                error_records += 1
        
        return {
            'new_records': new_records,
            'duplicate_records': duplicate_records,
            'error_records': error_records
        }


    def process_file_with_pipeline(self, file_path: str,
                                 clean_data: bool = True,
                                 anonymize_data: bool = True,
                                 import_to_db: bool = True,
                                 output_dir: str = "output") -> Dict[str, Any]:
        """
        使用新的处理管道处理文件

        Args:
            file_path: 文件路径
            clean_data: 是否进行数据清洗
            anonymize_data: 是否进行数据脱敏
            import_to_db: 是否导入到数据库
            output_dir: 输出目录

        Returns:
            处理结果
        """
        logger.info(f"使用处理管道处理文件: {file_path}")

        try:
            # 1. 验证文件
            validation_result = self.processing_pipeline.validate_file(file_path)
            if not validation_result['is_valid']:
                return {
                    'success': False,
                    'error': f"文件验证失败: {validation_result['errors']}",
                    'validation_result': validation_result
                }

            # 2. 获取默认配置
            config = create_default_config()

            # 3. 处理文件
            processing_result = self.processing_pipeline.process_file(
                file_path=file_path,
                output_dir=output_dir,
                clean_data=clean_data,
                anonymize_data=anonymize_data,
                field_mapping=config['field_mapping'],
                anonymize_config=config['anonymize_config'],
                save_intermediate=True
            )

            # 4. 如果需要，导入到数据库
            if import_to_db and processing_result.get('final_file'):
                import_result = self.import_excel_file(
                    processing_result['final_file'],
                    privacy_enabled=False  # 已经脱敏过了
                )
                processing_result['import_result'] = import_result

            return {
                'success': True,
                'processing_result': processing_result,
                'validation_result': validation_result
            }

        except Exception as e:
            logger.error(f"处理管道执行失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _apply_field_mappings(self, df: pd.DataFrame, field_mappings: Dict[str, int]) -> pd.DataFrame:
        """应用字段映射，重命名列"""
        try:
            # 创建新的DataFrame，只包含映射的列
            mapped_df = pd.DataFrame()

            for field_key, col_index in field_mappings.items():
                if col_index < len(df.columns):
                    original_col_name = df.columns[col_index]
                    mapped_df[field_key] = df.iloc[:, col_index]
                    logger.info(f"🗺️ 映射字段: {original_col_name} -> {field_key}")

            return mapped_df

        except Exception as e:
            logger.error(f"应用字段映射失败: {str(e)}")
            raise ValueError(f"字段映射失败: {str(e)}")

    def _standardize_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        try:
            column_mapping = {
                # 患者ID相关
                '患者ID': 'patient_id', '病人ID': 'patient_id', '住院号': 'patient_id',
                # 患者姓名相关
                '患者姓名': 'patient_name', '病人姓名': 'patient_name', '姓名': 'patient_name',
                # 手术日期相关
                '手术日期': 'surgery_date', '手术时间': 'surgery_date', '日期': 'surgery_date',
                # 手术相关字段
                '手术类型': 'surgery_type', '手术名称': 'surgery_name',
                '麻醉方式': 'anesthesia_method', '麻醉方法': 'anesthesia_method',
                '麻醉医师': 'anesthesiologist', '麻醉医生': 'anesthesiologist',
                '主刀医师': 'surgeon', '主刀医生': 'surgeon', '外科医师': 'surgeon',
                '年龄': 'age', '性别': 'gender'
            }

            # 重命名列
            df_renamed = df.rename(columns=column_mapping)

            # 记录映射结果
            for old_name, new_name in column_mapping.items():
                if old_name in df.columns:
                    logger.info(f"🏷️ 标准化列名: {old_name} -> {new_name}")

            return df_renamed

        except Exception as e:
            logger.error(f"标准化列名失败: {str(e)}")
            return df

    def _validate_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证数据质量"""
        errors = []
        error_rows = []

        try:
            # 检查必填字段
            required_fields = ['patient_id', 'patient_name', 'surgery_date']
            for field in required_fields:
                if field in df.columns:
                    null_count = df[field].isnull().sum()
                    if null_count > 0:
                        errors.append(f"字段 {field} 有 {null_count} 个空值")
                        # 记录有空值的行
                        null_rows = df[df[field].isnull()].index.tolist()
                        error_rows.extend(null_rows)

            # 检查日期格式
            if 'surgery_date' in df.columns:
                try:
                    pd.to_datetime(df['surgery_date'], errors='coerce')
                    invalid_dates = df[pd.to_datetime(df['surgery_date'], errors='coerce').isnull()].index.tolist()
                    if invalid_dates:
                        errors.append(f"发现 {len(invalid_dates)} 个无效日期")
                        error_rows.extend(invalid_dates)
                except Exception:
                    errors.append("手术日期格式验证失败")

            # 去重错误行索引
            error_rows = list(set(error_rows))

            return {
                'errors': errors,
                'error_rows': error_rows,
                'valid_rows': len(df) - len(error_rows)
            }

        except Exception as e:
            logger.error(f"数据验证失败: {str(e)}")
            return {
                'errors': [f"验证过程出错: {str(e)}"],
                'error_rows': [],
                'valid_rows': len(df)
            }

    def _process_and_import_data(self, df: pd.DataFrame, batch_id: str,
                               privacy_enabled: bool, import_config: Dict[str, Any]) -> Dict[str, Any]:
        """处理并导入数据 - 增强版本"""
        try:
            batch_size = import_config.get('batchSize', 500)
            skip_duplicates = import_config.get('skipDuplicates', True)
            duplicate_strategy = import_config.get('duplicateStrategy', 'skip')

            total_records = len(df)
            new_records = 0
            duplicate_records = 0
            error_records = 0

            logger.info(f"📦 开始批量处理，批次大小: {batch_size}")

            # 分批处理数据
            for i in range(0, total_records, batch_size):
                batch_df = df.iloc[i:i + batch_size]
                logger.info(f"🔄 处理批次 {i//batch_size + 1}: {len(batch_df)} 条记录")

                try:
                    # 处理当前批次
                    batch_result = self._process_batch(
                        batch_df, batch_id, privacy_enabled,
                        skip_duplicates, duplicate_strategy
                    )

                    new_records += batch_result['new_records']
                    duplicate_records += batch_result['duplicate_records']
                    error_records += batch_result['error_records']

                except Exception as e:
                    logger.error(f"批次处理失败: {str(e)}")
                    error_records += len(batch_df)

            return {
                'total_records': total_records,
                'new_records': new_records,
                'duplicate_records': duplicate_records,
                'error_records': error_records,
                'success_rate': (new_records / total_records * 100) if total_records > 0 else 0
            }

        except Exception as e:
            logger.error(f"数据处理和导入失败: {str(e)}")
            raise e

    def _process_batch(self, batch_df: pd.DataFrame, batch_id: str,
                      privacy_enabled: bool, skip_duplicates: bool,
                      duplicate_strategy: str) -> Dict[str, int]:
        """处理单个批次的数据"""
        new_records = 0
        duplicate_records = 0
        error_records = 0

        for index, row in batch_df.iterrows():
            try:
                # 转换为字典
                record_data = row.to_dict()

                # 数据脱敏（如果启用）
                if privacy_enabled:
                    record_data = self._apply_privacy_protection(record_data)

                # 检查重复记录
                if skip_duplicates:
                    is_duplicate = self._check_duplicate_record(record_data)
                    if is_duplicate:
                        if duplicate_strategy == 'skip':
                            duplicate_records += 1
                            continue
                        elif duplicate_strategy == 'update':
                            # 更新现有记录
                            self._update_existing_record(record_data)
                            duplicate_records += 1
                            continue
                        # 'create' 策略继续创建新记录

                # 确保record_data包含batch_id
                record_data['import_batch_id'] = batch_id

                # 插入新记录
                self.db.insert_surgery_record(record_data)
                new_records += 1

            except Exception as e:
                logger.error(f"处理记录失败 (行 {index}): {str(e)}")
                error_records += 1

        return {
            'new_records': new_records,
            'duplicate_records': duplicate_records,
            'error_records': error_records
        }
