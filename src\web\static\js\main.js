/**
 * 麻醉质控数据管理系统 - 主要JavaScript功能
 */

// 全局配置
const CONFIG = {
    THEME_KEY: 'theme',
    DEFAULT_THEME: 'corporate',
    ANIMATION_DURATION: 300,
    DEBOUNCE_DELAY: 300
};

// 主题管理
class ThemeManager {
    constructor() {
        this.themes = [
            'light', 'dark', 'cupcake', 'bumblebee', 'emerald',
            'corporate', 'synthwave', 'retro', 'cyberpunk', 'valentine',
            'halloween', 'garden', 'forest', 'aqua', 'lofi',
            'pastel', 'fantasy', 'wireframe', 'black', 'luxury',
            'dracula', 'cmyk', 'autumn', 'business', 'acid',
            'lemonade', 'night', 'coffee', 'winter'
        ];
        this.currentTheme = this.getStoredTheme() || CONFIG.DEFAULT_THEME;
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.updateThemeIndicators();
        this.bindEvents();
    }

    getStoredTheme() {
        return localStorage.getItem(CONFIG.THEME_KEY);
    }

    setStoredTheme(theme) {
        localStorage.setItem(CONFIG.THEME_KEY, theme);
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        this.setStoredTheme(theme);

        // 更新CSS变量
        this.updateCSSVariables();
    }

    updateCSSVariables() {
        // 强制重新计算CSS变量
        const root = document.documentElement;
        const computedStyle = getComputedStyle(root);

        // 更新自定义变量
        root.style.setProperty('--chart-bg', `hsl(${computedStyle.getPropertyValue('--b2')})`);
        root.style.setProperty('--chart-placeholder', `hsl(${computedStyle.getPropertyValue('--bc')} / 0.4)`);
        root.style.setProperty('--activity-bg', `hsl(${computedStyle.getPropertyValue('--b2')})`);
        root.style.setProperty('--hover-bg', `hsl(${computedStyle.getPropertyValue('--b3')})`);
        root.style.setProperty('--border-color', `hsl(${computedStyle.getPropertyValue('--b3')})`);
    }

    setTheme(theme) {
        if (this.themes.includes(theme)) {
            this.currentTheme = theme;
            this.applyTheme(theme);
            this.updateThemeIndicators();

            // 触发主题变更事件
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme }
            }));

            this.showNotification(`已切换到${this.getThemeName(theme)}`, 'success');
        }
    }

    getCurrentTheme() {
        return this.currentTheme;
    }

    updateThemeIndicators() {
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            const theme = option.getAttribute('data-theme');
            option.classList.toggle('active', theme === this.currentTheme);
        });
    }

    bindEvents() {
        // 主题切换事件（向后兼容）
        window.setTheme = (theme) => {
            this.setTheme(theme);
        };
    }

    getThemeName(theme) {
        const themeNames = {
            'light': '浅色主题',
            'dark': '深色主题',
            'cupcake': '温馨主题',
            'bumblebee': '大黄蜂主题',
            'emerald': '翡翠主题',
            'corporate': '商务主题',
            'synthwave': '科技主题',
            'retro': '复古主题',
            'cyberpunk': '赛博朋克',
            'valentine': '情人节主题',
            'halloween': '万圣节主题',
            'garden': '花园主题',
            'forest': '森林主题',
            'aqua': '海洋主题',
            'lofi': '低保真主题',
            'pastel': '粉彩主题',
            'fantasy': '幻想主题',
            'wireframe': '线框主题',
            'black': '黑色主题',
            'luxury': '奢华主题',
            'dracula': '德古拉主题',
            'cmyk': 'CMYK主题',
            'autumn': '秋季主题',
            'business': '商业主题',
            'acid': '酸性主题',
            'lemonade': '柠檬水主题',
            'night': '夜晚主题',
            'coffee': '咖啡主题',
            'winter': '冬季主题'
        };
        return themeNames[theme] || theme;
    }

    showNotification(message, type = 'info') {
        // 使用新的通知管理器
        if (window.notificationManager) {
            return window.notificationManager.show(message, type);
        } else {
            // 降级到简单通知
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    getNotificationIcon(type) {
        const icons = {
            'info': 'info-circle',
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'times-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// 导航管理
class NavigationManager {
    constructor() {
        this.isCollapsed = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateActiveNavigation();
        this.restoreCollapseState();
    }

    bindEvents() {
        // 导航项点击事件
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                this.handleNavClick(e, item);
            });
        });

        // 监听导航栏收缩事件
        window.addEventListener('sidebarToggle', (e) => {
            this.isCollapsed = e.detail.collapsed;
            this.updateLayout();
        });
    }

    restoreCollapseState() {
        // 状态恢复现在在base.html中处理
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            this.isCollapsed = sidebar.classList.contains('sidebar-collapsed');
        }
    }

    updateLayout() {
        // 布局会通过CSS自动调整
    }

    updateActiveNavigation() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    handleNavClick(e, item) {
        // 添加点击动画效果
        item.style.transform = 'scale(0.95)';
        setTimeout(() => {
            item.style.transform = '';
        }, 150);
    }
}

// 工具函数
class Utils {
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    static formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num);
    }

    static formatDate(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hour = String(d.getHours()).padStart(2, '0');
        const minute = String(d.getMinutes()).padStart(2, '0');
        const second = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hour)
            .replace('mm', minute)
            .replace('ss', second);
    }

    static showLoading(element) {
        if (element) {
            element.innerHTML = '<div class="loading-spinner mx-auto"></div>';
        }
    }

    static hideLoading(element, originalContent) {
        if (element) {
            element.innerHTML = originalContent;
        }
    }

    static copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            themeManager.showNotification('已复制到剪贴板', 'success');
        }).catch(() => {
            themeManager.showNotification('复制失败', 'error');
        });
    }
}

// 表格增强
class TableEnhancer {
    constructor(tableSelector) {
        this.table = document.querySelector(tableSelector);
        if (this.table) {
            this.init();
        }
    }

    init() {
        this.addSortingFeature();
        this.addSearchFeature();
        this.addPaginationFeature();
    }

    addSortingFeature() {
        const headers = this.table.querySelectorAll('thead th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(header);
            });
        });
    }

    sortTable(header) {
        const column = header.cellIndex;
        const isAscending = header.classList.contains('sort-asc');
        const tbody = this.table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        rows.sort((a, b) => {
            const aText = a.cells[column].textContent.trim();
            const bText = b.cells[column].textContent.trim();
            
            if (isAscending) {
                return bText.localeCompare(aText, 'zh-CN', { numeric: true });
            } else {
                return aText.localeCompare(bText, 'zh-CN', { numeric: true });
            }
        });

        // 更新排序指示器
        this.table.querySelectorAll('thead th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');

        // 重新排列行
        rows.forEach(row => tbody.appendChild(row));
    }

    addSearchFeature() {
        // 搜索功能将在具体页面中实现
    }

    addPaginationFeature() {
        // 分页功能将在具体页面中实现
    }
}

// 立即初始化主题管理器（不等待DOM）
window.themeManager = new ThemeManager();

// 其他组件在DOM加载后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化导航管理器
    window.navigationManager = new NavigationManager();

    // 初始化表格增强器
    window.tableEnhancer = new TableEnhancer('.table-custom');

    // 添加页面加载动画
    if (document.body) {
        document.body.classList.add('fade-in');
    }

    // 绑定全局事件
    window.addEventListener('resize', Utils.debounce(() => {
        // 处理窗口大小变化
        if (window.navigationManager) {
            window.navigationManager.updateLayout();
        }
    }, CONFIG.DEBOUNCE_DELAY));
});

// 导出工具类供其他脚本使用
window.Utils = Utils;
window.CONFIG = CONFIG;
