<!-- SPA版本的患者编辑页面 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">编辑患者</h1>
            <p class="text-base-content/60 mt-1">修改患者的基本信息</p>
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline" onclick="cancelEdit()">
                <i class="fas fa-times mr-2"></i>取消
            </button>
            <button class="btn btn-primary" onclick="savePatient()">
                <i class="fas fa-save mr-2"></i>保存
            </button>
        </div>
    </div>

    <!-- 编辑表单 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-user-edit text-primary mr-2"></i>基本信息
            </h2>
            
            <form id="patient-form" class="space-y-6 mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">患者ID</span>
                        </label>
                        <input type="text" 
                               id="patient-id" 
                               class="input input-bordered bg-base-200" 
                               value="P{{ patient_id or '001' }}" 
                               readonly>
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">姓名 <span class="text-error">*</span></span>
                        </label>
                        <input type="text" 
                               id="patient-name" 
                               class="input input-bordered" 
                               value="张三" 
                               required>
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">性别 <span class="text-error">*</span></span>
                        </label>
                        <select id="patient-gender" class="select select-bordered" required>
                            <option value="">请选择性别</option>
                            <option value="男" selected>男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">出生日期 <span class="text-error">*</span></span>
                        </label>
                        <input type="date" 
                               id="patient-birth" 
                               class="input input-bordered" 
                               value="1979-01-15" 
                               required>
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">联系电话</span>
                        </label>
                        <input type="tel" 
                               id="patient-phone" 
                               class="input input-bordered" 
                               value="13812341234" 
                               placeholder="请输入手机号码">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">身份证号</span>
                        </label>
                        <input type="text" 
                               id="patient-id-card" 
                               class="input input-bordered" 
                               value="320123197901151234" 
                               placeholder="请输入身份证号码">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">紧急联系人</span>
                        </label>
                        <input type="text" 
                               id="emergency-contact" 
                               class="input input-bordered" 
                               value="李四" 
                               placeholder="请输入紧急联系人姓名">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">紧急联系人电话</span>
                        </label>
                        <input type="tel" 
                               id="emergency-phone" 
                               class="input input-bordered" 
                               value="13987654321" 
                               placeholder="请输入紧急联系人电话">
                    </div>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">家庭住址</span>
                    </label>
                    <textarea id="patient-address" 
                              class="textarea textarea-bordered h-24" 
                              placeholder="请输入详细地址">江苏省南京市玄武区某某街道123号</textarea>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">过敏史</span>
                    </label>
                    <textarea id="allergy-history" 
                              class="textarea textarea-bordered h-24" 
                              placeholder="请输入过敏史，如无过敏史请填写'无'">青霉素过敏</textarea>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">既往病史</span>
                    </label>
                    <textarea id="medical-history" 
                              class="textarea textarea-bordered h-24" 
                              placeholder="请输入既往病史，如无病史请填写'无'">高血压病史3年</textarea>
                </div>
            </form>
        </div>
    </div>

    <!-- 操作记录 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-history text-primary mr-2"></i>操作记录
            </h2>
            
            <div class="overflow-x-auto mt-4">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>操作时间</th>
                            <th>操作类型</th>
                            <th>操作人员</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024-01-15 14:30</td>
                            <td><span class="badge badge-info">信息修改</span></td>
                            <td>管理员</td>
                            <td>更新联系电话</td>
                        </tr>
                        <tr>
                            <td>2024-01-10 09:15</td>
                            <td><span class="badge badge-success">新增患者</span></td>
                            <td>护士张三</td>
                            <td>初次录入患者信息</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// 患者编辑页面功能
function savePatient() {
    const form = document.getElementById('patient-form');
    const formData = new FormData(form);
    
    // 验证必填字段
    const requiredFields = ['patient-name', 'patient-gender', 'patient-birth'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('input-error');
            isValid = false;
        } else {
            field.classList.remove('input-error');
        }
    });
    
    if (!isValid) {
        themeManager.showNotification('请填写所有必填字段', 'error');
        return;
    }

    // 模拟保存
    themeManager.showNotification('正在保存...', 'info');

    setTimeout(() => {
        themeManager.showNotification('患者信息保存成功', 'success');
        
        // 返回患者详情页面
        const patientId = {{ patient_id or 1 }};
        if (window.router) {
            window.router.navigate(`/patients/${patientId}`);
        } else {
            window.location.href = `/patients/${patientId}`;
        }
    }, 1000);
}

function cancelEdit() {
    if (confirm('确定要取消编辑吗？未保存的更改将丢失。')) {
        const patientId = {{ patient_id or 1 }};
        if (window.router) {
            window.router.navigate(`/patients/${patientId}`);
        } else {
            window.location.href = `/patients/${patientId}`;
        }
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('患者编辑页面加载完成');
    
    // 绑定表单验证事件
    const form = document.getElementById('patient-form');
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('input-error');
            } else {
                this.classList.remove('input-error');
            }
        });
    });
});
</script>
