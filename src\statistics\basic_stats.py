"""
基础统计模块
"""
from typing import Dict, Any, List, Tuple
from database.models import DatabaseManager
from utils.logger import logger


class BasicStatistics:
    """基础统计分析"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_overview_stats(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取概览统计数据"""
        logger.info(f"获取概览统计: {start_date} - {end_date}")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 总体统计
            cursor = conn.execute(f'''
                SELECT 
                    COUNT(*) as total_surgeries,
                    COUNT(DISTINCT patient_id) as total_patients,
                    COUNT(DISTINCT anesthesiologist) as total_anesthesiologists,
                    COUNT(DISTINCT surgeon) as total_surgeons,
                    COUNT(DISTINCT department) as total_departments
                FROM surgery_records {where_clause}
            ''', params)
            
            overview = cursor.fetchone()
            
            # 今日统计
            today_cursor = conn.execute('''
                SELECT COUNT(*) 
                FROM surgery_records 
                WHERE DATE(surgery_date) = DATE('now')
            ''')
            today_count = today_cursor.fetchone()[0]
            
            # 本月统计
            month_cursor = conn.execute('''
                SELECT COUNT(*) 
                FROM surgery_records 
                WHERE strftime('%Y-%m', surgery_date) = strftime('%Y-%m', 'now')
            ''')
            month_count = month_cursor.fetchone()[0]
            
            # 本年统计
            year_cursor = conn.execute('''
                SELECT COUNT(*) 
                FROM surgery_records 
                WHERE strftime('%Y', surgery_date) = strftime('%Y', 'now')
            ''')
            year_count = year_cursor.fetchone()[0]
            
            return {
                'total_surgeries': overview[0] or 0,
                'total_patients': overview[1] or 0,
                'total_anesthesiologists': overview[2] or 0,
                'total_surgeons': overview[3] or 0,
                'total_departments': overview[4] or 0,
                'today_surgeries': today_count or 0,
                'month_surgeries': month_count or 0,
                'year_surgeries': year_count or 0
            }
    
    def get_age_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取年龄分布统计"""
        logger.info("获取年龄分布统计")

        # 注意：当前数据库中没有age字段，返回空分布
        # 如果需要年龄统计，需要在数据导入时添加年龄字段
        logger.warning("数据库中没有age字段，返回空的年龄分布")

        return {
            '未成年(<18岁)': 0,
            '青年(18-30岁)': 0,
            '中年(31-50岁)': 0,
            '中老年(51-70岁)': 0,
            '老年(>70岁)': 0,
            '未知': 0
        }
    
    def get_gender_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取性别分布统计"""
        logger.info("获取性别分布统计")
        
        # 注意：当前数据库中没有gender字段，返回空分布
        # 如果需要性别统计，需要在数据导入时添加性别字段
        logger.warning("数据库中没有gender字段，返回空的性别分布")

        return {
            '男性': 0,
            '女性': 0,
            '未知': 0
        }
    
    def get_surgery_level_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取手术级别分布统计"""
        logger.info("获取手术级别分布统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    COALESCE(surgery_level, '未知') as level,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY surgery_level
                ORDER BY count DESC
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def get_incision_level_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取切口等级分布统计"""
        logger.info("获取切口等级分布统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    COALESCE(incision_level, '未知') as level,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY incision_level
                ORDER BY 
                    CASE 
                        WHEN incision_level = 'Ⅰ级' THEN 1
                        WHEN incision_level = 'Ⅱ级' THEN 2
                        WHEN incision_level = 'Ⅲ级' THEN 3
                        WHEN incision_level = 'Ⅳ级' THEN 4
                        ELSE 5
                    END
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def get_position_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取体位分布统计"""
        logger.info("获取体位分布统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_where_clause(
                start_date, end_date,
                ["position IS NOT NULL", "position != ''"]
            )

            cursor = conn.execute(f'''
                SELECT
                    COALESCE(position, '未知') as position,
                    COUNT(*) as count
                FROM surgery_records
                {where_clause}
                GROUP BY position
                ORDER BY count DESC
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def get_postoperative_analgesia_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取术后镇痛分布统计"""
        logger.info("获取术后镇痛分布统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    CASE 
                        WHEN postoperative_analgesia = '否' OR postoperative_analgesia = '' OR postoperative_analgesia IS NULL THEN '无镇痛'
                        ELSE postoperative_analgesia
                    END as analgesia_type,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY analgesia_type
                ORDER BY count DESC
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def _build_date_filter(self, start_date: str = None, end_date: str = None) -> Tuple[str, List]:
        """构建日期过滤条件"""
        conditions = []
        params = []

        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]

        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""

        return where_clause, params

    def _build_where_clause(self, start_date: str = None, end_date: str = None, additional_conditions: List[str] = None) -> Tuple[str, List]:
        """构建完整的WHERE子句"""
        conditions = []
        params = []

        # 添加日期条件
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]

        # 添加额外条件
        if additional_conditions:
            conditions.extend(additional_conditions)

        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""

        return where_clause, params
