<!-- SPA版本的新增患者内容 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">新增患者</h1>
            <p class="text-base-content/60 mt-1">添加新的患者信息</p>
        </div>
        <div class="flex space-x-2">
            <a href="/patients" class="btn btn-outline" onclick="router.navigate('/patients'); return false;">
                <i class="fas fa-arrow-left mr-2"></i>
                返回列表
            </a>
        </div>
    </div>

    <!-- 患者信息表单 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h3 class="card-title mb-6">基本信息</h3>
            
            <form id="patient-form" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 姓名 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">患者姓名 <span class="text-error">*</span></span>
                        </label>
                        <input type="text" name="name" class="input input-bordered" placeholder="请输入患者姓名" required>
                    </div>
                    
                    <!-- 性别 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">性别 <span class="text-error">*</span></span>
                        </label>
                        <select name="gender" class="select select-bordered" required>
                            <option value="">请选择性别</option>
                            <option value="male">男</option>
                            <option value="female">女</option>
                        </select>
                    </div>
                    
                    <!-- 出生日期 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">出生日期 <span class="text-error">*</span></span>
                        </label>
                        <input type="date" name="birth_date" class="input input-bordered" required>
                    </div>
                    
                    <!-- 身份证号 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">身份证号</span>
                        </label>
                        <input type="text" name="id_card" class="input input-bordered" placeholder="请输入身份证号">
                    </div>
                    
                    <!-- 联系电话 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">联系电话 <span class="text-error">*</span></span>
                        </label>
                        <input type="tel" name="phone" class="input input-bordered" placeholder="请输入联系电话" required>
                    </div>
                    
                    <!-- 紧急联系人 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">紧急联系人</span>
                        </label>
                        <input type="text" name="emergency_contact" class="input input-bordered" placeholder="请输入紧急联系人">
                    </div>
                    
                    <!-- 紧急联系电话 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">紧急联系电话</span>
                        </label>
                        <input type="tel" name="emergency_phone" class="input input-bordered" placeholder="请输入紧急联系电话">
                    </div>
                    
                    <!-- 血型 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">血型</span>
                        </label>
                        <select name="blood_type" class="select select-bordered">
                            <option value="">请选择血型</option>
                            <option value="A">A型</option>
                            <option value="B">B型</option>
                            <option value="AB">AB型</option>
                            <option value="O">O型</option>
                        </select>
                    </div>
                </div>
                
                <!-- 地址 -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">联系地址</span>
                    </label>
                    <textarea name="address" class="textarea textarea-bordered" placeholder="请输入详细地址" rows="3"></textarea>
                </div>
                
                <!-- 过敏史 -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">过敏史</span>
                    </label>
                    <textarea name="allergies" class="textarea textarea-bordered" placeholder="请输入过敏史，如无过敏史请填写"无"" rows="3"></textarea>
                </div>
                
                <!-- 既往病史 -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">既往病史</span>
                    </label>
                    <textarea name="medical_history" class="textarea textarea-bordered" placeholder="请输入既往病史" rows="3"></textarea>
                </div>
                
                <!-- 备注 -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">备注</span>
                    </label>
                    <textarea name="notes" class="textarea textarea-bordered" placeholder="其他需要说明的信息" rows="3"></textarea>
                </div>
                
                <!-- 提交按钮 -->
                <div class="flex justify-end space-x-4 pt-6">
                    <button type="button" class="btn btn-outline" onclick="resetForm()">
                        <i class="fas fa-undo mr-2"></i>
                        重置
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i>
                        保存患者信息
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 表单提交处理
document.getElementById('patient-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // 获取表单数据
    const formData = new FormData(this);
    const patientData = {};
    
    for (let [key, value] of formData.entries()) {
        patientData[key] = value;
    }
    
    // 验证必填字段
    if (!patientData.name || !patientData.gender || !patientData.birth_date || !patientData.phone) {
        window.utils.showNotification('请填写所有必填字段', 'error');
        return;
    }
    
    // 验证电话号码格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(patientData.phone)) {
        window.utils.showNotification('请输入正确的手机号码', 'error');
        return;
    }
    
    // 模拟保存
    savePatient(patientData);
});

function savePatient(patientData) {
    // 显示保存中状态
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<div class="loading loading-spinner loading-sm mr-2"></div>保存中...';
    submitBtn.disabled = true;
    
    // 模拟API调用
    setTimeout(() => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // 显示成功消息
        window.utils.showNotification('患者信息保存成功', 'success');
        
        // 询问是否继续添加
        if (confirm('患者信息已保存成功！是否继续添加新患者？')) {
            resetForm();
        } else {
            // 跳转到患者列表
            router.navigate('/patients');
        }
    }, 2000);
}

function resetForm() {
    document.getElementById('patient-form').reset();
    window.utils.showNotification('表单已重置', 'info');
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('新增患者页面加载完成');
    
    // 设置默认日期为今天
    const today = new Date().toISOString().split('T')[0];
    // 不设置默认出生日期，让用户自己选择
});
</script>
