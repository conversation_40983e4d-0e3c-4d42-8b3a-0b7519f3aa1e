"""
统计服务总入口
整合所有统计模块，提供统一的接口
"""
from typing import Dict, Any, List, Optional
from .basic_stats import BasicStatistics
from .anesthesia_stats import AnesthesiaStatistics
from .surgery_stats import SurgeryStatistics
from .time_stats import TimeStatistics
from .quality_stats import QualityStatistics
from .department_stats import DepartmentStatistics
from .staff_stats import StaffStatistics
from .trend_stats import TrendStatistics
from utils.logger import logger


class StatisticsService:
    """统计服务总入口"""
    
    def __init__(self):
        self.basic_stats = BasicStatistics()
        self.anesthesia_stats = AnesthesiaStatistics()
        self.surgery_stats = SurgeryStatistics()
        self.time_stats = TimeStatistics()
        self.quality_stats = QualityStatistics()
        self.department_stats = DepartmentStatistics()
        self.staff_stats = StaffStatistics()
        self.trend_stats = TrendStatistics()
    
    def get_dashboard_data(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取仪表盘数据 - 优化版本"""
        logger.info("获取仪表盘统计数据")

        try:
            # 使用并发获取数据以提高性能
            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                # 并发执行多个查询
                future_overview = executor.submit(self.basic_stats.get_overview_stats, start_date, end_date)
                future_anesthesia = executor.submit(self.anesthesia_stats.get_anesthesia_method_distribution, start_date, end_date)
                future_surgery_types = executor.submit(self.surgery_stats.get_surgery_type_distribution, start_date, end_date)
                future_department = executor.submit(self.department_stats.get_department_overview, start_date, end_date)

                # 获取结果
                overview = future_overview.result()
                anesthesia_methods = future_anesthesia.result()
                surgery_types = future_surgery_types.result()
                department_overview = future_department.result()

            # 处理科室分布数据
            department_distribution = {dept['department']: dept['surgery_count'] for dept in department_overview[:10]}

            # 获取其他数据（这些查询相对较快）
            age_distribution = self.basic_stats.get_age_distribution(start_date, end_date)
            gender_distribution = self.basic_stats.get_gender_distribution(start_date, end_date)
            daily_trend = self.time_stats.get_daily_surgery_trend(start_date, end_date)
            quality_score = self.quality_stats.get_data_quality_score(start_date, end_date)
            
            return {
                'overview': overview,
                'anesthesia_methods': anesthesia_methods,
                'surgery_types': surgery_types,
                'department_distribution': department_distribution,
                'age_distribution': age_distribution,
                'gender_distribution': gender_distribution,
                'daily_trend': daily_trend[-30:],  # 最近30天
                'quality_score': quality_score
            }
            
        except Exception as e:
            logger.error(f"获取仪表盘数据失败: {str(e)}")
            return {
                'error': str(e),
                'overview': {},
                'anesthesia_methods': {},
                'surgery_types': {},
                'department_distribution': {},
                'age_distribution': {},
                'gender_distribution': {},
                'daily_trend': [],
                'quality_score': {}
            }
    
    def get_comprehensive_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取综合分析数据"""
        logger.info("获取综合分析数据")
        
        try:
            return {
                # 基础统计
                'basic': {
                    'overview': self.basic_stats.get_overview_stats(start_date, end_date),
                    'age_distribution': self.basic_stats.get_age_distribution(start_date, end_date),
                    'gender_distribution': self.basic_stats.get_gender_distribution(start_date, end_date),
                    'surgery_level_distribution': self.basic_stats.get_surgery_level_distribution(start_date, end_date),
                    'incision_level_distribution': self.basic_stats.get_incision_level_distribution(start_date, end_date),
                    'position_distribution': self.basic_stats.get_position_distribution(start_date, end_date),
                    'analgesia_distribution': self.basic_stats.get_postoperative_analgesia_distribution(start_date, end_date)
                },
                
                # 麻醉统计
                'anesthesia': {
                    'method_distribution': self.anesthesia_stats.get_anesthesia_method_distribution(start_date, end_date),
                    'method_by_age': self.anesthesia_stats.get_anesthesia_method_by_age(start_date, end_date),
                    'method_by_surgery_type': self.anesthesia_stats.get_anesthesia_method_by_surgery_type(start_date, end_date),
                    'anesthesiologist_workload': self.anesthesia_stats.get_anesthesiologist_workload(start_date, end_date),
                    'duration_analysis': self.anesthesia_stats.get_anesthesia_duration_analysis(start_date, end_date),
                    'complications': self.anesthesia_stats.get_anesthesia_complications(start_date, end_date)
                },
                
                # 手术统计
                'surgery': {
                    'type_distribution': self.surgery_stats.get_surgery_type_distribution(start_date, end_date),
                    'name_top': self.surgery_stats.get_surgery_name_top(start_date, end_date),
                    'room_utilization': self.surgery_stats.get_operating_room_utilization(start_date, end_date),
                    'surgeon_workload': self.surgery_stats.get_surgeon_workload(start_date, end_date),
                    'assistant_analysis': self.surgery_stats.get_assistant_analysis(start_date, end_date),
                    'diagnosis_analysis': self.surgery_stats.get_diagnosis_analysis(start_date, end_date),
                    'pathological_specimen': self.surgery_stats.get_pathological_specimen_analysis(start_date, end_date)
                },
                
                # 时间统计
                'time': {
                    'daily_trend': self.time_stats.get_daily_surgery_trend(start_date, end_date),
                    'monthly_trend': self.time_stats.get_monthly_surgery_trend(start_date, end_date),
                    'weekday_distribution': self.time_stats.get_weekday_distribution(start_date, end_date),
                    'hourly_distribution': self.time_stats.get_hourly_distribution(start_date, end_date),
                    'duration_analysis': self.time_stats.get_surgery_duration_analysis(start_date, end_date),
                    'efficiency_analysis': self.time_stats.get_time_efficiency_analysis(start_date, end_date)
                },
                
                # 质量统计
                'quality': {
                    'completeness_analysis': self.quality_stats.get_data_completeness_analysis(start_date, end_date),
                    'consistency_analysis': self.quality_stats.get_data_consistency_analysis(start_date, end_date),
                    'quality_score': self.quality_stats.get_data_quality_score(start_date, end_date)
                },
                
                # 科室统计
                'department': {
                    'overview': self.department_stats.get_department_overview(start_date, end_date),
                    'anesthesia_methods': self.department_stats.get_department_anesthesia_methods(start_date, end_date),
                    'surgery_types': self.department_stats.get_department_surgery_types(start_date, end_date),
                    'workload_trend': self.department_stats.get_department_workload_trend(start_date, end_date),
                    'quality_indicators': self.department_stats.get_department_quality_indicators(start_date, end_date),
                    'comparison': self.department_stats.get_department_comparison(start_date, end_date)
                },
                
                # 人员统计
                'staff': {
                    'overview': self.staff_stats.get_staff_overview(start_date, end_date),
                    'anesthesiologist_performance': self.staff_stats.get_anesthesiologist_performance(start_date, end_date),
                    'surgeon_performance': self.staff_stats.get_surgeon_performance(start_date, end_date),
                    'nurse_workload': self.staff_stats.get_nurse_workload(start_date, end_date),
                    'team_collaboration': self.staff_stats.get_team_collaboration_analysis(start_date, end_date),
                    'specialization': self.staff_stats.get_staff_specialization_analysis(start_date, end_date)
                },
                
                # 趋势统计
                'trend': {
                    'yearly_trend': self.trend_stats.get_yearly_trend(),
                    'quarterly_trend': self.trend_stats.get_quarterly_trend(),
                    'monthly_trend_detailed': self.trend_stats.get_monthly_trend_detailed(),
                    'anesthesia_method_trend': self.trend_stats.get_anesthesia_method_trend(),
                    'department_trend': self.trend_stats.get_department_trend(),
                    'quality_trend': self.trend_stats.get_quality_trend(),
                    'workload_forecast': self.trend_stats.get_workload_forecast()
                }
            }
            
        except Exception as e:
            logger.error(f"获取综合分析数据失败: {str(e)}")
            return {'error': str(e)}
    
    def get_custom_analysis(self, analysis_type: str, start_date: str = None, end_date: str = None, **kwargs) -> Dict[str, Any]:
        """获取自定义分析数据"""
        logger.info(f"获取自定义分析数据: {analysis_type}")
        
        try:
            if analysis_type == 'anesthesia_performance':
                return {
                    'anesthesiologist_workload': self.anesthesia_stats.get_anesthesiologist_workload(start_date, end_date),
                    'method_distribution': self.anesthesia_stats.get_anesthesia_method_distribution(start_date, end_date),
                    'duration_analysis': self.anesthesia_stats.get_anesthesia_duration_analysis(start_date, end_date),
                    'complications': self.anesthesia_stats.get_anesthesia_complications(start_date, end_date)
                }
            
            elif analysis_type == 'surgery_efficiency':
                return {
                    'surgeon_workload': self.surgery_stats.get_surgeon_workload(start_date, end_date),
                    'room_utilization': self.surgery_stats.get_operating_room_utilization(start_date, end_date),
                    'duration_analysis': self.time_stats.get_surgery_duration_analysis(start_date, end_date),
                    'efficiency_analysis': self.time_stats.get_time_efficiency_analysis(start_date, end_date)
                }
            
            elif analysis_type == 'department_comparison':
                return {
                    'overview': self.department_stats.get_department_overview(start_date, end_date),
                    'comparison': self.department_stats.get_department_comparison(start_date, end_date),
                    'quality_indicators': self.department_stats.get_department_quality_indicators(start_date, end_date),
                    'workload_trend': self.department_stats.get_department_workload_trend(start_date, end_date)
                }
            
            elif analysis_type == 'quality_assessment':
                return {
                    'completeness_analysis': self.quality_stats.get_data_completeness_analysis(start_date, end_date),
                    'consistency_analysis': self.quality_stats.get_data_consistency_analysis(start_date, end_date),
                    'quality_score': self.quality_stats.get_data_quality_score(start_date, end_date),
                    'quality_trend': self.trend_stats.get_quality_trend()
                }
            
            elif analysis_type == 'trend_forecast':
                return {
                    'yearly_trend': self.trend_stats.get_yearly_trend(),
                    'monthly_trend': self.trend_stats.get_monthly_trend_detailed(),
                    'workload_forecast': self.trend_stats.get_workload_forecast(),
                    'anesthesia_method_trend': self.trend_stats.get_anesthesia_method_trend(),
                    'department_trend': self.trend_stats.get_department_trend()
                }
            
            else:
                return {'error': f'未知的分析类型: {analysis_type}'}
                
        except Exception as e:
            logger.error(f"获取自定义分析数据失败: {str(e)}")
            return {'error': str(e)}
    
    def get_export_data(self, export_type: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取导出数据"""
        logger.info(f"获取导出数据: {export_type}")
        
        try:
            if export_type == 'summary_report':
                return {
                    'overview': self.basic_stats.get_overview_stats(start_date, end_date),
                    'anesthesia_methods': self.anesthesia_stats.get_anesthesia_method_distribution(start_date, end_date),
                    'surgery_types': self.surgery_stats.get_surgery_type_distribution(start_date, end_date),
                    'department_overview': self.department_stats.get_department_overview(start_date, end_date),
                    'quality_score': self.quality_stats.get_data_quality_score(start_date, end_date)
                }
            
            elif export_type == 'detailed_analysis':
                return self.get_comprehensive_analysis(start_date, end_date)
            
            elif export_type == 'staff_performance':
                return {
                    'anesthesiologist_performance': self.staff_stats.get_anesthesiologist_performance(start_date, end_date),
                    'surgeon_performance': self.staff_stats.get_surgeon_performance(start_date, end_date),
                    'nurse_workload': self.staff_stats.get_nurse_workload(start_date, end_date),
                    'team_collaboration': self.staff_stats.get_team_collaboration_analysis(start_date, end_date)
                }
            
            else:
                return {'error': f'未知的导出类型: {export_type}'}
                
        except Exception as e:
            logger.error(f"获取导出数据失败: {str(e)}")
            return {'error': str(e)}
