<!-- SPA版本的DaisyUI测试内容 -->
<div class="space-y-8">
    <!-- 页面标题 -->
    <div class="text-center">
        <h1 class="text-4xl font-bold text-base-content mb-2">DaisyUI组件测试页面</h1>
        <p class="text-base-content/60">测试所有DaisyUI组件和主题切换功能</p>
    </div>

    <!-- 主题切换测试 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">主题切换测试</h2>
            <p class="text-sm text-base-content/60 mb-4">点击下方按钮测试不同主题</p>
            <div class="flex flex-wrap gap-2">
                <button class="btn btn-primary" onclick="setTheme('corporate')">商务主题</button>
                <button class="btn btn-secondary" onclick="setTheme('light')">浅色主题</button>
                <button class="btn btn-accent" onclick="setTheme('dark')">深色主题</button>
                <button class="btn btn-info" onclick="setTheme('cupcake')">温馨主题</button>
                <button class="btn btn-success" onclick="setTheme('emerald')">翡翠主题</button>
                <button class="btn btn-warning" onclick="setTheme('synthwave')">科技主题</button>
                <button class="btn btn-error" onclick="setTheme('forest')">森林主题</button>
                <button class="btn" onclick="setTheme('aqua')">海洋主题</button>
            </div>
            <div class="flex flex-wrap gap-2 mt-2">
                <button class="btn btn-outline" onclick="setTheme('retro')">复古主题</button>
                <button class="btn btn-outline" onclick="setTheme('cyberpunk')">赛博朋克</button>
                <button class="btn btn-outline" onclick="setTheme('valentine')">情人节</button>
                <button class="btn btn-outline" onclick="setTheme('halloween')">万圣节</button>
                <button class="btn btn-outline" onclick="setTheme('garden')">花园主题</button>
                <button class="btn btn-outline" onclick="setTheme('lofi')">低保真</button>
                <button class="btn btn-outline" onclick="setTheme('pastel')">粉彩主题</button>
                <button class="btn btn-outline" onclick="setTheme('fantasy')">幻想主题</button>
            </div>
            <div class="flex flex-wrap gap-2 mt-2">
                <button class="btn btn-outline" onclick="setTheme('wireframe')">线框主题</button>
                <button class="btn btn-outline" onclick="setTheme('black')">黑色主题</button>
                <button class="btn btn-outline" onclick="setTheme('luxury')">奢华主题</button>
                <button class="btn btn-outline" onclick="setTheme('dracula')">德古拉</button>
                <button class="btn btn-outline" onclick="setTheme('cmyk')">CMYK主题</button>
                <button class="btn btn-outline" onclick="setTheme('autumn')">秋季主题</button>
                <button class="btn btn-outline" onclick="setTheme('business')">商业主题</button>
                <button class="btn btn-outline" onclick="setTheme('acid')">酸性主题</button>
            </div>
            <div class="flex flex-wrap gap-2 mt-2">
                <button class="btn btn-outline" onclick="setTheme('lemonade')">柠檬水</button>
                <button class="btn btn-outline" onclick="setTheme('night')">夜晚主题</button>
                <button class="btn btn-outline" onclick="setTheme('coffee')">咖啡主题</button>
                <button class="btn btn-outline" onclick="setTheme('winter')">冬季主题</button>
                <button class="btn btn-outline" onclick="setTheme('bumblebee')">大黄蜂</button>
            </div>
        </div>
    </div>

    <!-- 按钮组件测试 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body space-y-4">
            <h2 class="card-title">按钮组件</h2>
            <div class="flex flex-wrap gap-2">
                <button class="btn btn-primary">Primary</button>
                <button class="btn btn-secondary">Secondary</button>
                <button class="btn btn-accent">Accent</button>
                <button class="btn btn-info">Info</button>
                <button class="btn btn-success">Success</button>
                <button class="btn btn-warning">Warning</button>
                <button class="btn btn-error">Error</button>
                <button class="btn btn-ghost">Ghost</button>
                <button class="btn btn-link">Link</button>
            </div>
            
            <div class="flex flex-wrap gap-2">
                <button class="btn btn-outline btn-primary">Outline Primary</button>
                <button class="btn btn-outline btn-secondary">Outline Secondary</button>
                <button class="btn btn-outline btn-accent">Outline Accent</button>
            </div>
            
            <div class="flex flex-wrap gap-2">
                <button class="btn btn-xs">Extra Small</button>
                <button class="btn btn-sm">Small</button>
                <button class="btn">Normal</button>
                <button class="btn btn-lg">Large</button>
            </div>
        </div>
    </div>

    <!-- 统计卡片测试 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">统计卡片</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="stat stat-primary rounded-lg">
                    <div class="stat-title">Primary Stat</div>
                    <div class="stat-value">1,234</div>
                    <div class="stat-desc">+12% from last month</div>
                </div>

                <div class="stat stat-secondary rounded-lg">
                    <div class="stat-title">Secondary Stat</div>
                    <div class="stat-value">567</div>
                    <div class="stat-desc">+8% from last week</div>
                </div>

                <div class="stat stat-accent rounded-lg">
                    <div class="stat-title">Accent Stat</div>
                    <div class="stat-value">89%</div>
                    <div class="stat-desc">Quality score</div>
                </div>

                <div class="stat stat-success rounded-lg">
                    <div class="stat-title">Success Stat</div>
                    <div class="stat-value">100%</div>
                    <div class="stat-desc">System uptime</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主题变量测试 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">主题变量测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-4">
                    <div class="chart-bg p-4 rounded-lg">
                        <p class="chart-placeholder">图表背景色测试</p>
                    </div>
                    <div class="activity-bg hover-bg p-4 rounded-lg cursor-pointer">
                        <p>活动背景色测试（悬停查看效果）</p>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="border theme-border p-4 rounded-lg">
                        <p>主题边框色测试</p>
                    </div>
                    <div class="text-shadow p-4">
                        <p class="text-lg font-bold">文字阴影测试</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 警告框测试 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body space-y-4">
            <h2 class="card-title">警告框组件</h2>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <span>这是一个信息提示框</span>
            </div>
            
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <span>这是一个成功提示框</span>
            </div>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <span>这是一个警告提示框</span>
            </div>
            
            <div class="alert alert-error">
                <i class="fas fa-times-circle"></i>
                <span>这是一个错误提示框</span>
            </div>
        </div>
    </div>

    <!-- 加载动画测试 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">加载动画</h2>
            <div class="flex flex-wrap gap-4 items-center">
                <span class="loading loading-spinner loading-xs"></span>
                <span class="loading loading-spinner loading-sm"></span>
                <span class="loading loading-spinner loading-md"></span>
                <span class="loading loading-spinner loading-lg"></span>
                
                <span class="loading loading-dots loading-xs"></span>
                <span class="loading loading-dots loading-sm"></span>
                <span class="loading loading-dots loading-md"></span>
                <span class="loading loading-dots loading-lg"></span>
                
                <span class="loading loading-ring loading-xs"></span>
                <span class="loading loading-ring loading-sm"></span>
                <span class="loading loading-ring loading-md"></span>
                <span class="loading loading-ring loading-lg"></span>
            </div>
        </div>
    </div>

    <!-- 功能测试 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">功能测试</h2>
            <div class="flex flex-wrap gap-2">
                <button class="btn btn-primary" onclick="testNotification('info')">测试信息通知</button>
                <button class="btn btn-success" onclick="testNotification('success')">测试成功通知</button>
                <button class="btn btn-warning" onclick="testNotification('warning')">测试警告通知</button>
                <button class="btn btn-error" onclick="testNotification('error')">测试错误通知</button>
                <button class="btn btn-ghost" onclick="testModal()">测试模态框</button>
                <button class="btn btn-outline" onclick="testSidebar()">测试侧边栏</button>
            </div>
        </div>
    </div>
</div>

<!-- 测试模态框 -->
<div id="test-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">测试模态框</h3>
        <p class="py-4">这是一个DaisyUI模态框组件的测试。</p>
        <div class="modal-action">
            <button class="btn" onclick="closeTestModal()">关闭</button>
            <button class="btn btn-primary" onclick="closeTestModal()">确定</button>
        </div>
    </div>
</div>

<script>
// 测试通知功能
function testNotification(type) {
    const messages = {
        'info': '这是一个信息通知',
        'success': '这是一个成功通知',
        'warning': '这是一个警告通知',
        'error': '这是一个错误通知'
    };
    
    if (window.utils) {
        window.utils.showNotification(messages[type], type);
    } else {
        alert(messages[type]);
    }
}

// 测试模态框
function testModal() {
    document.getElementById('test-modal').classList.add('modal-open');
}

function closeTestModal() {
    document.getElementById('test-modal').classList.remove('modal-open');
}

// 测试侧边栏
function testSidebar() {
    if (window.sidebarManager) {
        window.sidebarManager.toggle();
        window.utils.showNotification('侧边栏切换测试', 'info');
    } else {
        window.utils.showNotification('侧边栏管理器未初始化', 'error');
    }
}

// 主题设置函数
function setTheme(theme) {
    if (window.themeManager) {
        window.themeManager.setTheme(theme);
    } else {
        // 直接设置主题
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
    }
    
    window.utils.showNotification(`已切换到${getThemeName(theme)}`, 'success');
}

function getThemeName(theme) {
    const themeNames = {
        'light': '浅色主题',
        'dark': '深色主题',
        'cupcake': '温馨主题',
        'bumblebee': '大黄蜂主题',
        'emerald': '翡翠主题',
        'corporate': '商务主题',
        'synthwave': '科技主题',
        'retro': '复古主题',
        'cyberpunk': '赛博朋克',
        'valentine': '情人节主题',
        'halloween': '万圣节主题',
        'garden': '花园主题',
        'forest': '森林主题',
        'aqua': '海洋主题',
        'lofi': '低保真主题',
        'pastel': '粉彩主题',
        'fantasy': '幻想主题',
        'wireframe': '线框主题',
        'black': '黑色主题',
        'luxury': '奢华主题',
        'dracula': '德古拉主题',
        'cmyk': 'CMYK主题',
        'autumn': '秋季主题',
        'business': '商业主题',
        'acid': '酸性主题',
        'lemonade': '柠檬水主题',
        'night': '夜晚主题',
        'coffee': '咖啡主题',
        'winter': '冬季主题'
    };
    return themeNames[theme] || theme;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DaisyUI测试页面加载完成');
    
    // 显示当前主题
    const currentTheme = document.documentElement.getAttribute('data-theme');
    console.log('当前主题:', currentTheme);
    
    // 测试动画效果
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.3s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 50);
        }, index * 100);
    });
});
</script>
