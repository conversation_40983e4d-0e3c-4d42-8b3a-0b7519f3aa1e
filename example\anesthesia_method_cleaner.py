import pandas as pd
import re
import os


def remove_brackets_and_content(s):
    s = re.sub(r'\（.*?\)', '', str(s))  # remove content within mixed brackets
    s = re.sub(r'\（.*?\）', '', str(s))  # remove content within mixed brackets
    s = re.sub(r'\(.*?\)', '', str(s))  # remove content within mixed brackets
    s = re.sub(r'\(.*?\）', '', str(s))  # remove content within mixed brackets
    return s.strip()

def split_on_symbol(s):
    if s:
        parts = re.split(r'(\W+)', s)
        return [parts[0], ''.join(parts[1:]) if len(parts) > 1 else '']
    else:
        return [None, None]

def replace_terms(s):
    s = str(s)
    if '全身' in s:
        return '全身麻醉'
    elif '硬' in s:
        return '椎管内麻醉'
    elif '神经' in s:
        return '神经阻滞麻醉'
    elif '静脉' in s:
        return '静脉麻醉'
    elif '骶管' in s:
        return '椎管内麻醉'
    elif '局部' in s:
        return '局部麻醉'
    elif '椎管' in s:
        return '椎管内麻醉'
    elif '超声' in s:
        return ''
    elif '吸入' in s:
        return '全身麻醉'
    elif '表面' in s:
        return ''
    elif '静吸' in s:
        return '全身麻醉'
    elif '蛛网膜' in s:
        return '椎管内麻醉'
    else:
        return s

# 创建output文件夹（如果不存在）
os.makedirs('output', exist_ok=True)

# 读取输入文件
input_file = r'G:\code\Datacleaning\input\202506.xlsx'
df = pd.read_excel(input_file, engine='openpyxl')

df['麻醉方法1'] = df['麻醉方法'].apply(remove_brackets_and_content)
df[['left column', 'right column']] = df['麻醉方法1'].apply(split_on_symbol).apply(pd.Series)
df['主要麻醉方式'] = df['left column'].apply(replace_terms)
df['复合麻醉方式'] = df['right column'].apply(replace_terms)

# 获取原文件名（不含路径和扩展名）
file_name = os.path.basename(input_file)
file_name_without_ext = os.path.splitext(file_name)[0]

# 构建输出文件路径
output_file = os.path.join('output', f"{file_name_without_ext}_anesthesia_standardized.xlsx")

# 保存到output文件夹
df.to_excel(output_file, index=False)
print(f"处理完成，结果已保存至: {output_file}")