<!-- SPA版本的仪表盘内容 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">仪表盘</h1>
            <p class="text-base-content/60 mt-1">系统概览和关键指标</p>
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline btn-sm" onclick="location.reload()">
                <i class="fas fa-sync-alt mr-2"></i>
                刷新数据
            </button>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats stats-vertical lg:stats-horizontal shadow-lg">
        <!-- 总患者数 -->
        <div class="stat stat-primary">
            <div class="stat-figure">
                <i class="fas fa-users text-4xl opacity-80"></i>
            </div>
            <div class="stat-title opacity-80">总患者数</div>
            <div class="stat-value" id="total-patients">--</div>
            <div class="stat-desc opacity-80">
                <span class="text-success">+12%</span> 较上月
            </div>
        </div>

        <!-- 今日手术 -->
        <div class="stat stat-secondary">
            <div class="stat-figure">
                <i class="fas fa-procedures text-4xl opacity-80"></i>
            </div>
            <div class="stat-title opacity-80">今日手术</div>
            <div class="stat-value" id="today-surgeries">--</div>
            <div class="stat-desc opacity-80">
                <span class="text-warning">-5%</span> 较昨日
            </div>
        </div>

        <!-- 数据质量评分 -->
        <div class="stat stat-accent">
            <div class="stat-figure">
                <i class="fas fa-chart-line text-4xl opacity-80"></i>
            </div>
            <div class="stat-title opacity-80">数据质量评分</div>
            <div class="stat-value" id="data-quality">--</div>
            <div class="stat-desc opacity-80">
                <span class="text-success">+2.1%</span> 较上周
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="stat stat-success">
            <div class="stat-figure">
                <i class="fas fa-server text-4xl opacity-80"></i>
            </div>
            <div class="stat-title opacity-80">系统状态</div>
            <div class="stat-value text-xl">正常运行</div>
            <div class="stat-desc opacity-80">
                运行时间: <span id="system-uptime">--</span>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 手术趋势图 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h3 class="card-title">手术趋势</h3>
                <p class="text-sm text-base-content/60 mb-4">最近30天手术数量变化</p>
                <div class="h-64 flex items-center justify-center chart-bg rounded-lg">
                    <div class="text-center chart-placeholder">
                        <i class="fas fa-chart-area text-4xl mb-4"></i>
                        <p>图表加载中...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 麻醉方式分布 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h3 class="card-title">麻醉方式分布</h3>
                <p class="text-sm text-base-content/60 mb-4">各种麻醉方式使用比例</p>
                <div class="h-64 flex items-center justify-center chart-bg rounded-lg">
                    <div class="text-center chart-placeholder">
                        <i class="fas fa-chart-pie text-4xl mb-4"></i>
                        <p>图表加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作和最近活动 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 快速操作 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h3 class="card-title">快速操作</h3>
                <div class="space-y-3 mt-4">
                    <a href="/data-import" class="btn btn-outline btn-block justify-start" onclick="router.navigate('/data-import'); return false;">
                        <i class="fas fa-upload mr-3"></i>
                        导入数据
                    </a>
                    <a href="/patient-add" class="btn btn-outline btn-block justify-start" onclick="router.navigate('/patient-add'); return false;">
                        <i class="fas fa-user-plus mr-3"></i>
                        新增患者
                    </a>
                    <a href="/statistics" class="btn btn-outline btn-block justify-start" onclick="router.navigate('/statistics'); return false;">
                        <i class="fas fa-chart-bar mr-3"></i>
                        查看统计
                    </a>
                    <a href="/system-settings" class="btn btn-outline btn-block justify-start" onclick="router.navigate('/system-settings'); return false;">
                        <i class="fas fa-cog mr-3"></i>
                        系统设置
                    </a>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="card bg-base-100 shadow-xl lg:col-span-2">
            <div class="card-body">
                <h3 class="card-title">最近活动</h3>
                <p class="text-sm text-base-content/60 mb-4">系统最新操作记录</p>
                <div class="space-y-4" id="recent-activities">
                    <!-- 动态加载最近活动 -->
                    <div class="flex items-center justify-center py-8">
                        <div class="text-center text-base-content/60">
                            <div class="loading loading-spinner loading-md mb-2"></div>
                            <p>加载最近活动...</p>
                        </div>
                    </div>
                </div>
                <div class="card-actions justify-center mt-4">
                    <a href="#" class="btn btn-ghost btn-sm">查看全部活动</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加动画效果
    const cards = document.querySelectorAll('.stats, .card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('slide-in-left');
        }, index * 100);
    });
    
    // 加载仪表盘数据
    loadDashboardData();
});

// 加载仪表盘数据
function loadDashboardData() {
    // 模拟API调用加载统计数据
    setTimeout(() => {
        // 更新统计卡片
        document.getElementById('total-patients').textContent = '1,234';
        document.getElementById('today-surgeries').textContent = '28';
        document.getElementById('data-quality').textContent = '95.2';
        document.getElementById('system-uptime').textContent = '15天';
        
        // 加载最近活动
        loadRecentActivities();
    }, 1000);
}

// 加载最近活动
function loadRecentActivities() {
    const activitiesContainer = document.getElementById('recent-activities');
    const activities = [
        {
            icon: 'fas fa-upload',
            iconBg: 'stat-primary',
            title: '数据导入完成',
            description: '导入了 156 条手术记录',
            time: '2分钟前'
        },
        {
            icon: 'fas fa-user-plus',
            iconBg: 'stat-secondary',
            title: '新增患者',
            description: '添加了新患者信息',
            time: '15分钟前'
        },
        {
            icon: 'fas fa-chart-bar',
            iconBg: 'stat-accent',
            title: '生成统计报告',
            description: '生成了本月质控报告',
            time: '1小时前'
        },
        {
            icon: 'fas fa-cog',
            iconBg: 'stat-success',
            title: '系统配置更新',
            description: '更新了数据脱敏规则',
            time: '3小时前'
        }
    ];

    activitiesContainer.innerHTML = activities.map(activity => `
        <div class="flex items-center space-x-4 p-3 activity-bg hover-bg rounded-lg">
            <div class="w-10 h-10 ${activity.iconBg} rounded-full flex items-center justify-center">
                <i class="${activity.icon}"></i>
            </div>
            <div class="flex-1">
                <p class="font-medium">${activity.title}</p>
                <p class="text-sm text-base-content/60">${activity.description}</p>
            </div>
            <div class="text-sm text-base-content/60">
                ${activity.time}
            </div>
        </div>
    `).join('');
}
</script>
