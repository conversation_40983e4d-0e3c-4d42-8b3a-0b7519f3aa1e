"""
系统监控和性能分析工具
"""
import time
import functools
import threading
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta
from utils.logger import logger


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.api_metrics = defaultdict(lambda: {
            'calls': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'max_time': 0.0,
            'min_time': float('inf'),
            'errors': 0,
            'last_called': None
        })
        self.request_history = deque(maxlen=1000)
        self.error_history = deque(maxlen=100)
        self.lock = threading.Lock()
    
    def record_api_call(self, endpoint: str, method: str, duration: float, 
                       status_code: int, user_ip: str = None):
        """记录API调用"""
        with self.lock:
            key = f"{method} {endpoint}"
            metric = self.api_metrics[key]
            
            metric['calls'] += 1
            metric['total_time'] += duration
            metric['avg_time'] = metric['total_time'] / metric['calls']
            metric['max_time'] = max(metric['max_time'], duration)
            metric['min_time'] = min(metric['min_time'], duration)
            metric['last_called'] = datetime.now()
            
            if status_code >= 400:
                metric['errors'] += 1
            
            # 记录请求历史
            self.request_history.append({
                'timestamp': time.time(),
                'endpoint': endpoint,
                'method': method,
                'duration': duration,
                'status_code': status_code,
                'user_ip': user_ip
            })
            
            # 记录错误
            if status_code >= 400:
                self.error_history.append({
                    'timestamp': time.time(),
                    'endpoint': endpoint,
                    'method': method,
                    'status_code': status_code,
                    'duration': duration,
                    'user_ip': user_ip
                })
            
            # 慢请求警告
            if duration > 2.0:
                logger.warning(f"慢请求: {key} 耗时 {duration:.2f}s")
    
    def get_api_stats(self, time_window: int = 3600) -> Dict[str, Any]:
        """获取API统计信息"""
        with self.lock:
            current_time = time.time()
            cutoff_time = current_time - time_window
            
            # 过滤时间窗口内的请求
            recent_requests = [r for r in self.request_history if r['timestamp'] >= cutoff_time]
            recent_errors = [e for e in self.error_history if e['timestamp'] >= cutoff_time]
            
            # 计算统计信息
            total_requests = len(recent_requests)
            total_errors = len(recent_errors)
            error_rate = total_errors / total_requests if total_requests > 0 else 0
            
            # 按端点分组
            endpoint_stats = defaultdict(lambda: {'requests': 0, 'errors': 0, 'total_time': 0})
            for req in recent_requests:
                key = f"{req['method']} {req['endpoint']}"
                endpoint_stats[key]['requests'] += 1
                endpoint_stats[key]['total_time'] += req['duration']
                if req['status_code'] >= 400:
                    endpoint_stats[key]['errors'] += 1
            
            # 计算平均响应时间
            for stats in endpoint_stats.values():
                if stats['requests'] > 0:
                    stats['avg_time'] = stats['total_time'] / stats['requests']
                    stats['error_rate'] = stats['errors'] / stats['requests']
            
            return {
                'total_requests': total_requests,
                'total_errors': total_errors,
                'error_rate': error_rate,
                'requests_per_minute': total_requests / (time_window / 60),
                'endpoint_stats': dict(endpoint_stats),
                'time_window': time_window
            }
    
    def get_slow_endpoints(self, threshold: float = 1.0) -> List[Dict[str, Any]]:
        """获取慢端点列表"""
        slow_endpoints = []
        for endpoint, metrics in self.api_metrics.items():
            if metrics['avg_time'] > threshold and metrics['calls'] > 0:
                slow_endpoints.append({
                    'endpoint': endpoint,
                    'avg_time': metrics['avg_time'],
                    'max_time': metrics['max_time'],
                    'calls': metrics['calls'],
                    'error_rate': metrics['errors'] / metrics['calls']
                })
        
        return sorted(slow_endpoints, key=lambda x: x['avg_time'], reverse=True)
    
    def get_error_summary(self, time_window: int = 3600) -> Dict[str, Any]:
        """获取错误摘要"""
        with self.lock:
            current_time = time.time()
            cutoff_time = current_time - time_window
            
            recent_errors = [e for e in self.error_history if e['timestamp'] >= cutoff_time]
            
            # 按状态码分组
            status_code_counts = defaultdict(int)
            endpoint_errors = defaultdict(int)
            
            for error in recent_errors:
                status_code_counts[error['status_code']] += 1
                endpoint_errors[f"{error['method']} {error['endpoint']}"] += 1
            
            return {
                'total_errors': len(recent_errors),
                'status_code_distribution': dict(status_code_counts),
                'endpoint_errors': dict(endpoint_errors),
                'time_window': time_window
            }


# 全局监控器实例
system_monitor = SystemMonitor()


def monitor_api_performance(func):
    """API性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        from flask import request, g
        
        start_time = time.time()
        status_code = 200
        
        try:
            result = func(*args, **kwargs)
            
            # 尝试从响应中获取状态码
            if hasattr(result, 'status_code'):
                status_code = result.status_code
            elif isinstance(result, tuple) and len(result) > 1:
                status_code = result[1]
            
            return result
        except Exception as e:
            status_code = 500
            raise
        finally:
            duration = time.time() - start_time
            
            # 记录API调用
            endpoint = request.endpoint or request.path
            method = request.method
            user_ip = request.remote_addr
            
            system_monitor.record_api_call(
                endpoint=endpoint,
                method=method,
                duration=duration,
                status_code=status_code,
                user_ip=user_ip
            )
    
    return wrapper


class CacheMonitor:
    """缓存监控器"""
    
    def __init__(self):
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'hit_rate': 0.0
        }
        self.lock = threading.Lock()
    
    def record_hit(self):
        """记录缓存命中"""
        with self.lock:
            self.cache_stats['hits'] += 1
            self._update_hit_rate()
    
    def record_miss(self):
        """记录缓存未命中"""
        with self.lock:
            self.cache_stats['misses'] += 1
            self._update_hit_rate()
    
    def record_set(self):
        """记录缓存设置"""
        with self.lock:
            self.cache_stats['sets'] += 1
    
    def record_delete(self):
        """记录缓存删除"""
        with self.lock:
            self.cache_stats['deletes'] += 1
    
    def _update_hit_rate(self):
        """更新命中率"""
        total = self.cache_stats['hits'] + self.cache_stats['misses']
        if total > 0:
            self.cache_stats['hit_rate'] = self.cache_stats['hits'] / total
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            return self.cache_stats.copy()
    
    def reset_stats(self):
        """重置统计"""
        with self.lock:
            self.cache_stats = {
                'hits': 0,
                'misses': 0,
                'sets': 0,
                'deletes': 0,
                'hit_rate': 0.0
            }


# 全局缓存监控器
cache_monitor = CacheMonitor()


class DatabaseMonitor:
    """数据库监控器"""
    
    def __init__(self):
        self.query_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'max_time': 0.0
        })
        self.slow_queries = deque(maxlen=50)
        self.lock = threading.Lock()
    
    def record_query(self, query_type: str, duration: float, query: str = None):
        """记录数据库查询"""
        with self.lock:
            stats = self.query_stats[query_type]
            stats['count'] += 1
            stats['total_time'] += duration
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['max_time'] = max(stats['max_time'], duration)
            
            # 记录慢查询
            if duration > 0.5:  # 超过500ms
                self.slow_queries.append({
                    'query_type': query_type,
                    'duration': duration,
                    'query': query[:200] if query else None,
                    'timestamp': datetime.now()
                })
    
    def get_stats(self) -> Dict[str, Any]:
        """获取数据库统计"""
        with self.lock:
            return {
                'query_stats': dict(self.query_stats),
                'slow_queries': list(self.slow_queries)
            }


# 全局数据库监控器
db_monitor = DatabaseMonitor()


def get_system_health() -> Dict[str, Any]:
    """获取系统健康状态"""
    try:
        import psutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_mb': memory.available / 1024 / 1024,
            'disk_percent': disk.percent,
            'disk_free_gb': disk.free / 1024 / 1024 / 1024,
            'timestamp': datetime.now().isoformat()
        }
    except ImportError:
        return {
            'error': 'psutil not available',
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


def get_comprehensive_report() -> Dict[str, Any]:
    """获取综合监控报告"""
    return {
        'system_health': get_system_health(),
        'api_performance': system_monitor.get_api_stats(),
        'slow_endpoints': system_monitor.get_slow_endpoints(),
        'error_summary': system_monitor.get_error_summary(),
        'cache_stats': cache_monitor.get_stats(),
        'database_stats': db_monitor.get_stats(),
        'generated_at': datetime.now().isoformat()
    }
