"""
科室统计模块
"""
from typing import Dict, Any, List, Tuple
from database.models import DatabaseManager
from utils.logger import logger


class DepartmentStatistics:
    """科室相关统计分析"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_department_overview(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """获取科室概览统计"""
        logger.info("获取科室概览统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 构建完整的WHERE条件，包括department过滤
            if where_clause:
                full_where_clause = where_clause + " AND department IS NOT NULL AND department != ''"
                # 需要为子查询和主查询都提供参数
                full_params = params + params  # 参数重复一次给子查询使用
            else:
                full_where_clause = "WHERE department IS NOT NULL AND department != ''"
                full_params = []

            cursor = conn.execute(f'''
                SELECT
                    department,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT patient_id) as patient_count,
                    COUNT(DISTINCT surgeon) as surgeon_count,
                    COUNT(DISTINCT anesthesiologist) as anesthesiologist_count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM surgery_records {where_clause}), 2) as percentage
                FROM surgery_records
                {full_where_clause}
                GROUP BY department
                ORDER BY surgery_count DESC
            ''', full_params)
            
            result = []
            for i, row in enumerate(cursor.fetchall(), 1):
                result.append({
                    'rank': i,
                    'department': row[0],
                    'surgery_count': row[1],
                    'patient_count': row[2],
                    'surgeon_count': row[3],
                    'anesthesiologist_count': row[4],
                    'avg_duration': row[5] or 0,
                    'total_duration': row[6] or 0,
                    'percentage': row[7],
                    'avg_surgeries_per_surgeon': round(row[1] / max(row[3], 1), 2)
                })
            
            return result
    
    def get_department_anesthesia_methods(self, start_date: str = None, end_date: str = None) -> Dict[str, Dict[str, int]]:
        """获取各科室麻醉方法分布"""
        logger.info("获取各科室麻醉方法分布")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    department,
                    anesthesia_method,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                AND department IS NOT NULL AND department != ''
                AND anesthesia_method IS NOT NULL AND anesthesia_method != ''
                GROUP BY department, anesthesia_method
                ORDER BY department, count DESC
            ''', params)
            
            result = {}
            for row in cursor.fetchall():
                department, method, count = row
                if department not in result:
                    result[department] = {}
                result[department][method] = count
            
            return result
    
    def get_department_surgery_types(self, start_date: str = None, end_date: str = None) -> Dict[str, Dict[str, int]]:
        """获取各科室手术类型分布"""
        logger.info("获取各科室手术类型分布")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    department,
                    surgery_type,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                AND department IS NOT NULL AND department != ''
                AND surgery_type IS NOT NULL AND surgery_type != ''
                GROUP BY department, surgery_type
                ORDER BY department, count DESC
            ''', params)
            
            result = {}
            for row in cursor.fetchall():
                department, surgery_type, count = row
                if department not in result:
                    result[department] = {}
                result[department][surgery_type] = count
            
            return result
    
    def get_department_workload_trend(self, start_date: str = None, end_date: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """获取科室工作量趋势"""
        logger.info("获取科室工作量趋势")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    department,
                    strftime('%Y-%m', surgery_date) as month,
                    COUNT(*) as count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration
                FROM surgery_records 
                {where_clause}
                AND department IS NOT NULL AND department != ''
                GROUP BY department, strftime('%Y-%m', surgery_date)
                ORDER BY department, month
            ''', params)
            
            result = {}
            for row in cursor.fetchall():
                department, month, count, avg_duration = row
                if department not in result:
                    result[department] = []
                result[department].append({
                    'month': month,
                    'count': count,
                    'avg_duration': avg_duration or 0
                })
            
            return result
    
    def get_department_quality_indicators(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """获取科室质量指标"""
        logger.info("获取科室质量指标")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    department,
                    COUNT(*) as total_surgeries,
                    -- 并发症率
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complication_cases,
                    -- 术后镇痛使用率
                    COUNT(CASE WHEN postoperative_analgesia IS NOT NULL AND postoperative_analgesia != '' AND postoperative_analgesia != '否' THEN 1 END) as analgesia_cases,
                    -- 平均手术时长
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    -- 数据完整性（以手术名称为例）
                    COUNT(CASE WHEN surgery_name IS NULL OR surgery_name = '' THEN 1 END) as missing_surgery_name,
                    -- 时间记录完整性
                    COUNT(CASE WHEN surgery_start_time IS NULL OR surgery_start_time = '' THEN 1 END) as missing_start_time,
                    COUNT(CASE WHEN surgery_end_time IS NULL OR surgery_end_time = '' THEN 1 END) as missing_end_time
                FROM surgery_records 
                {where_clause}
                AND department IS NOT NULL AND department != ''
                GROUP BY department
                ORDER BY total_surgeries DESC
            ''', params)
            
            result = []
            for row in cursor.fetchall():
                department = row[0]
                total = row[1]
                complications = row[2]
                analgesia = row[3]
                avg_duration = row[4] or 0
                missing_surgery_name = row[5]
                missing_start_time = row[6]
                missing_end_time = row[7]
                
                # 计算各种率
                complication_rate = (complications / max(total, 1)) * 100
                analgesia_rate = (analgesia / max(total, 1)) * 100
                surgery_name_completeness = ((total - missing_surgery_name) / max(total, 1)) * 100
                time_completeness = ((total - missing_start_time - missing_end_time) / max(total * 2, 1)) * 100
                
                result.append({
                    'department': department,
                    'total_surgeries': total,
                    'complication_rate': round(complication_rate, 2),
                    'analgesia_rate': round(analgesia_rate, 2),
                    'avg_duration': avg_duration,
                    'surgery_name_completeness': round(surgery_name_completeness, 2),
                    'time_completeness': round(time_completeness, 2)
                })
            
            return result
    
    def get_department_comparison(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取科室对比分析"""
        logger.info("获取科室对比分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 获取科室基础数据
            cursor = conn.execute(f'''
                SELECT 
                    department,
                    COUNT(*) as surgery_count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    COUNT(DISTINCT surgeon) as surgeon_count,
                    COUNT(DISTINCT anesthesiologist) as anesthesiologist_count
                FROM surgery_records 
                {where_clause}
                AND department IS NOT NULL AND department != ''
                GROUP BY department
            ''', params)
            
            departments_data = {}
            total_surgeries = 0
            
            for row in cursor.fetchall():
                dept = row[0]
                count = row[1]
                avg_duration = row[2] or 0
                surgeon_count = row[3]
                anesthesiologist_count = row[4]
                
                departments_data[dept] = {
                    'surgery_count': count,
                    'avg_duration': avg_duration,
                    'surgeon_count': surgeon_count,
                    'anesthesiologist_count': anesthesiologist_count,
                    'efficiency': count / max(surgeon_count, 1)  # 每个医生的手术量
                }
                total_surgeries += count
            
            # 计算各科室占比和排名
            sorted_by_count = sorted(departments_data.items(), key=lambda x: x[1]['surgery_count'], reverse=True)
            sorted_by_efficiency = sorted(departments_data.items(), key=lambda x: x[1]['efficiency'], reverse=True)
            sorted_by_duration = sorted(departments_data.items(), key=lambda x: x[1]['avg_duration'], reverse=True)
            
            # 添加排名和占比信息
            for i, (dept, data) in enumerate(sorted_by_count):
                data['count_rank'] = i + 1
                data['percentage'] = round((data['surgery_count'] / max(total_surgeries, 1)) * 100, 2)
            
            for i, (dept, data) in enumerate(sorted_by_efficiency):
                departments_data[dept]['efficiency_rank'] = i + 1
            
            for i, (dept, data) in enumerate(sorted_by_duration):
                departments_data[dept]['duration_rank'] = i + 1
            
            return {
                'departments_data': departments_data,
                'total_surgeries': total_surgeries,
                'total_departments': len(departments_data),
                'top_by_count': sorted_by_count[:5],
                'top_by_efficiency': sorted_by_efficiency[:5]
            }
    
    def _build_date_filter(self, start_date: str = None, end_date: str = None) -> Tuple[str, List]:
        """构建日期过滤条件"""
        conditions = []
        params = []
        
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
    
    def _build_where_clause(self, start_date: str = None, end_date: str = None, additional_conditions: List[str] = None) -> Tuple[str, List]:
        """构建完整的WHERE子句"""
        conditions = []
        params = []
        
        # 添加日期条件
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        # 添加额外条件
        if additional_conditions:
            conditions.extend(additional_conditions)
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
