"""
错误处理工具
"""
import traceback
import functools
from typing import Callable, Any, Dict
from utils.logger import logger


class AnesthesiaQCError(Exception):
    """麻醉质控系统基础异常类"""
    pass


class DataProcessingError(AnesthesiaQCError):
    """数据处理异常"""
    pass


class FileUploadError(AnesthesiaQCError):
    """文件上传异常"""
    pass


class ValidationError(AnesthesiaQCError):
    """数据验证异常"""
    pass


def safe_execute(func: Callable) -> Callable:
    """
    安全执行装饰器，捕获并记录异常
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise
    return wrapper


def handle_web_error(func: Callable) -> Callable:
    """
    Web请求错误处理装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Dict[str, Any]:
        try:
            return func(*args, **kwargs)
        except FileUploadError as e:
            logger.warning(f"文件上传错误: {str(e)}")
            return {'success': False, 'error': f'文件上传错误: {str(e)}'}
        except DataProcessingError as e:
            logger.error(f"数据处理错误: {str(e)}")
            return {'success': False, 'error': f'数据处理错误: {str(e)}'}
        except ValidationError as e:
            logger.warning(f"数据验证错误: {str(e)}")
            return {'success': False, 'error': f'数据验证错误: {str(e)}'}
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return {'success': False, 'error': '系统内部错误，请稍后重试'}
    return wrapper


def validate_file(file_path: str, max_size: int = 50 * 1024 * 1024) -> None:
    """
    验证文件
    
    Args:
        file_path: 文件路径
        max_size: 最大文件大小（字节）
        
    Raises:
        ValidationError: 文件验证失败
    """
    import os
    from pathlib import Path
    
    if not os.path.exists(file_path):
        raise ValidationError(f"文件不存在: {file_path}")
    
    file_size = os.path.getsize(file_path)
    if file_size > max_size:
        raise ValidationError(f"文件过大: {file_size / 1024 / 1024:.1f}MB，最大允许 {max_size / 1024 / 1024:.1f}MB")
    
    file_ext = Path(file_path).suffix.lower()
    allowed_extensions = {'.xlsx', '.xls'}
    if file_ext not in allowed_extensions:
        raise ValidationError(f"不支持的文件格式: {file_ext}，支持的格式: {', '.join(allowed_extensions)}")


def validate_dataframe(df) -> None:
    """
    验证DataFrame
    
    Args:
        df: 要验证的DataFrame
        
    Raises:
        ValidationError: 数据验证失败
    """
    import pandas as pd
    
    if df is None or df.empty:
        raise ValidationError("数据为空")
    
    required_columns = ['手术日期', '麻醉方法']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValidationError(f"缺少必要的列: {', '.join(missing_columns)}")
    
    # 检查数据行数
    if len(df) > 10000:
        logger.warning(f"数据行数较多: {len(df)} 行，处理可能需要较长时间")


def format_error_message(error: Exception) -> str:
    """
    格式化错误消息
    
    Args:
        error: 异常对象
        
    Returns:
        格式化后的错误消息
    """
    if isinstance(error, (FileUploadError, DataProcessingError, ValidationError)):
        return str(error)
    elif isinstance(error, FileNotFoundError):
        return "文件未找到，请检查文件路径"
    elif isinstance(error, PermissionError):
        return "文件访问权限不足，请检查文件权限"
    elif isinstance(error, MemoryError):
        return "内存不足，请尝试处理较小的文件"
    else:
        return "系统内部错误，请稍后重试"


class ErrorContext:
    """
    错误上下文管理器
    """
    
    def __init__(self, operation_name: str, raise_on_error: bool = True):
        self.operation_name = operation_name
        self.raise_on_error = raise_on_error
        self.error = None
    
    def __enter__(self):
        logger.info(f"开始执行: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.error = exc_val
            logger.error(f"执行失败: {self.operation_name} - {str(exc_val)}")
            
            if not self.raise_on_error:
                # 抑制异常
                return True
        else:
            logger.info(f"执行成功: {self.operation_name}")
        
        return False
