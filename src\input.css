@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif;
  }
  
  body {
    @apply bg-base-100 text-base-content;
  }
}

/* 自定义组件样式 */
@layer components {
  .btn-medical {
    @apply btn btn-primary bg-medical-500 hover:bg-medical-600 border-medical-500 hover:border-medical-600;
  }
  
  .card-medical {
    @apply card bg-base-100 shadow-xl border border-base-300;
  }
  
  .input-medical {
    @apply input input-bordered focus:input-primary;
  }
  
  .table-medical {
    @apply table table-zebra w-full;
  }
  
  .badge-medical {
    @apply badge badge-primary;
  }
  
  .alert-medical {
    @apply alert alert-info;
  }
  
  .modal-medical {
    @apply modal modal-bottom sm:modal-middle;
  }
  
  .navbar-medical {
    @apply navbar bg-base-100 shadow-lg;
  }
  
  .drawer-medical {
    @apply drawer drawer-mobile;
  }
  
  .menu-medical {
    @apply menu p-4 w-80 bg-base-200 text-base-content;
  }
  
  .stats-medical {
    @apply stats shadow;
  }
  
  .progress-medical {
    @apply progress progress-primary;
  }
  
  .loading-medical {
    @apply loading loading-spinner loading-lg text-primary;
  }
}

/* 自定义工具类 */
@layer utilities {
  .text-medical {
    @apply text-medical-600;
  }
  
  .bg-medical {
    @apply bg-medical-50;
  }
  
  .border-medical {
    @apply border-medical-200;
  }
  
  .shadow-medical {
    @apply shadow-lg shadow-medical-100;
  }
  
  .gradient-medical {
    background: linear-gradient(135deg, theme('colors.medical.500'), theme('colors.medical.600'));
  }
  
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }
  
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }
}

/* 响应式工具类 */
@layer utilities {
  .container-medical {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .grid-medical {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }
  
  .flex-medical {
    @apply flex flex-col sm:flex-row items-center justify-between gap-4;
  }
}

/* 动画效果 */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    @apply bg-white text-black;
  }
  
  .card {
    @apply shadow-none border border-gray-300;
  }
}