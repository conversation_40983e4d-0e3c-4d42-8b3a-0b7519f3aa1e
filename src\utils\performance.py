"""
性能优化工具
"""
import time
import functools
import psutil
import gc
import json
import datetime
import decimal
import numpy as np
import pandas as pd
from typing import Callable, Any
from utils.logger import logger


def timing_decorator(func: Callable) -> Callable:
    """
    函数执行时间装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logger.info(f"{func.__name__} 执行时间: {execution_time:.2f}秒")
        
        return result
    return wrapper


def memory_monitor(func: Callable) -> Callable:
    """
    内存使用监控装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        # 获取执行前内存使用
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        result = func(*args, **kwargs)
        
        # 获取执行后内存使用
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_diff = memory_after - memory_before
        
        logger.info(f"{func.__name__} 内存使用: {memory_before:.1f}MB -> {memory_after:.1f}MB (差值: {memory_diff:+.1f}MB)")
        
        return result
    return wrapper


def optimize_pandas_performance():
    """
    优化pandas性能设置
    """
    import pandas as pd
    
    # 设置pandas选项以提高性能
    pd.set_option('mode.chained_assignment', None)  # 禁用链式赋值警告
    pd.set_option('compute.use_bottleneck', True)   # 使用bottleneck加速
    pd.set_option('compute.use_numexpr', True)      # 使用numexpr加速
    
    logger.info("pandas性能优化设置已应用")


def cleanup_memory():
    """
    清理内存
    """
    # 强制垃圾回收
    collected = gc.collect()
    logger.info(f"内存清理完成，回收了 {collected} 个对象")


def get_system_info() -> dict:
    """
    获取系统信息
    
    Returns:
        系统信息字典
    """
    try:
        cpu_count = psutil.cpu_count()
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            'cpu_count': cpu_count,
            'cpu_percent': cpu_percent,
            'memory_total': memory.total / 1024 / 1024 / 1024,  # GB
            'memory_available': memory.available / 1024 / 1024 / 1024,  # GB
            'memory_percent': memory.percent,
            'disk_total': disk.total / 1024 / 1024 / 1024,  # GB
            'disk_free': disk.free / 1024 / 1024 / 1024,  # GB
            'disk_percent': (disk.used / disk.total) * 100
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {str(e)}")
        return {}


def log_system_info():
    """
    记录系统信息
    """
    info = get_system_info()
    if info:
        logger.info(f"系统信息 - CPU: {info['cpu_count']}核心 {info['cpu_percent']:.1f}% | "
                   f"内存: {info['memory_available']:.1f}GB/{info['memory_total']:.1f}GB "
                   f"({info['memory_percent']:.1f}%) | "
                   f"磁盘: {info['disk_free']:.1f}GB/{info['disk_total']:.1f}GB "
                   f"({info['disk_percent']:.1f}%)")


class PerformanceContext:
    """
    性能监控上下文管理器
    """
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.start_memory = None
    
    def __enter__(self):
        self.start_time = time.time()
        try:
            process = psutil.Process()
            self.start_memory = process.memory_info().rss / 1024 / 1024  # MB
        except:
            self.start_memory = 0
        
        logger.info(f"开始执行: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.time()
        execution_time = end_time - self.start_time
        
        try:
            process = psutil.Process()
            end_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_diff = end_memory - self.start_memory
        except:
            end_memory = 0
            memory_diff = 0
        
        if exc_type is None:
            logger.info(f"完成执行: {self.operation_name} | "
                       f"耗时: {execution_time:.2f}秒 | "
                       f"内存变化: {memory_diff:+.1f}MB")
        else:
            logger.error(f"执行失败: {self.operation_name} | "
                        f"耗时: {execution_time:.2f}秒 | "
                        f"错误: {exc_val}")


# 初始化性能优化
def initialize_performance_optimizations():
    """
    初始化性能优化设置
    """
    optimize_pandas_performance()
    log_system_info()
    logger.info("性能优化初始化完成")


def convert_to_json_serializable(obj):
    """
    将对象转换为JSON可序列化的格式

    Args:
        obj: 要转换的对象

    Returns:
        JSON可序列化的对象
    """
    if obj is None:
        return None

    # 基本类型直接返回
    if isinstance(obj, (str, int, float, bool)):
        return obj

    # 日期时间类型
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()

    if isinstance(obj, datetime.date):
        return obj.isoformat()

    if isinstance(obj, datetime.time):
        return obj.isoformat()

    # Decimal类型
    if isinstance(obj, decimal.Decimal):
        return float(obj)

    # NumPy类型
    if hasattr(obj, 'dtype'):  # NumPy数组或标量
        if hasattr(obj, 'tolist'):
            return obj.tolist()
        else:
            return obj.item()

    # Pandas类型
    if hasattr(obj, 'to_dict'):  # DataFrame
        return obj.to_dict('records')

    if hasattr(obj, 'to_list'):  # Series
        return obj.to_list()

    # 字典类型
    if isinstance(obj, dict):
        return {key: convert_to_json_serializable(value) for key, value in obj.items()}

    # 列表、元组类型
    if isinstance(obj, (list, tuple)):
        return [convert_to_json_serializable(item) for item in obj]

    # 集合类型
    if isinstance(obj, set):
        return list(obj)

    # 其他对象尝试转换为字符串
    try:
        # 尝试JSON序列化测试
        json.dumps(obj)
        return obj
    except (TypeError, ValueError):
        # 如果无法序列化，转换为字符串
        return str(obj)
