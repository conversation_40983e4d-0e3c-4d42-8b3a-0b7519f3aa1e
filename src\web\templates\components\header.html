<!-- 顶部标题栏组件 -->
<header class="header-container h-16 bg-base-100 border-b border-base-300 flex items-center justify-between px-6 shadow-sm sticky top-0 z-30 transition-all duration-300">
    <!-- 左侧：导航控制和面包屑 -->
    <div class="flex items-center space-x-4">
        <!-- 侧边栏切换按钮 -->
        <button class="btn btn-ghost btn-circle w-10 h-10 hover:bg-primary hover:text-primary-content transition-all duration-200" onclick="toggleSidebar()" title="切换侧边栏">
            <i class="fas fa-bars text-lg"></i>
        </button>

        <!-- 面包屑导航 -->
        <nav class="breadcrumbs text-sm" aria-label="面包屑导航">
            <ol class="flex items-center space-x-1">
                {% block breadcrumbs %}
                <li>
                    <a href="{{ url_for('dashboard') }}" class="breadcrumb-item hover:text-primary transition-colors flex items-center">
                        <i class="fas fa-home mr-1"></i>首页
                    </a>
                </li>
                {% endblock %}
            </ol>
        </nav>
    </div>

    <!-- 右侧：操作区域 -->
    <div class="flex items-center space-x-3">
        <!-- 通知中心 -->
        <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-ghost btn-circle w-10 h-10 indicator" title="通知中心">
                <i class="fas fa-bell text-lg"></i>
                <span class="badge badge-xs badge-primary indicator-item" id="notification-count">3</span>
            </div>
            <div tabindex="0" class="dropdown-content card card-compact w-80 bg-base-100 shadow-xl border border-base-200 z-[1]">
                <div class="card-body">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="card-title text-sm">通知中心</h3>
                        <button class="btn btn-ghost btn-xs" onclick="markAllAsRead()">全部已读</button>
                    </div>
                    <div class="space-y-2 max-h-64 overflow-y-auto">
                        <!-- 通知项 -->
                        <div class="flex items-start space-x-3 p-2 hover:bg-base-50 rounded cursor-pointer" onclick="readNotification(1)">
                            <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium">数据导入完成</p>
                                <p class="text-xs text-base-content/60">成功导入156条手术记录</p>
                                <p class="text-xs text-base-content/40">2分钟前</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3 p-2 hover:bg-base-50 rounded cursor-pointer" onclick="readNotification(2)">
                            <div class="w-2 h-2 bg-warning rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium">系统维护提醒</p>
                                <p class="text-xs text-base-content/60">系统将于今晚23:00进行维护</p>
                                <p class="text-xs text-base-content/40">1小时前</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3 p-2 hover:bg-base-50 rounded cursor-pointer" onclick="readNotification(3)">
                            <div class="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium">备份完成</p>
                                <p class="text-xs text-base-content/60">数据库备份已成功完成</p>
                                <p class="text-xs text-base-content/40">3小时前</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-actions justify-center mt-3">
                        <a href="#" class="btn btn-ghost btn-sm">查看全部</a>
                    </div>
                </div>
            </div>
        </div>



        <!-- 用户菜单 -->
        <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-ghost btn-circle w-10 h-10 avatar" title="用户菜单">
                <div class="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center">
                    <i class="fas fa-user text-lg"></i>
                </div>
            </div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-lg border border-base-200">
                <li class="menu-title">
                    <span>管理员</span>
                </li>
                <li><a href="#" onclick="showProfile()">
                    <i class="fas fa-user-circle"></i> 个人资料
                </a></li>
                <li><a href="#" onclick="showPreferences()">
                    <i class="fas fa-cog"></i> 偏好设置
                </a></li>
                <li><a href="#" onclick="showActivityLog()">
                    <i class="fas fa-history"></i> 活动日志
                </a></li>
                <li><hr class="my-1"></li>
                <li><a href="#" onclick="showHelp()">
                    <i class="fas fa-question-circle"></i> 帮助中心
                </a></li>
                <li><a href="#" onclick="showKeyboardShortcuts()">
                    <i class="fas fa-keyboard"></i> 快捷键
                </a></li>
                <li><hr class="my-1"></li>
                <li><a href="#" onclick="lockScreen()">
                    <i class="fas fa-lock"></i> 锁定屏幕
                </a></li>
                <li><a href="#" onclick="logout()" class="text-error">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a></li>
            </ul>
        </div>
    </div>
</header>

<!-- 全局搜索结果模态框 -->
<div id="search-modal" class="modal">
    <div class="modal-box max-w-2xl">
        <h3 class="font-bold text-lg mb-4">搜索结果</h3>
        <div id="search-results" class="space-y-2 max-h-96 overflow-y-auto">
            <!-- 搜索结果将在这里显示 -->
        </div>
        <div class="modal-action">
            <button class="btn" onclick="closeSearchModal()">关闭</button>
        </div>
    </div>
</div>

<script>
// 标题栏组件相关功能

// 全局搜索
function handleGlobalSearch(event) {
    if (event.key === 'Enter') {
        performGlobalSearch();
    }
}

function performGlobalSearch() {
    const query = document.getElementById('global-search').value.trim();
    if (!query) {
        themeManager.showNotification('请输入搜索关键词', 'warning');
        return;
    }
    
    // 模拟搜索
    const mockResults = [
        { type: '患者', title: '张三 (P001)', description: '45岁男性，最近手术：胆囊切除术', url: '/patients/1' },
        { type: '手术', title: '胆囊切除术', description: '2025-01-15，主刀医生：王医生', url: '/surgeries/1' },
        { type: '设置', title: '数据导入设置', description: '配置数据导入相关参数', url: '/system-settings' }
    ];
    
    showSearchResults(mockResults);
}

function showSearchResults(results) {
    const modal = document.getElementById('search-modal');
    const resultsContainer = document.getElementById('search-results');
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<p class="text-center text-base-content/60">未找到相关结果</p>';
    } else {
        resultsContainer.innerHTML = results.map(result => `
            <div class="p-3 hover:bg-base-50 rounded cursor-pointer" onclick="window.location.href='${result.url}'">
                <div class="flex items-center space-x-3">
                    <span class="badge badge-outline badge-sm">${result.type}</span>
                    <div class="flex-1">
                        <p class="font-medium">${result.title}</p>
                        <p class="text-sm text-base-content/60">${result.description}</p>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    modal.classList.add('modal-open');
}

function closeSearchModal() {
    document.getElementById('search-modal').classList.remove('modal-open');
}

// 通知相关
function markAllAsRead() {
    document.getElementById('notification-count').style.display = 'none';
    themeManager.showNotification('所有通知已标记为已读', 'success');
}

function readNotification(id) {
    themeManager.showNotification(`通知 ${id} 已读`, 'info');
    // 更新通知计数
    const countElement = document.getElementById('notification-count');
    let count = parseInt(countElement.textContent) - 1;
    if (count <= 0) {
        countElement.style.display = 'none';
    } else {
        countElement.textContent = count;
    }
}

// 快速操作
function quickReport() {
    themeManager.showNotification('正在生成快速报告...', 'info');
    setTimeout(() => {
        themeManager.showNotification('报告生成完成', 'success');
    }, 2000);
}

// 用户菜单功能
function showProfile() {
    themeManager.showNotification('个人资料功能开发中...', 'info');
}

function showPreferences() {
    themeManager.showNotification('偏好设置功能开发中...', 'info');
}

function showActivityLog() {
    themeManager.showNotification('活动日志功能开发中...', 'info');
}

function showHelp() {
    themeManager.showNotification('帮助中心功能开发中...', 'info');
}

function showKeyboardShortcuts() {
    const shortcuts = `
常用快捷键：
Ctrl + / : 显示快捷键帮助
Ctrl + K : 全局搜索
Ctrl + N : 新增患者
Ctrl + I : 数据导入
Ctrl + S : 保存当前页面
Ctrl + R : 刷新数据
F11 : 全屏模式
Esc : 关闭模态框
    `;
    alert(shortcuts);
}

function lockScreen() {
    if (confirm('确定要锁定屏幕吗？')) {
        themeManager.showNotification('屏幕锁定功能开发中...', 'info');
    }
}

function logout() {
    if (confirm('确定要退出登录吗？')) {
        themeManager.showNotification('正在退出...', 'info');
        setTimeout(() => {
            window.location.href = '/login';
        }, 1000);
    }
}

// 键盘快捷键支持
document.addEventListener('keydown', function(event) {
    // Ctrl + K: 全局搜索
    if (event.ctrlKey && event.key === 'k') {
        event.preventDefault();
        document.getElementById('global-search').focus();
    }
    
    // Ctrl + /: 显示快捷键帮助
    if (event.ctrlKey && event.key === '/') {
        event.preventDefault();
        showKeyboardShortcuts();
    }
    
    // Ctrl + N: 新增患者
    if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        window.location.href = '/patients/add';
    }
    
    // Ctrl + I: 数据导入
    if (event.ctrlKey && event.key === 'i') {
        event.preventDefault();
        window.location.href = '/data-import';
    }
    
    // Esc: 关闭模态框
    if (event.key === 'Escape') {
        closeSearchModal();
    }
});

// 实时时间显示（可选）
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
    });
    
    // 如果有时间显示元素，更新它
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// 每分钟更新一次时间
setInterval(updateTime, 60000);
updateTime(); // 立即执行一次


</script>
