<!-- 通知组件 -->

<!-- 通知容器 -->
<div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
    <!-- 通知项将在这里动态添加 -->
</div>

<!-- 全局通知横幅 -->
<div id="global-banner" class="fixed top-0 left-0 right-0 z-40 hidden">
    <div class="alert alert-info">
        <div class="flex items-center justify-between w-full">
            <div class="flex items-center space-x-3">
                <i class="fas fa-info-circle text-xl"></i>
                <div>
                    <div class="font-medium" id="banner-title">系统通知</div>
                    <div class="text-sm opacity-80" id="banner-message">这是一条全局通知消息</div>
                </div>
            </div>
            <button class="btn btn-ghost btn-sm" onclick="closeBanner()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<!-- 浮动操作按钮 -->
<div id="fab-container" class="fixed bottom-6 right-6 z-40">
    <div class="dropdown dropdown-top dropdown-end">
        <div tabindex="0" role="button" class="btn btn-circle btn-primary shadow-lg">
            <i class="fas fa-plus text-lg"></i>
        </div>
        <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-xl border border-base-200 mb-2">
            <li><a href="{{ url_for('patient_add') if url_for else '#' }}">
                <i class="fas fa-user-plus"></i> 新增患者
            </a></li>
            <li><a href="{{ url_for('data_import') if url_for else '#' }}">
                <i class="fas fa-upload"></i> 导入数据
            </a></li>
            <li><a href="#" onclick="quickBackup()">
                <i class="fas fa-save"></i> 快速备份
            </a></li>
        </ul>
    </div>
</div>

<script>
// 通知管理器
class NotificationManager {
    constructor() {
        this.notifications = new Map();
        this.nextId = 1;
        this.defaultDuration = 5000;
        this.maxNotifications = 5;
    }

    // 显示通知
    show(message, type = 'info', options = {}) {
        const id = this.nextId++;
        const duration = options.duration || this.defaultDuration;
        const persistent = options.persistent || false;
        const actions = options.actions || [];

        // 如果通知数量超过限制，移除最旧的
        if (this.notifications.size >= this.maxNotifications) {
            const oldestId = this.notifications.keys().next().value;
            this.remove(oldestId);
        }

        const notification = this.createNotificationElement(id, message, type, actions, persistent);
        const container = document.getElementById('notification-container');
        container.appendChild(notification);

        // 添加进入动画
        setTimeout(() => {
            notification.classList.add('notification-enter');
        }, 10);

        // 存储通知信息
        this.notifications.set(id, {
            element: notification,
            type: type,
            message: message,
            timestamp: Date.now()
        });

        // 自动移除（如果不是持久通知）
        if (!persistent && duration > 0) {
            setTimeout(() => {
                this.remove(id);
            }, duration);
        }

        return id;
    }

    // 创建通知元素
    createNotificationElement(id, message, type, actions, persistent) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} shadow-lg notification-item`;
        notification.dataset.notificationId = id;

        const iconMap = {
            'info': 'fa-info-circle',
            'success': 'fa-check-circle',
            'warning': 'fa-exclamation-triangle',
            'error': 'fa-times-circle'
        };

        const icon = iconMap[type] || iconMap['info'];

        let actionsHtml = '';
        if (actions.length > 0) {
            actionsHtml = actions.map(action => 
                `<button class="btn btn-sm btn-ghost" onclick="${action.callback}">${action.label}</button>`
            ).join('');
        }

        notification.innerHTML = `
            <div class="flex items-start justify-between w-full">
                <div class="flex items-center space-x-3">
                    <i class="fas ${icon} text-lg flex-shrink-0"></i>
                    <div class="flex-1">
                        <div class="font-medium">${message}</div>
                        ${actionsHtml ? `<div class="mt-2 space-x-2">${actionsHtml}</div>` : ''}
                    </div>
                </div>
                ${!persistent ? `
                    <button class="btn btn-ghost btn-xs ml-2" onclick="notificationManager.remove(${id})">
                        <i class="fas fa-times"></i>
                    </button>
                ` : ''}
            </div>
        `;

        return notification;
    }

    // 移除通知
    remove(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            const element = notification.element;
            
            // 添加退出动画
            element.classList.add('notification-exit');
            
            setTimeout(() => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
                this.notifications.delete(id);
            }, 300);
        }
    }

    // 清除所有通知
    clearAll() {
        this.notifications.forEach((notification, id) => {
            this.remove(id);
        });
    }

    // 显示成功通知
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    // 显示错误通知
    error(message, options = {}) {
        return this.show(message, 'error', { ...options, duration: 8000 });
    }

    // 显示警告通知
    warning(message, options = {}) {
        return this.show(message, 'warning', { ...options, duration: 6000 });
    }

    // 显示信息通知
    info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    // 显示进度通知
    showProgress(message, progress = 0) {
        const id = this.nextId++;
        const notification = document.createElement('div');
        notification.className = 'alert alert-info shadow-lg notification-item';
        notification.dataset.notificationId = id;

        notification.innerHTML = `
            <div class="w-full">
                <div class="flex items-center space-x-3 mb-2">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span class="font-medium">${message}</span>
                </div>
                <progress class="progress progress-primary w-full" value="${progress}" max="100"></progress>
                <div class="text-xs text-right mt-1">${progress}%</div>
            </div>
        `;

        const container = document.getElementById('notification-container');
        container.appendChild(notification);

        this.notifications.set(id, {
            element: notification,
            type: 'progress',
            message: message,
            timestamp: Date.now()
        });

        return id;
    }

    // 更新进度通知
    updateProgress(id, progress, message = null) {
        const notification = this.notifications.get(id);
        if (notification && notification.type === 'progress') {
            const element = notification.element;
            const progressBar = element.querySelector('progress');
            const progressText = element.querySelector('.text-xs');
            const messageElement = element.querySelector('.font-medium');

            if (progressBar) progressBar.value = progress;
            if (progressText) progressText.textContent = `${progress}%`;
            if (message && messageElement) messageElement.textContent = message;

            // 如果进度完成，转换为成功通知
            if (progress >= 100) {
                setTimeout(() => {
                    this.remove(id);
                    this.success(message || '操作完成');
                }, 1000);
            }
        }
    }

    // 显示全局横幅
    showBanner(title, message, type = 'info', persistent = false) {
        const banner = document.getElementById('global-banner');
        const titleElement = document.getElementById('banner-title');
        const messageElement = document.getElementById('banner-message');

        titleElement.textContent = title;
        messageElement.textContent = message;

        // 设置样式
        banner.className = `fixed top-0 left-0 right-0 z-40`;
        banner.querySelector('.alert').className = `alert alert-${type}`;

        banner.classList.remove('hidden');

        // 自动隐藏（如果不是持久横幅）
        if (!persistent) {
            setTimeout(() => {
                this.closeBanner();
            }, 10000);
        }
    }

    // 关闭横幅
    closeBanner() {
        const banner = document.getElementById('global-banner');
        banner.classList.add('hidden');
    }
}

// 创建全局通知管理器
window.notificationManager = new NotificationManager();

// 便捷函数
function showNotification(message, type = 'info', options = {}) {
    return notificationManager.show(message, type, options);
}

function showSuccess(message, options = {}) {
    return notificationManager.success(message, options);
}

function showError(message, options = {}) {
    return notificationManager.error(message, options);
}

function showWarning(message, options = {}) {
    return notificationManager.warning(message, options);
}

function showInfo(message, options = {}) {
    return notificationManager.info(message, options);
}

function showProgress(message, progress = 0) {
    return notificationManager.showProgress(message, progress);
}

function updateProgress(id, progress, message = null) {
    notificationManager.updateProgress(id, progress, message);
}

function showBanner(title, message, type = 'info', persistent = false) {
    notificationManager.showBanner(title, message, type, persistent);
}

function closeBanner() {
    notificationManager.closeBanner();
}

function clearAllNotifications() {
    notificationManager.clearAll();
}

// 横幅关闭函数
window.closeBanner = closeBanner;

// 系统事件通知
document.addEventListener('DOMContentLoaded', function() {
    // 检查网络状态
    if (!navigator.onLine) {
        showBanner('网络连接', '当前处于离线状态，部分功能可能不可用', 'warning', true);
    }

    // 监听网络状态变化
    window.addEventListener('online', function() {
        showSuccess('网络连接已恢复');
        closeBanner();
    });

    window.addEventListener('offline', function() {
        showBanner('网络连接', '网络连接已断开，请检查网络设置', 'error', true);
    });
});

// 页面可见性变化通知
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面重新可见时，可以检查是否有新通知
        // 这里可以添加检查逻辑
    }
});

// 错误处理
window.addEventListener('error', function(event) {
    console.error('页面错误:', event.error);
    showError('页面发生错误，请刷新页面重试');
});

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise拒绝:', event.reason);
    showError('操作失败，请重试');
});

// 浮动操作按钮功能
function quickBackup() {
    const progressId = showProgress('正在备份数据...', 0);
    
    // 模拟备份进度
    let progress = 0;
    const interval = setInterval(() => {
        progress += 10;
        updateProgress(progressId, progress, '正在备份数据...');
        
        if (progress >= 100) {
            clearInterval(interval);
        }
    }, 200);
}

// 键盘快捷键
document.addEventListener('keydown', function(event) {
    // Ctrl + Shift + N: 清除所有通知
    if (event.ctrlKey && event.shiftKey && event.key === 'N') {
        event.preventDefault();
        clearAllNotifications();
        showInfo('所有通知已清除');
    }
});
</script>

<style>
/* 通知动画 */
.notification-item {
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-out;
}

.notification-item.notification-enter {
    transform: translateX(0);
    opacity: 1;
}

.notification-item.notification-exit {
    transform: translateX(100%);
    opacity: 0;
    max-height: 0;
    margin: 0;
    padding: 0;
}

/* 浮动操作按钮动画 */
#fab-container {
    animation: fab-bounce 2s ease-in-out infinite;
}

@keyframes fab-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

#fab-container:hover {
    animation: none;
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* 横幅动画 */
#global-banner {
    transform: translateY(-100%);
    transition: transform 0.3s ease-out;
}

#global-banner:not(.hidden) {
    transform: translateY(0);
}

/* 响应式调整 */
@media (max-width: 640px) {
    #notification-container {
        left: 1rem;
        right: 1rem;
        max-width: none;
    }
    
    #fab-container {
        bottom: 1rem;
        right: 1rem;
    }
}

/* 深色主题适配 */
[data-theme="dark"] .notification-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .notification-item {
        border: 2px solid currentColor;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .notification-item,
    #fab-container,
    #global-banner {
        animation: none;
        transition: none;
    }
}
</style>
