<!-- SPA版本的导入历史内容 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">导入历史</h1>
            <p class="text-base-content/60 mt-1">查看数据导入记录和状态</p>
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline btn-sm" onclick="refreshHistory()">
                <i class="fas fa-sync-alt mr-2"></i>
                刷新
            </button>
            <a href="/data-import" class="btn btn-primary btn-sm" onclick="router.navigate('/data-import'); return false;">
                <i class="fas fa-plus mr-2"></i>
                新建导入
            </a>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">状态筛选</span>
                    </label>
                    <select class="select select-bordered" id="status-filter">
                        <option value="">所有状态</option>
                        <option value="success">成功</option>
                        <option value="failed">失败</option>
                        <option value="processing">处理中</option>
                    </select>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">开始日期</span>
                    </label>
                    <input type="date" class="input input-bordered" id="start-date">
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">结束日期</span>
                    </label>
                    <input type="date" class="input input-bordered" id="end-date">
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">&nbsp;</span>
                    </label>
                    <button class="btn btn-primary" onclick="filterHistory()">
                        <i class="fas fa-search mr-2"></i>
                        筛选
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入历史列表 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h3 class="card-title mb-4">导入记录</h3>
            
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>导入ID</th>
                            <th>文件名</th>
                            <th>导入时间</th>
                            <th>状态</th>
                            <th>记录数</th>
                            <th>成功数</th>
                            <th>失败数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="history-table-body">
                        <!-- 加载中状态 -->
                        <tr>
                            <td colspan="8" class="text-center py-8">
                                <div class="flex items-center justify-center">
                                    <div class="loading loading-spinner loading-md mr-2"></div>
                                    <span>加载导入历史...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-center mt-6">
                <div class="join">
                    <button class="join-item btn btn-outline" onclick="previousPage()">«</button>
                    <button class="join-item btn btn-outline btn-active">1</button>
                    <button class="join-item btn btn-outline">2</button>
                    <button class="join-item btn btn-outline">3</button>
                    <button class="join-item btn btn-outline" onclick="nextPage()">»</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div id="detail-modal" class="modal">
    <div class="modal-box w-11/12 max-w-5xl">
        <h3 class="font-bold text-lg mb-4">导入详情</h3>
        <div id="detail-content">
            <!-- 详情内容将动态加载 -->
        </div>
        <div class="modal-action">
            <button class="btn" onclick="closeDetailModal()">关闭</button>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let importHistory = [];

async function loadImportHistory() {
    try {
        showLoading(true);

        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            limit: 20,
            ...getFilters()
        });

        const response = await fetch(`/api/import-history?${params}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            importHistory = result.data || [];
            renderHistoryTable();
            updateStatistics();
        } else {
            throw new Error(result.error || '加载失败');
        }

    } catch (error) {
        console.error('加载导入历史失败:', error);

        // 使用模拟数据作为后备
        const mockHistory = [
            {
                id: 'IMP001',
                filename: '手术记录_2024-01-15.xlsx',
                import_time: '2024-01-15 14:30:25',
                status: 'completed',
                total_records: 156,
                new_records: 156,
                duplicate_records: 0,
                error_records: 0,
                duration: 155
            },
            {
                id: 'IMP002',
                filename: '患者信息_2024-01-14.xlsx',
                import_time: '2024-01-14 09:15:42',
                status: 'completed',
                total_records: 89,
                new_records: 87,
                duplicate_records: 0,
                error_records: 2,
                duration: 98
            },
            {
                id: 'IMP003',
                filename: '麻醉记录_2024-01-13.xlsx',
                import_time: '2024-01-13 16:45:18',
                status: 'failed',
                total_records: 234,
                new_records: 0,
                duplicate_records: 0,
                error_records: 234,
                duration: 15,
                error_message: '文件格式不正确'
            }
        ];

        importHistory = mockHistory;
        renderHistoryTable();
        updateStatistics();

        utils.showNotification('使用模拟数据，请检查网络连接', 'warning');
    } finally {
        showLoading(false);
    }
}

// 获取筛选条件
function getFilters() {
    const filters = {};

    const status = document.getElementById('status-filter').value;
    if (status) filters.status = status;

    const startDate = document.getElementById('start-date').value;
    if (startDate) filters.start_date = startDate;

    const endDate = document.getElementById('end-date').value;
    if (endDate) filters.end_date = endDate;

    const search = document.getElementById('search-input').value.trim();
    if (search) filters.search = search;

    return filters;
}

// 显示加载状态
function showLoading(show) {
    const tbody = document.getElementById('history-table-body');
    if (show) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-8">
                    <span class="loading loading-spinner loading-lg"></span>
                    <span class="ml-3">加载中...</span>
                </td>
            </tr>
        `;
    }
}

// 更新统计信息
function updateStatistics() {
    const totalImports = importHistory.length;
    const successImports = importHistory.filter(h => h.status === 'completed').length;
    const failedImports = importHistory.filter(h => h.status === 'failed').length;
    const totalRecords = importHistory.reduce((sum, h) => sum + (h.total_records || 0), 0);

    // 这里可以更新页面上的统计显示
    console.log('统计信息:', { totalImports, successImports, failedImports, totalRecords });
}

function renderHistoryTable() {
    const tbody = document.getElementById('history-table-body');
    
    if (importHistory.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-8">
                    <div class="text-base-content/60">
                        <i class="fas fa-inbox text-4xl mb-4"></i>
                        <p>暂无导入记录</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = importHistory.map(record => `
        <tr>
            <td>${record.id}</td>
            <td>${record.filename}</td>
            <td>${record.importTime}</td>
            <td>
                <div class="badge ${getStatusBadgeClass(record.status)}">
                    ${getStatusText(record.status)}
                </div>
            </td>
            <td>${record.totalRecords}</td>
            <td class="text-success">${record.successRecords}</td>
            <td class="text-error">${record.failedRecords}</td>
            <td>
                <div class="flex space-x-2">
                    <button class="btn btn-ghost btn-xs" onclick="viewDetail('${record.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${record.status === 'success' ? `
                        <button class="btn btn-ghost btn-xs" onclick="downloadReport('${record.id}')" title="下载报告">
                            <i class="fas fa-download"></i>
                        </button>
                    ` : ''}
                    ${record.status === 'failed' ? `
                        <button class="btn btn-ghost btn-xs" onclick="retryImport('${record.id}')" title="重新导入">
                            <i class="fas fa-redo"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

function getStatusBadgeClass(status) {
    const classes = {
        'success': 'badge-success',
        'failed': 'badge-error',
        'processing': 'badge-warning'
    };
    return classes[status] || 'badge-ghost';
}

function getStatusText(status) {
    const texts = {
        'success': '成功',
        'failed': '失败',
        'processing': '处理中'
    };
    return texts[status] || status;
}

function filterHistory() {
    const status = document.getElementById('status-filter').value;
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    
    // 这里可以添加实际的筛选逻辑
    themeManager.showNotification('筛选完成', 'success');
    loadImportHistory(); // 重新加载数据
}

function refreshHistory() {
    themeManager.showNotification('刷新中...', 'info');
    loadImportHistory();
    setTimeout(() => {
        themeManager.showNotification('数据已刷新', 'success');
    }, 500);
}

function viewDetail(importId) {
    const record = importHistory.find(r => r.id === importId);
    if (!record) return;
    
    const detailContent = document.getElementById('detail-content');
    detailContent.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
                <p><strong>导入ID：</strong>${record.id}</p>
                <p><strong>文件名：</strong>${record.filename}</p>
                <p><strong>导入时间：</strong>${record.importTime}</p>
                <p><strong>状态：</strong><span class="badge ${getStatusBadgeClass(record.status)}">${getStatusText(record.status)}</span></p>
            </div>
            <div class="space-y-2">
                <p><strong>总记录数：</strong>${record.totalRecords}</p>
                <p><strong>成功记录：</strong><span class="text-success">${record.successRecords}</span></p>
                <p><strong>失败记录：</strong><span class="text-error">${record.failedRecords}</span></p>
                <p><strong>成功率：</strong>${((record.successRecords / record.totalRecords) * 100).toFixed(1)}%</p>
            </div>
        </div>
        
        ${record.status === 'failed' ? `
            <div class="mt-4">
                <h4 class="font-bold mb-2">错误信息：</h4>
                <div class="bg-error/10 p-3 rounded">
                    <p class="text-error">文件格式不正确，请检查Excel文件结构</p>
                </div>
            </div>
        ` : ''}
    `;
    
    document.getElementById('detail-modal').classList.add('modal-open');
}

function closeDetailModal() {
    document.getElementById('detail-modal').classList.remove('modal-open');
}

function downloadReport(importId) {
    themeManager.showNotification(`下载 ${importId} 的导入报告`, 'info');
}

function retryImport(importId) {
    if (confirm('确定要重新导入这个文件吗？')) {
        themeManager.showNotification(`重新导入 ${importId}`, 'info');
    }
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        loadImportHistory();
    }
}

function nextPage() {
    currentPage++;
    loadImportHistory();
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('导入历史页面加载完成');
    loadImportHistory();
});
</script>
