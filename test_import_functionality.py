#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据导入功能脚本
生成符合系统格式的测试数据并测试导入功能
"""

import pandas as pd
import random
from datetime import datetime, timedelta
import os
import sys
import requests
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self):
        # 基础数据字典
        self.departments = ['外一科', '外二科', '泌尿外科', '骨科', '神经外科', '胸外科', '妇产科', '眼科', '耳鼻喉科']
        self.operating_rooms = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10']
        self.genders = ['男', '女']
        self.surgery_types = ['择期', '急诊', '限期']
        self.surgery_levels = ['一级', '二级', '三级', '四级', '其他']
        self.incision_levels = ['Ⅰ级', 'Ⅱ级', 'Ⅲ级', '其他']
        self.positions = ['平卧位', '侧卧位', '俯卧位', '截石位', '坐位']
        self.anesthesia_methods = [
            '全身麻醉', '腰硬联合麻醉', '神经阻滞麻醉', '局部麻醉', 
            '椎管内麻醉', '硬膜外麻醉', '腰麻', '臂丛神经阻滞'
        ]
        
        # 手术名称库
        self.surgery_names = [
            '腹腔镜下单侧腹股沟斜疝修补术',
            '经尿道右侧输尿管/肾盂激光碎石取石术',
            '左侧肩锁关节脱位切开复位内固定术',
            '胆囊切除术',
            '阑尾切除术',
            '甲状腺切除术',
            '乳腺肿物切除术',
            '疝修补术',
            '痔疮切除术',
            '白内障超声乳化术',
            '扁桃体切除术',
            '骨折内固定术',
            '关节镜检查术',
            '子宫肌瘤切除术',
            '剖宫产术'
        ]
        
        # 医生姓名库
        self.doctor_names = [
            '张三文', '李四明', '王五华', '赵六强', '钱七宝', '孙八福',
            '周九龙', '吴十杰', '郑一一', '王二二', '李三三', '张四四',
            '龙剑', '孙宇', '杨一明', '魏一千', '起一佳', '米国',
            '王志富', '王凯', '李五海', '李六宇', '袁二明', '虞磊',
            '毛双华', '邹六燕', '罗三鑫', '邓九文'
        ]
        
        # 护士姓名库
        self.nurse_names = [
            '护士甲', '护士乙', '护士丙', '护士丁', '护士戊',
            '李护士', '王护士', '张护士', '刘护士', '陈护士',
            '杨护士', '赵护士', '黄护士', '周护士', '吴护士'
        ]

    def generate_patient_name(self):
        """生成患者姓名"""
        surnames = ['张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴', '徐', '孙', '马', '朱', '胡']
        given_names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛']
        return random.choice(surnames) + random.choice(given_names) + random.choice(given_names)

    def generate_hospital_number(self, base_date):
        """生成住院号"""
        year = base_date.year
        return f"{year}{random.randint(100000, 999999)}"

    def generate_time_sequence(self, base_date):
        """生成手术时间序列"""
        # 入室时间 (8:00-16:00)
        entry_hour = random.randint(8, 16)
        entry_minute = random.choice([0, 15, 30, 45])
        entry_time = base_date.replace(hour=entry_hour, minute=entry_minute)
        
        # 麻醉开始时间 (入室后5-15分钟)
        anesthesia_start = entry_time + timedelta(minutes=random.randint(5, 15))
        
        # 手术开始时间 (麻醉开始后15-45分钟)
        surgery_start = anesthesia_start + timedelta(minutes=random.randint(15, 45))
        
        # 手术时长 (30分钟-4小时)
        surgery_duration = random.randint(30, 240)
        surgery_end = surgery_start + timedelta(minutes=surgery_duration)
        
        # 麻醉结束时间 (手术结束后5-20分钟)
        anesthesia_end = surgery_end + timedelta(minutes=random.randint(5, 20))
        
        # 出室时间 (麻醉结束时间)
        exit_time = anesthesia_end
        
        # PACU时间 (可能为空)
        pacu_entry = ""
        pacu_exit = ""
        if random.choice([True, False]):  # 50%概率进PACU
            pacu_entry = (exit_time + timedelta(minutes=random.randint(5, 15))).strftime("%H:%M")
            pacu_exit = (exit_time + timedelta(minutes=random.randint(30, 120))).strftime("%H:%M")
        
        return {
            'entry_time': entry_time.strftime("%H:%M"),
            'anesthesia_start': anesthesia_start.strftime("%H:%M"),
            'surgery_start': surgery_start.strftime("%H:%M"),
            'surgery_end': surgery_end.strftime("%H:%M"),
            'anesthesia_end': anesthesia_end.strftime("%H:%M"),
            'exit_time': exit_time.strftime("%H:%M"),
            'pacu_entry': pacu_entry,
            'pacu_exit': pacu_exit
        }

    def generate_diagnosis(self, surgery_name):
        """根据手术名称生成术前诊断"""
        diagnosis_map = {
            '腹腔镜下单侧腹股沟斜疝修补术': '腹股沟斜疝（右侧）',
            '经尿道右侧输尿管/肾盂激光碎石取石术': '肾积水伴输尿管结石（右侧）',
            '左侧肩锁关节脱位切开复位内固定术': '肩锁关节脱位（左）；肩胛骨骨折（左 粉碎性）',
            '胆囊切除术': '胆囊结石；慢性胆囊炎',
            '阑尾切除术': '急性阑尾炎',
            '甲状腺切除术': '甲状腺结节',
            '乳腺肿物切除术': '乳腺纤维瘤',
            '疝修补术': '腹股沟疝',
            '痔疮切除术': '混合痔',
            '白内障超声乳化术': '老年性白内障',
            '扁桃体切除术': '慢性扁桃体炎',
            '骨折内固定术': '骨折',
            '关节镜检查术': '膝关节损伤',
            '子宫肌瘤切除术': '子宫肌瘤',
            '剖宫产术': '胎位不正'
        }
        return diagnosis_map.get(surgery_name, '待查')

    def generate_test_data(self, num_records=200, start_date=None):
        """生成测试数据"""
        if start_date is None:
            start_date = datetime(2025, 6, 30)
        
        data = []
        
        for i in range(num_records):
            # 随机选择日期 (在指定日期前后30天内)
            date_offset = random.randint(-30, 30)
            surgery_date = start_date + timedelta(days=date_offset)
            
            # 生成时间序列
            times = self.generate_time_sequence(surgery_date)
            
            # 选择手术相关信息
            surgery_name = random.choice(self.surgery_names)
            anesthesia_method = random.choice(self.anesthesia_methods)
            
            # 生成医护人员
            main_surgeon = random.choice(self.doctor_names)
            assistant1 = random.choice(self.doctor_names) if random.choice([True, False]) else ""
            assistant2 = random.choice(self.doctor_names) if random.choice([True, False, False]) else ""
            assistant3 = random.choice(self.doctor_names) if random.choice([True, False, False, False]) else ""
            
            main_anesthesiologist = random.choice(self.doctor_names)
            assistant_anesthesiologist = random.choice(self.doctor_names) if random.choice([True, False]) else ""
            anesthesia_assistant = random.choice(self.nurse_names) if random.choice([True, False]) else ""
            
            # 生成一条记录
            record = {
                '手术日期': surgery_date.strftime("%Y-%m-%d"),
                '手术室': '手术室',
                '手术间': random.choice(self.operating_rooms),
                '住院号': self.generate_hospital_number(surgery_date),
                '患者姓名': self.generate_patient_name(),
                '性别': random.choice(self.genders),
                '年龄': random.randint(18, 85),
                '临床科室': random.choice(self.departments),
                '手术名称': surgery_name,
                '麻醉方法': anesthesia_method,
                '入PACU时间': times['pacu_entry'],
                '出PACU时间': times['pacu_exit'],
                '手术类型': random.choice(self.surgery_types),
                '手术级别': random.choice(self.surgery_levels),
                '切口等级': random.choice(self.incision_levels),
                '入室时间': times['entry_time'],
                '麻醉开始时间': times['anesthesia_start'],
                '手术开始时间': times['surgery_start'],
                '手术结束时间': times['surgery_end'],
                '麻醉结束时间': times['anesthesia_end'],
                '出室时间': times['exit_time'],
                '术前诊断': self.generate_diagnosis(surgery_name),
                '手术医生': main_surgeon,
                '一助': assistant1,
                '二助': assistant2,
                '三助': assistant3,
                '主麻': main_anesthesiologist,
                '副麻': assistant_anesthesiologist,
                '麻醉助手': anesthesia_assistant,
                '灌注医生': random.choice(self.doctor_names) if random.choice([True, False, False]) else "",
                '洗手护士1': random.choice(self.nurse_names),
                '洗手护士2': random.choice(self.nurse_names) if random.choice([True, False]) else "",
                '巡回护士1': random.choice(self.nurse_names),
                '巡回护士2': random.choice(self.nurse_names) if random.choice([True, False]) else "",
                '接替器械护士': random.choice(self.nurse_names) if random.choice([True, False, False]) else "",
                '接替巡回护士': random.choice(self.nurse_names) if random.choice([True, False, False]) else "",
                '体位': random.choice(self.positions),
                '压疮评估': random.choice(['是', '否', '']),
                '术后镇痛': random.choice(['是', '否']),
                '手术部位标识': random.choice(['是', '否']),
                '术中导尿': random.choice(['是', '否', '']),
                '尿管护理': random.choice(['是', '否', '无']),
                '病理标本': random.choice(['是', '否', ''])
            }
            
            data.append(record)
        
        return pd.DataFrame(data)

class ImportTester:
    """导入功能测试器"""
    
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()

    def test_api_connectivity(self):
        """测试API连通性"""
        try:
            response = self.session.get(f"{self.base_url}/api/statistics")
            print(f"✅ API连通性测试: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ API连通性测试失败: {e}")
            return False

    def test_file_upload(self, file_path):
        """测试文件上传和导入"""
        try:
            # 准备文件
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
                
                # 准备表单数据
                data = {
                    'enable_privacy': 'true',
                    'import_config': json.dumps({
                        'enablePrivacy': True,
                        'enableCleaning': True,
                        'skipDuplicates': True,
                        'validateData': True,
                        'duplicateStrategy': 'skip',
                        'errorStrategy': 'skip',
                        'batchSize': 500
                    })
                }
                
                print(f"📤 开始上传文件: {file_path}")
                response = self.session.post(
                    f"{self.base_url}/api/import-data",
                    files=files,
                    data=data,
                    timeout=300  # 5分钟超时
                )
                
                print(f"📊 响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ 导入成功!")
                    print(f"📈 导入结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    return True, result
                else:
                    print(f"❌ 导入失败: {response.status_code}")
                    print(f"错误信息: {response.text}")
                    return False, response.text
                    
        except Exception as e:
            print(f"❌ 上传过程中发生错误: {e}")
            return False, str(e)

def diagnose_field_mapping():
    """诊断字段映射问题"""
    print("🔍 诊断字段映射问题...")

    # 生成简化的测试数据，只包含必需字段
    simplified_data = pd.DataFrame([
        {
            '患者ID': '2025123456',
            '患者姓名': '张三',
            '手术日期': '2025-06-30',
            '手术名称': '阑尾切除术',
            '麻醉方法': '全身麻醉',
            '年龄': 35,
            '性别': '男'
        },
        {
            '患者ID': '2025123457',
            '患者姓名': '李四',
            '手术日期': '2025-07-01',
            '手术名称': '胆囊切除术',
            '麻醉方法': '腰硬联合麻醉',
            '年龄': 42,
            '性别': '女'
        }
    ])

    # 保存简化数据
    simple_file = "test_simple_data.xlsx"
    simplified_data.to_excel(simple_file, index=False)
    print(f"✅ 简化测试数据已保存到: {simple_file}")
    print("📋 简化数据列名:", list(simplified_data.columns))

    # 测试导入
    tester = ImportTester()
    success, result = tester.test_file_upload(simple_file)

    if success:
        print("✅ 简化数据导入测试成功")
        if result.get('error_records', 0) > 0:
            print("⚠️ 但仍有错误记录，需要进一步检查字段映射")
    else:
        print("❌ 简化数据导入测试失败")

    return success, result

def main():
    """主函数"""
    print("🚀 开始生成测试数据和测试导入功能")
    print("=" * 60)

    # 1. 先进行字段映射诊断
    print("🔍 第一步：字段映射诊断")
    diagnose_success, diagnose_result = diagnose_field_mapping()

    if not diagnose_success or diagnose_result.get('error_records', 0) > 0:
        print("⚠️ 字段映射存在问题，建议先解决字段映射问题")
        print("💡 可能的问题：")
        print("   - 后端期望的字段名与Excel列名不匹配")
        print("   - 数据验证规则过于严格")
        print("   - 字段映射逻辑有bug")
        return

    # 2. 生成完整测试数据
    print("\n📊 第二步：生成完整测试数据...")
    generator = TestDataGenerator()
    test_data = generator.generate_test_data(num_records=10)  # 先用少量数据测试

    # 3. 保存为Excel文件
    output_file = "test_surgery_data.xlsx"
    test_data.to_excel(output_file, index=False)
    print(f"✅ 测试数据已保存到: {output_file}")
    print(f"📋 数据概览:")
    print(f"   - 记录数: {len(test_data)}")
    print(f"   - 列数: {len(test_data.columns)}")
    print(f"   - 日期范围: {test_data['手术日期'].min()} 到 {test_data['手术日期'].max()}")

    # 4. 显示列名对比
    print(f"\n📋 Excel列名:")
    for i, col in enumerate(test_data.columns):
        print(f"   {i+1:2d}. {col}")

    # 5. 测试导入功能
    print("\n" + "=" * 60)
    print("🧪 第三步：测试完整数据导入...")

    tester = ImportTester()

    # 测试API连通性
    if not tester.test_api_connectivity():
        print("❌ API连通性测试失败，请确保服务器正在运行")
        return

    # 测试文件导入
    success, result = tester.test_file_upload(output_file)

    if success:
        error_count = result.get('error_records', 0)
        total_count = result.get('total_records', 0)
        success_count = result.get('new_records', 0)

        if error_count == 0:
            print("🎉 测试完成! 导入功能完全正常")
        elif error_count < total_count:
            print(f"⚠️ 部分成功: {success_count}/{total_count} 记录成功导入")
            print(f"   错误记录: {error_count}")
        else:
            print(f"❌ 所有记录都失败了: {error_count}/{total_count}")
            print("💡 建议检查数据格式和验证规则")
    else:
        print("💥 测试失败! 需要检查导入功能")

    print("=" * 60)

if __name__ == "__main__":
    main()
