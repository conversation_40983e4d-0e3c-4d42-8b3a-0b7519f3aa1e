<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏收缩功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        /* 导航栏样式 */
        .sidebar {
            width: 16rem;
            background-color: #2d3748;
            color: white;
            transition: width 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed {
            width: 4rem;
        }

        .sidebar-header {
            height: 4rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #1a202c;
            border-bottom: 1px solid #4a5568;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sidebar-text {
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .sidebar-text {
            opacity: 0;
            display: none;
        }

        .sidebar-nav {
            flex: 1;
            padding: 1rem;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            position: relative;
        }

        .nav-item:hover {
            background-color: #4a5568;
        }

        .nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            flex-shrink: 0;
        }

        /* 收缩状态下的工具提示 */
        .sidebar.collapsed .nav-item:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            left: calc(100% + 0.5rem);
            top: 50%;
            transform: translateY(-50%);
            background: #1a202c;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.25rem;
            white-space: nowrap;
            z-index: 1000;
            font-size: 0.875rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            height: 4rem;
            background-color: white;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            padding: 0 1.5rem;
            gap: 1rem;
        }

        .toggle-btn {
            width: 2.5rem;
            height: 2.5rem;
            border: none;
            background: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .toggle-btn:hover {
            background-color: #f7fafc;
        }

        .content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }

        .status {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .demo-section {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧导航栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="nav-icon">❤️</div>
                    <span class="sidebar-text">麻醉质控</span>
                </div>
            </div>
            
            <div class="sidebar-nav">
                <div class="nav-item" data-tooltip="仪表盘">
                    <div class="nav-icon">📊</div>
                    <span class="sidebar-text">仪表盘</span>
                </div>
                
                <div class="nav-item" data-tooltip="数据管理">
                    <div class="nav-icon">💾</div>
                    <span class="sidebar-text">数据管理</span>
                </div>
                
                <div class="nav-item" data-tooltip="患者管理">
                    <div class="nav-icon">👥</div>
                    <span class="sidebar-text">患者管理</span>
                </div>
                
                <div class="nav-item" data-tooltip="统计分析">
                    <div class="nav-icon">📈</div>
                    <span class="sidebar-text">统计分析</span>
                </div>
                
                <div class="nav-item" data-tooltip="质控管理">
                    <div class="nav-icon">🛡️</div>
                    <span class="sidebar-text">质控管理</span>
                </div>
                
                <div class="nav-item" data-tooltip="系统管理">
                    <div class="nav-icon">⚙️</div>
                    <span class="sidebar-text">系统管理</span>
                </div>
            </div>
        </div>
        
        <!-- 右侧主内容区域 -->
        <div class="main-content">
            <div class="header">
                <button class="toggle-btn" onclick="toggleSidebar()" title="收缩/展开导航栏">
                    ☰
                </button>
                
                <div>
                    <span>首页 > 仪表盘</span>
                </div>
            </div>
            
            <div class="content">
                <div class="status">
                    <h3>功能测试状态</h3>
                    <p id="status-text">点击左上角的汉堡菜单按钮测试收缩功能</p>
                </div>
                
                <div class="demo-section">
                    <h2>导航栏收缩功能演示</h2>
                    <p>这是一个简化的演示页面，用于测试导航栏收缩功能。</p>
                    
                    <h3>功能特点：</h3>
                    <ul>
                        <li>点击汉堡菜单按钮可以收缩/展开导航栏</li>
                        <li>收缩时导航栏宽度从16rem变为4rem</li>
                        <li>收缩时文字隐藏，只显示图标</li>
                        <li>收缩状态下鼠标悬停显示工具提示</li>
                        <li>状态保存到localStorage</li>
                        <li>平滑的动画过渡效果</li>
                    </ul>
                    
                    <h3>测试步骤：</h3>
                    <ol>
                        <li>点击左上角的汉堡菜单按钮（☰）</li>
                        <li>观察导航栏是否收缩</li>
                        <li>再次点击按钮，观察是否展开</li>
                        <li>在收缩状态下，鼠标悬停在导航项上查看工具提示</li>
                        <li>刷新页面，检查状态是否保存</li>
                    </ol>
                </div>
                
                <div class="demo-section">
                    <h3>手动测试按钮</h3>
                    <button onclick="testToggle()" style="padding: 0.5rem 1rem; margin-right: 0.5rem;">手动切换</button>
                    <button onclick="checkStatus()" style="padding: 0.5rem 1rem; margin-right: 0.5rem;">检查状态</button>
                    <button onclick="resetState()" style="padding: 0.5rem 1rem;">重置状态</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换导航栏收缩状态
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const statusText = document.getElementById('status-text');
            
            if (sidebar) {
                sidebar.classList.toggle('collapsed');
                const isCollapsed = sidebar.classList.contains('collapsed');
                
                // 保存状态
                localStorage.setItem('sidebar-collapsed', isCollapsed);
                
                // 更新状态显示
                statusText.textContent = isCollapsed ? '导航栏已收缩' : '导航栏已展开';
                
                console.log('导航栏状态:', isCollapsed ? '收缩' : '展开');
            }
        }
        
        // 手动测试函数
        function testToggle() {
            toggleSidebar();
        }
        
        // 检查当前状态
        function checkStatus() {
            const sidebar = document.getElementById('sidebar');
            const isCollapsed = sidebar.classList.contains('collapsed');
            const savedState = localStorage.getItem('sidebar-collapsed');
            
            alert(`当前状态: ${isCollapsed ? '收缩' : '展开'}\n保存的状态: ${savedState}`);
        }
        
        // 重置状态
        function resetState() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.remove('collapsed');
            localStorage.removeItem('sidebar-collapsed');
            document.getElementById('status-text').textContent = '状态已重置';
        }
        
        // 页面加载时恢复状态
        document.addEventListener('DOMContentLoaded', function() {
            const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
            const sidebar = document.getElementById('sidebar');
            const statusText = document.getElementById('status-text');
            
            if (isCollapsed && sidebar) {
                sidebar.classList.add('collapsed');
                statusText.textContent = '导航栏已收缩（从localStorage恢复）';
            }
        });
    </script>
</body>
</html>
