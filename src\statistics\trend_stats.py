"""
趋势统计模块
"""
from typing import Dict, Any, List, Tuple
from database.models import DatabaseManager
from utils.logger import logger
import datetime


class TrendStatistics:
    """趋势分析统计"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_yearly_trend(self, years: int = 3) -> Dict[str, Any]:
        """获取年度趋势分析"""
        logger.info(f"获取年度趋势分析 (最近{years}年)")
        
        with self.db.get_connection() as conn:
            cursor = conn.execute('''
                SELECT 
                    strftime('%Y', surgery_date) as year,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT patient_id) as patient_count,
                    COUNT(DISTINCT anesthesiologist) as anesthesiologist_count,
                    COUNT(DISTINCT surgeon) as surgeon_count,
                    COUNT(DISTINCT department) as department_count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complication_cases
                FROM surgery_records 
                WHERE surgery_date >= date('now', '-' || ? || ' years')
                GROUP BY strftime('%Y', surgery_date)
                ORDER BY year
            ''', [years])
            
            yearly_data = []
            for row in cursor.fetchall():
                year = row[0]
                surgery_count = row[1]
                complication_cases = row[7]
                
                yearly_data.append({
                    'year': year,
                    'surgery_count': surgery_count,
                    'patient_count': row[2],
                    'anesthesiologist_count': row[3],
                    'surgeon_count': row[4],
                    'department_count': row[5],
                    'avg_duration': row[6] or 0,
                    'complication_rate': round((complication_cases / max(surgery_count, 1)) * 100, 2)
                })
            
            # 计算年度增长率
            for i in range(1, len(yearly_data)):
                prev_count = yearly_data[i-1]['surgery_count']
                curr_count = yearly_data[i]['surgery_count']
                growth_rate = ((curr_count - prev_count) / max(prev_count, 1)) * 100
                yearly_data[i]['growth_rate'] = round(growth_rate, 2)
            
            return {
                'yearly_data': yearly_data,
                'total_years': len(yearly_data)
            }
    
    def get_quarterly_trend(self, quarters: int = 8) -> Dict[str, Any]:
        """获取季度趋势分析"""
        logger.info(f"获取季度趋势分析 (最近{quarters}个季度)")
        
        with self.db.get_connection() as conn:
            cursor = conn.execute('''
                SELECT 
                    strftime('%Y', surgery_date) as year,
                    CASE 
                        WHEN strftime('%m', surgery_date) IN ('01', '02', '03') THEN 'Q1'
                        WHEN strftime('%m', surgery_date) IN ('04', '05', '06') THEN 'Q2'
                        WHEN strftime('%m', surgery_date) IN ('07', '08', '09') THEN 'Q3'
                        ELSE 'Q4'
                    END as quarter,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT patient_id) as patient_count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complication_cases
                FROM surgery_records 
                WHERE surgery_date >= date('now', '-' || ? || ' months')
                GROUP BY strftime('%Y', surgery_date), quarter
                ORDER BY year, quarter
            ''', [quarters * 3])
            
            quarterly_data = []
            for row in cursor.fetchall():
                year = row[0]
                quarter = row[1]
                surgery_count = row[2]
                complication_cases = row[5]
                
                quarterly_data.append({
                    'period': f"{year}-{quarter}",
                    'year': year,
                    'quarter': quarter,
                    'surgery_count': surgery_count,
                    'patient_count': row[3],
                    'avg_duration': row[4] or 0,
                    'complication_rate': round((complication_cases / max(surgery_count, 1)) * 100, 2)
                })
            
            return {
                'quarterly_data': quarterly_data[-quarters:],  # 只返回最近的季度数据
                'total_quarters': len(quarterly_data)
            }
    
    def get_monthly_trend_detailed(self, months: int = 12) -> Dict[str, Any]:
        """获取详细月度趋势分析"""
        logger.info(f"获取详细月度趋势分析 (最近{months}个月)")
        
        with self.db.get_connection() as conn:
            cursor = conn.execute('''
                SELECT 
                    strftime('%Y-%m', surgery_date) as month,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT patient_id) as patient_count,
                    COUNT(DISTINCT anesthesiologist) as anesthesiologist_count,
                    COUNT(DISTINCT surgeon) as surgeon_count,
                    COUNT(DISTINCT department) as department_count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration,
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complication_cases,
                    COUNT(CASE WHEN postoperative_analgesia IS NOT NULL AND postoperative_analgesia != '' AND postoperative_analgesia != '否' THEN 1 END) as analgesia_cases
                FROM surgery_records 
                WHERE surgery_date >= date('now', '-' || ? || ' months')
                GROUP BY strftime('%Y-%m', surgery_date)
                ORDER BY month
            ''', [months])
            
            monthly_data = []
            for row in cursor.fetchall():
                month = row[0]
                surgery_count = row[1]
                complication_cases = row[8]
                analgesia_cases = row[9]
                
                monthly_data.append({
                    'month': month,
                    'surgery_count': surgery_count,
                    'patient_count': row[2],
                    'anesthesiologist_count': row[3],
                    'surgeon_count': row[4],
                    'department_count': row[5],
                    'avg_duration': row[6] or 0,
                    'total_duration': row[7] or 0,
                    'complication_rate': round((complication_cases / max(surgery_count, 1)) * 100, 2),
                    'analgesia_rate': round((analgesia_cases / max(surgery_count, 1)) * 100, 2),
                    'avg_surgeries_per_day': round(surgery_count / 30, 2)  # 假设每月30天
                })
            
            return {
                'monthly_data': monthly_data,
                'total_months': len(monthly_data)
            }
    
    def get_anesthesia_method_trend(self, months: int = 12) -> Dict[str, List[Dict[str, Any]]]:
        """获取麻醉方法趋势分析"""
        logger.info(f"获取麻醉方法趋势分析 (最近{months}个月)")
        
        with self.db.get_connection() as conn:
            cursor = conn.execute('''
                SELECT 
                    strftime('%Y-%m', surgery_date) as month,
                    anesthesia_method,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY strftime('%Y-%m', surgery_date)), 2) as percentage
                FROM surgery_records 
                WHERE surgery_date >= date('now', '-' || ? || ' months')
                AND anesthesia_method IS NOT NULL AND anesthesia_method != ''
                GROUP BY strftime('%Y-%m', surgery_date), anesthesia_method
                ORDER BY month, count DESC
            ''', [months])
            
            trend_data = {}
            for row in cursor.fetchall():
                month = row[0]
                method = row[1]
                count = row[2]
                percentage = row[3]
                
                if method not in trend_data:
                    trend_data[method] = []
                
                trend_data[method].append({
                    'month': month,
                    'count': count,
                    'percentage': percentage
                })
            
            return trend_data
    
    def get_department_trend(self, months: int = 12) -> Dict[str, List[Dict[str, Any]]]:
        """获取科室工作量趋势分析"""
        logger.info(f"获取科室工作量趋势分析 (最近{months}个月)")
        
        with self.db.get_connection() as conn:
            cursor = conn.execute('''
                SELECT 
                    strftime('%Y-%m', surgery_date) as month,
                    department,
                    COUNT(*) as count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY strftime('%Y-%m', surgery_date)), 2) as percentage
                FROM surgery_records 
                WHERE surgery_date >= date('now', '-' || ? || ' months')
                AND department IS NOT NULL AND department != ''
                GROUP BY strftime('%Y-%m', surgery_date), department
                ORDER BY month, count DESC
            ''', [months])
            
            trend_data = {}
            for row in cursor.fetchall():
                month = row[0]
                department = row[1]
                count = row[2]
                avg_duration = row[3] or 0
                percentage = row[4]
                
                if department not in trend_data:
                    trend_data[department] = []
                
                trend_data[department].append({
                    'month': month,
                    'count': count,
                    'avg_duration': avg_duration,
                    'percentage': percentage
                })
            
            return trend_data
    
    def get_quality_trend(self, months: int = 12) -> List[Dict[str, Any]]:
        """获取质量指标趋势分析"""
        logger.info(f"获取质量指标趋势分析 (最近{months}个月)")
        
        with self.db.get_connection() as conn:
            cursor = conn.execute('''
                SELECT 
                    strftime('%Y-%m', surgery_date) as month,
                    COUNT(*) as total_surgeries,
                    -- 并发症率
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complication_cases,
                    -- 术后镇痛使用率
                    COUNT(CASE WHEN postoperative_analgesia IS NOT NULL AND postoperative_analgesia != '' AND postoperative_analgesia != '否' THEN 1 END) as analgesia_cases,
                    -- 数据完整性
                    COUNT(CASE WHEN surgery_name IS NULL OR surgery_name = '' THEN 1 END) as missing_surgery_name,
                    COUNT(CASE WHEN anesthesia_method IS NULL OR anesthesia_method = '' THEN 1 END) as missing_anesthesia_method,
                    COUNT(CASE WHEN surgeon IS NULL OR surgeon = '' THEN 1 END) as missing_surgeon,
                    COUNT(CASE WHEN anesthesiologist IS NULL OR anesthesiologist = '' THEN 1 END) as missing_anesthesiologist,
                    -- 平均手术时长
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration
                FROM surgery_records 
                WHERE surgery_date >= date('now', '-' || ? || ' months')
                GROUP BY strftime('%Y-%m', surgery_date)
                ORDER BY month
            ''', [months])
            
            quality_trend = []
            for row in cursor.fetchall():
                month = row[0]
                total = row[1]
                complication_cases = row[2]
                analgesia_cases = row[3]
                missing_surgery_name = row[4]
                missing_anesthesia_method = row[5]
                missing_surgeon = row[6]
                missing_anesthesiologist = row[7]
                avg_duration = row[8] or 0
                
                # 计算各种率
                complication_rate = (complication_cases / max(total, 1)) * 100
                analgesia_rate = (analgesia_cases / max(total, 1)) * 100
                
                # 数据完整性评分
                total_fields = total * 4  # 4个关键字段
                missing_total = missing_surgery_name + missing_anesthesia_method + missing_surgeon + missing_anesthesiologist
                completeness_score = ((total_fields - missing_total) / max(total_fields, 1)) * 100
                
                quality_trend.append({
                    'month': month,
                    'total_surgeries': total,
                    'complication_rate': round(complication_rate, 2),
                    'analgesia_rate': round(analgesia_rate, 2),
                    'completeness_score': round(completeness_score, 2),
                    'avg_duration': avg_duration
                })
            
            return quality_trend
    
    def get_workload_forecast(self, months: int = 6) -> Dict[str, Any]:
        """获取工作量预测分析"""
        logger.info(f"获取工作量预测分析 (未来{months}个月)")
        
        with self.db.get_connection() as conn:
            # 获取历史数据用于预测
            cursor = conn.execute('''
                SELECT 
                    strftime('%Y-%m', surgery_date) as month,
                    COUNT(*) as surgery_count
                FROM surgery_records 
                WHERE surgery_date >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', surgery_date)
                ORDER BY month
            ''')
            
            historical_data = []
            for row in cursor.fetchall():
                historical_data.append({
                    'month': row[0],
                    'surgery_count': row[1]
                })
            
            if len(historical_data) < 3:
                return {
                    'historical_data': historical_data,
                    'forecast_data': [],
                    'trend_analysis': '数据不足，无法进行预测'
                }
            
            # 简单的线性趋势预测
            counts = [data['surgery_count'] for data in historical_data]
            avg_count = sum(counts) / len(counts)
            
            # 计算趋势
            if len(counts) >= 6:
                recent_avg = sum(counts[-6:]) / 6
                early_avg = sum(counts[:6]) / 6
                trend_direction = "上升" if recent_avg > early_avg else "下降" if recent_avg < early_avg else "稳定"
                trend_rate = abs((recent_avg - early_avg) / max(early_avg, 1)) * 100
            else:
                trend_direction = "稳定"
                trend_rate = 0
            
            # 生成预测数据
            forecast_data = []
            base_date = datetime.datetime.now()
            
            for i in range(1, months + 1):
                future_date = base_date + datetime.timedelta(days=30 * i)
                future_month = future_date.strftime('%Y-%m')
                
                # 简单预测：基于平均值和趋势
                if trend_direction == "上升":
                    predicted_count = int(avg_count * (1 + trend_rate / 100 * i / 12))
                elif trend_direction == "下降":
                    predicted_count = int(avg_count * (1 - trend_rate / 100 * i / 12))
                else:
                    predicted_count = int(avg_count)
                
                forecast_data.append({
                    'month': future_month,
                    'predicted_count': max(predicted_count, 0),
                    'confidence': max(90 - i * 10, 50)  # 置信度随时间递减
                })
            
            return {
                'historical_data': historical_data,
                'forecast_data': forecast_data,
                'trend_analysis': f"趋势: {trend_direction}, 变化率: {round(trend_rate, 2)}%",
                'avg_monthly_count': round(avg_count, 0)
            }
    
    def _build_date_filter(self, start_date: str = None, end_date: str = None) -> Tuple[str, List]:
        """构建日期过滤条件"""
        conditions = []
        params = []
        
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
    
    def _build_where_clause(self, start_date: str = None, end_date: str = None, additional_conditions: List[str] = None) -> Tuple[str, List]:
        """构建完整的WHERE子句"""
        conditions = []
        params = []
        
        # 添加日期条件
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        # 添加额外条件
        if additional_conditions:
            conditions.extend(additional_conditions)
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
