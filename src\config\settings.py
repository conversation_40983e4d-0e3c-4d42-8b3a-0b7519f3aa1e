"""
应用配置设置
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

# 数据目录
DATA_DIR = PROJECT_ROOT / "data"
INPUT_DIR = DATA_DIR / "input"
OUTPUT_DIR = DATA_DIR / "output"
UPLOAD_DIR = PROJECT_ROOT / "uploads"

# 日志配置
LOG_DIR = PROJECT_ROOT / "logs"
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Web服务配置
WEB_HOST = "127.0.0.1"
WEB_PORT = 5000
DEBUG = True

# 文件处理配置
ALLOWED_EXTENSIONS = {'.xlsx', '.xls'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

# 麻醉方式标准化映射
ANESTHESIA_MAPPING = {
    "全身": "全身麻醉",
    "硬": "椎管内麻醉",
    "神经": "神经阻滞麻醉",
    "静脉": "静脉麻醉",
    "骶管": "椎管内麻醉",
    "局部": "局部麻醉",
    "椎管": "椎管内麻醉",
    "超声": "",
    "吸入": "全身麻醉",
    "表面": "",
    "静吸": "全身麻醉",
    "蛛网膜": "椎管内麻醉"
}

# 确保目录存在
for directory in [DATA_DIR, INPUT_DIR, OUTPUT_DIR, UPLOAD_DIR, LOG_DIR]:
    directory.mkdir(parents=True, exist_ok=True)
