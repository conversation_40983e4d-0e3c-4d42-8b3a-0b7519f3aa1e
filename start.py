#!/usr/bin/env python3
"""
麻醉质控数据管理系统启动脚本
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def main():
    """启动Web应用"""
    print("🚀 启动麻醉质控数据管理系统...")
    print("=" * 50)
    
    try:
        # 导入并启动Web应用
        from web.app import app
        
        print("✅ 系统初始化完成")
        print("🌐 Web服务器启动中...")
        print("📱 访问地址: http://127.0.0.1:5000")
        print("⏹️  按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 启动Flask应用 - 高性能配置
        print("🔧 使用优化的Flask开发服务器")
        print("💡 建议: 生产环境请使用 python start_production.py")

        # 设置Flask配置 - 开发模式
        app.config.update(
            # 启用调试模式以支持热加载
            DEBUG=True,
            TESTING=False,

            # 启用模板自动重载
            TEMPLATES_AUTO_RELOAD=True,

            # 优化JSON序列化
            JSON_SORT_KEYS=False,
            JSONIFY_PRETTYPRINT_REGULAR=False,

            # 设置最大内容长度
            MAX_CONTENT_LENGTH=50 * 1024 * 1024,  # 50MB

            # 优化会话配置
            PERMANENT_SESSION_LIFETIME=3600,  # 1小时

            # 开发环境功能
            EXPLAIN_TEMPLATE_LOADING=False,
            PRESERVE_CONTEXT_ON_EXCEPTION=True  # 开发环境启用异常保留
        )

        app.run(
            host='127.0.0.1',  # 使用127.0.0.1避免DNS解析延迟
            port=5000,
            debug=True,  # 启用调试模式
            threaded=True,
            processes=1,
            use_reloader=True,  # 启用代码热加载
            use_debugger=True,  # 启用调试器
            passthrough_errors=False,
            ssl_context=None,
            request_handler=None,
            extra_files=None
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
