2025-08-25 10:29:09,013 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-25 10:29:11,426 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,426 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,427 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-25 10:29:11,427 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-25 10:29:11,427 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-25 10:29:11,428 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-25 10:29:11,429 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,429 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,431 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,431 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,432 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,433 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,434 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,434 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,436 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,436 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,437 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,438 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,439 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,439 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,440 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:11,440 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:11,446 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-25 10:29:11,448 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-25 10:29:11,448 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-25 10:29:11,484 - anesthesia_qc - INFO - WebSocket服务初始化完成
2025-08-25 10:29:11,484 - anesthesia_qc - INFO - WebSocket服务已初始化
2025-08-25 10:29:11,772 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-25 10:29:12,197 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,198 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,199 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-25 10:29:12,199 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-25 10:29:12,199 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-25 10:29:12,200 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-25 10:29:12,201 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,201 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,202 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,202 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,204 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,204 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,205 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,206 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,207 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,207 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,208 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,210 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,212 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,212 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,214 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:12,216 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:12,219 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-25 10:29:12,219 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-25 10:29:12,220 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-25 10:29:12,249 - anesthesia_qc - INFO - WebSocket服务初始化完成
2025-08-25 10:29:12,249 - anesthesia_qc - INFO - WebSocket服务已初始化
2025-08-25 10:29:32,893 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:32,898 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 10:29:32,908 - anesthesia_qc - INFO - 数据库迁移完成
2025-08-25 10:29:32,908 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-25 11:40:39,741 - anesthesia_qc - INFO - 客户端连接: 6r1godv_7Lbtxs25AAAB
2025-08-25 11:40:55,788 - anesthesia_qc - INFO - 客户端 6r1godv_7Lbtxs25AAAB 加入导入房间: import_1756093255787_ik4kx9ble
2025-08-25 11:40:55,819 - anesthesia_qc - INFO - 获取仪表盘统计数据
2025-08-25 11:40:55,821 - anesthesia_qc - INFO - 获取概览统计: None - None
2025-08-25 11:40:55,821 - anesthesia_qc - INFO - 获取麻醉方法分布统计
2025-08-25 11:40:55,821 - anesthesia_qc - INFO - 获取手术类型分布统计
2025-08-25 11:40:55,821 - anesthesia_qc - INFO - 获取科室概览统计
2025-08-25 11:40:55,824 - anesthesia_qc - INFO - 获取年龄分布统计
2025-08-25 11:40:55,825 - anesthesia_qc - WARNING - 数据库中没有age字段，返回空的年龄分布
2025-08-25 11:40:55,825 - anesthesia_qc - INFO - 获取性别分布统计
2025-08-25 11:40:55,826 - anesthesia_qc - WARNING - 数据库中没有gender字段，返回空的性别分布
2025-08-25 11:40:55,826 - anesthesia_qc - INFO - 获取每日手术量趋势
2025-08-25 11:40:55,828 - anesthesia_qc - INFO - 获取数据质量评分
2025-08-25 11:40:55,828 - anesthesia_qc - INFO - 获取数据完整性分析
2025-08-25 11:40:55,828 - anesthesia_qc - INFO - 获取数据一致性分析
2025-08-25 11:40:55,839 - anesthesia_qc - INFO - 文件上传成功: d:\code\AnesthesiaQCDataManagement\uploads\20250825_114055_test_surgery_data.xlsx (大小: 8114 bytes)
2025-08-25 11:40:56,240 - anesthesia_qc - INFO - 🚀 开始导入文件: 20250825_114055_test_surgery_data.xlsx, 批次ID: 750a0f4a-b308-4418-a5aa-42ef950f8f01, 脱敏: True
2025-08-25 11:40:56,240 - anesthesia_qc - INFO - 📋 导入配置: {'enablePrivacy': True, 'enableCleaning': True, 'skipDuplicates': True, 'validateData': True, 'autoRepair': False, 'generateReport': True, 'duplicateStrategy': 'skip', 'errorStrategy': 'skip', 'batchSize': 500}
2025-08-25 11:40:56,240 - anesthesia_qc - INFO - 🗺️ 字段映射: {}
2025-08-25 11:40:56,250 - anesthesia_qc - INFO - 开始执行: 读取Excel文件
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 📊 读取到 10 条记录，43 列
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 🏷️ 标准化列名: 住院号 -> patient_id
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 🏷️ 标准化列名: 患者姓名 -> patient_name
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 🏷️ 标准化列名: 手术日期 -> surgery_date
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 🏷️ 标准化列名: 手术类型 -> surgery_type
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 🏷️ 标准化列名: 手术名称 -> surgery_name
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 🏷️ 标准化列名: 麻醉方法 -> anesthesia_method
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 🏷️ 标准化列名: 年龄 -> age
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 🏷️ 标准化列名: 性别 -> gender
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 完成执行: 读取Excel文件 | 耗时: 0.02秒 | 内存变化: +0.3MB
2025-08-25 11:40:56,270 - anesthesia_qc - INFO - 开始执行: 数据清洗
2025-08-25 11:40:56,280 - anesthesia_qc - INFO - 开始清洗麻醉方法...
2025-08-25 11:40:56,280 - anesthesia_qc - INFO - 麻醉方法清洗完成，标准化为 3 种方法
2025-08-25 11:40:56,280 - anesthesia_qc - INFO - 🧹 数据清洗完成，有效记录: 10
2025-08-25 11:40:56,280 - anesthesia_qc - INFO - 完成执行: 数据清洗 | 耗时: 0.01秒 | 内存变化: +0.4MB
2025-08-25 11:40:56,280 - anesthesia_qc - INFO - 开始执行: 数据验证
2025-08-25 11:40:56,290 - anesthesia_qc - INFO - 完成执行: 数据验证 | 耗时: 0.01秒 | 内存变化: +0.1MB
2025-08-25 11:40:56,290 - anesthesia_qc - INFO - 开始执行: 导入数据库
2025-08-25 11:40:56,290 - anesthesia_qc - INFO - 📦 开始批量处理，批次大小: 500
2025-08-25 11:40:56,290 - anesthesia_qc - INFO - 🔄 处理批次 1: 10 条记录
2025-08-25 11:40:56,388 - anesthesia_qc - INFO - 完成执行: 导入数据库 | 耗时: 0.10秒 | 内存变化: +0.1MB
2025-08-25 11:40:56,409 - anesthesia_qc - INFO - ✅ 文件导入完成: 新增10条, 重复0条, 错误0条
2025-08-25 11:40:56,409 - anesthesia_qc - INFO - import_excel_file 执行时间: 0.17秒
2025-08-25 11:40:56,409 - anesthesia_qc - INFO - 数据导入完成: {'success': True, 'batch_id': '750a0f4a-b308-4418-a5aa-42ef950f8f01', 'filename': '20250825_114055_test_surgery_data.xlsx', 'total_records': 10, 'new_records': 10, 'duplicate_records': 0, 'error_records': 0, 'privacy_enabled': True, 'privacy_summary': {'anonymized_names_count': 46, 'anonymized_ids_count': 10, 'field_stats': {}, 'security_mode': True, 'original_data_deleted': True, 'warning': '原始敏感数据已被永久删除，无法恢复'}, 'file_info': {'original_name': 'test_surgery_data.xlsx', 'size': 8114, 'upload_time': '2025-08-25T11:40:56.409358'}}
2025-08-25 11:40:56,419 - anesthesia_qc - INFO - 临时文件已清理: d:\code\AnesthesiaQCDataManagement\uploads\20250825_114055_test_surgery_data.xlsx
2025-08-25 11:45:29,125 - anesthesia_qc - ERROR - 请求异常: unknown - WebSocketService._register_handlers.<locals>.handle_disconnect() takes 0 positional arguments but 1 was given
2025-08-25 11:45:29,125 - anesthesia_qc - INFO - 客户端断开连接: 6r1godv_7Lbtxs25AAAB
