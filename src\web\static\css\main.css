/* 麻醉质控数据管理系统 - 自定义样式 */

/* 全局样式 - 使用DaisyUI主题变量 */
:root {
    --sidebar-width: 20rem;
    --sidebar-collapsed-width: 4.5rem;
    --header-height: 4rem;
    --transition-duration: 0.3s;
    --icon-size: 1.125rem; /* 18px */

    /* 自定义主题变量 */
    --chart-bg: hsl(var(--b2));
    --chart-placeholder: hsl(var(--bc) / 0.4);
    --activity-bg: hsl(var(--b2));
    --hover-bg: hsl(var(--b3));
    --border-color: hsl(var(--b3));
}

/* 平滑过渡效果 - 移除全局transition，避免影响交互 */

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--bc) / 0.3);
}

/* DaisyUI Drawer 自定义样式 */
.drawer-side {
    z-index: 40;
}

.drawer-content {
    min-height: 100vh;
}

/* 标题栏样式 */
.header-container {
    margin-left: 0;
    transition: margin-left var(--transition-duration) ease;
}

.header-icon {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-icon i {
    font-size: var(--icon-size);
}

.header-status {
    @apply flex items-center space-x-2;
}

/* 面包屑导航样式 */
.breadcrumbs ol li:not(:last-child):after {
    content: ">";
    margin: 0 0.5rem;
    opacity: 0.5;
}

.breadcrumb-item {
    @apply text-base-content/60 hover:text-primary transition-colors flex items-center;
}

.breadcrumbs ol li:last-child .breadcrumb-item {
    @apply text-base-content font-medium;
}

/* 布局调整 - 简化版本 */
.flex-1 {
    transition: all var(--transition-duration) ease;
}

/* 主题切换按钮样式 */
.theme-option {
    @apply flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-base-200 transition-colors;
}

.theme-option.active {
    @apply bg-primary text-primary-content;
}

/* 自定义卡片样式已移除，现在使用DaisyUI原生card组件 */

/* 自定义统计卡片样式已移除，现在使用DaisyUI原生stats组件 */

/* 表格样式 */
.table-custom {
    @apply table table-zebra w-full bg-base-100 rounded-lg overflow-hidden shadow-sm;
}

.table-custom thead {
    @apply bg-base-200;
}

.table-custom thead th {
    @apply text-base-content font-semibold py-4 px-6 text-left;
}

.table-custom tbody td {
    @apply py-4 px-6 border-b border-base-200;
}

.table-custom tbody tr:hover {
    @apply bg-base-100;
}

/* 按钮样式增强 */
.btn-primary {
    @apply shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200;
}

.btn-secondary {
    @apply shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200;
}

/* 自定义表单样式已移除，现在使用DaisyUI原生form-control组件 */

/* 加载动画 */
.loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary;
}

/* 通知样式 */
.alert-custom {
    @apply alert shadow-lg rounded-lg border-l-4;
}

.alert-custom.alert-info {
    @apply border-l-info bg-info/10 text-info-content;
}

.alert-custom.alert-success {
    @apply border-l-success bg-success/10 text-success-content;
}

.alert-custom.alert-warning {
    @apply border-l-warning bg-warning/10 text-warning-content;
}

.alert-custom.alert-error {
    @apply border-l-error bg-error/10 text-error-content;
}

/* 模态框样式 */
.modal-custom .modal-box {
    @apply max-w-2xl w-full mx-4 bg-base-100 shadow-2xl rounded-xl;
}

.modal-custom .modal-action {
    @apply pt-6 border-t border-base-200;
}

/* 响应式设计 - 暂时移除以避免冲突 */

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

/* 工具类 */
.text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
}

.glass-effect {
    @apply backdrop-blur-sm bg-base-100/80 border border-base-200/50;
}

.shadow-custom {
    @apply shadow-xl;
}

/* 主题相关的工具类 */
.chart-bg {
    background-color: var(--chart-bg);
}

.chart-placeholder {
    color: var(--chart-placeholder);
}

.activity-bg {
    background-color: var(--activity-bg);
}

.hover-bg:hover {
    background-color: var(--hover-bg);
}

.theme-border {
    border-color: var(--border-color);
}

/* 统计卡片主题适配 */
.stat-primary {
    @apply bg-primary text-primary-content;
}

.stat-secondary {
    @apply bg-secondary text-secondary-content;
}

.stat-accent {
    @apply bg-accent text-accent-content;
}

.stat-success {
    @apply bg-success text-success-content;
}

.stat-info {
    @apply bg-info text-info-content;
}

.stat-warning {
    @apply bg-warning text-warning-content;
}

.stat-error {
    @apply bg-error text-error-content;
}

/* 主题适配的阴影 */
.text-shadow {
    text-shadow: 0 1px 2px hsl(var(--bc) / 0.1);
}

.shadow-soft {
    box-shadow: 0 2px 15px -3px hsl(var(--bc) / 0.07), 0 10px 20px -2px hsl(var(--bc) / 0.04);
}

/* 打印样式 */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }
}
