<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据导入 - 麻醉质控数据管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.19/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        /* Toast通知系统 - 确保始终在最前面 */
        .toast {
            z-index: 9999 !important;
            position: fixed !important;
        }

        .toast .alert {
            z-index: 10000 !important;
            position: relative !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
            backdrop-filter: blur(10px) !important;
        }

        /* 拖拽区域样式优化 */
        #dropZone.border-primary {
            border-color: hsl(var(--p)) !important;
            background-color: hsl(var(--p) / 0.1) !important;
        }

        /* 步骤指示器动画 */
        .step {
            transition: all 0.3s ease;
        }

        .step.step-primary {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="bg-base-100">
    <!-- 导航栏 -->
    <div class="navbar bg-base-200 shadow-lg">
        <div class="flex-1">
            <h1 class="text-xl font-bold">数据导入</h1>
        </div>
        <div class="flex-none">
            <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-ghost">
                    <i class="fas fa-ellipsis-v"></i>
                </div>
                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                    <li><a onclick="downloadTemplate()"><i class="fas fa-download"></i> 下载模板</a></li>
                    <li><a onclick="viewHistory()"><i class="fas fa-history"></i> 导入历史</a></li>
                    <li><a onclick="viewHelp()"><i class="fas fa-question-circle"></i> 帮助文档</a></li>
                    <li><hr></li>
                    <li><a onclick="testToast()"><i class="fas fa-bell"></i> 测试通知</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container mx-auto p-6 max-w-4xl">
        <!-- DaisyUI Steps 组件 -->
        <ul class="steps steps-horizontal w-full mb-8">
            <li class="step step-primary" data-content="1">选择文件</li>
            <li class="step" data-content="2">预览数据</li>
            <li class="step" data-content="3">配置导入</li>
            <li class="step" data-content="4">完成导入</li>
        </ul>

        <!-- 步骤内容容器 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <!-- 步骤1: 文件选择 -->
                <div id="step-1" class="step-content">
                    <h2 class="card-title mb-6">
                        <i class="fas fa-file-upload text-primary"></i>
                        选择要导入的文件
                    </h2>

                    <!-- 文件上传区域 -->
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text">选择Excel文件</span>
                            <span class="label-text-alt">支持 .xlsx, .xls 格式</span>
                        </label>
                        <input type="file" 
                               class="file-input file-input-bordered file-input-primary w-full" 
                               accept=".xlsx,.xls"
                               id="fileInput"
                               onchange="handleFileSelect(this)">
                    </div>

                    <!-- 或者拖拽上传 -->
                    <div class="divider">或者</div>
                    
                    <div class="border-2 border-dashed border-base-300 rounded-lg p-8 text-center hover:border-primary transition-colors cursor-pointer"
                         id="dropZone"
                         ondrop="handleDrop(event)" 
                         ondragover="handleDragOver(event)"
                         ondragleave="handleDragLeave(event)">
                        <i class="fas fa-cloud-upload-alt text-6xl text-base-content/40 mb-4"></i>
                        <p class="text-lg font-medium mb-2">拖拽文件到此处</p>
                        <p class="text-sm text-base-content/60">支持 .xlsx, .xls 格式，最大50MB</p>
                    </div>

                    <!-- 文件信息显示 -->
                    <div id="fileInfo" class="alert alert-success mt-6 hidden">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <h4 class="font-bold">文件已选择</h4>
                            <div id="fileDetails"></div>
                        </div>
                        <button class="btn btn-sm btn-outline" onclick="clearFile()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="card-actions justify-end mt-6">
                        <button class="btn btn-primary" onclick="nextStep()" id="nextBtn1" disabled>
                            下一步 <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>

                <!-- 步骤2: 数据预览 -->
                <div id="step-2" class="step-content hidden">
                    <h2 class="card-title mb-6">
                        <i class="fas fa-table text-primary"></i>
                        数据预览
                    </h2>

                    <!-- 数据统计 -->
                    <div class="stats shadow w-full mb-6">
                        <div class="stat">
                            <div class="stat-title">总记录数</div>
                            <div class="stat-value text-primary" id="totalRecords">0</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">有效记录</div>
                            <div class="stat-value text-success" id="validRecords">0</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">无效记录</div>
                            <div class="stat-value text-warning" id="invalidRecords">0</div>
                        </div>
                    </div>

                    <!-- 字段说明 -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">字段说明</h3>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <div>
                                <p class="font-semibold">系统将自动识别以下字段：</p>
                                <ul class="mt-2 text-sm">
                                    <li>• <strong>患者信息</strong>：患者ID、患者姓名、病人ID、住院号</li>
                                    <li>• <strong>手术信息</strong>：手术日期、手术时间、手术名称</li>
                                    <li>• <strong>麻醉信息</strong>：麻醉方式、麻醉方法、麻醉医生</li>
                                </ul>
                                <p class="mt-2 text-sm opacity-75">无需手动配置字段映射，系统会智能匹配列名</p>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览表格 -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">数据预览（前10行）</h3>
                        <div class="overflow-x-auto">
                            <table class="table table-zebra w-full">
                                <thead id="previewHeader"></thead>
                                <tbody id="previewBody"></tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 验证结果 -->
                    <div id="validationResults" class="mb-6"></div>

                    <!-- 操作按钮 -->
                    <div class="card-actions justify-between">
                        <button class="btn btn-outline" onclick="prevStep()">
                            <i class="fas fa-arrow-left mr-2"></i> 上一步
                        </button>
                        <button class="btn btn-primary" onclick="nextStep()" id="nextBtn2">
                            下一步 <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>

                <!-- 步骤3: 配置导入 -->
                <div id="step-3" class="step-content hidden">
                    <h2 class="card-title mb-6">
                        <i class="fas fa-cog text-primary"></i>
                        导入配置
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 数据处理选项 -->
                        <div class="card bg-base-200">
                            <div class="card-body">
                                <h3 class="card-title text-base">数据处理</h3>
                                <div class="form-control">
                                    <label class="label cursor-pointer">
                                        <span class="label-text">启用数据脱敏</span>
                                        <input type="checkbox" class="toggle toggle-primary" id="enablePrivacy" checked>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer">
                                        <span class="label-text">启用数据清洗</span>
                                        <input type="checkbox" class="toggle toggle-primary" id="enableCleaning" checked>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer">
                                        <span class="label-text">跳过重复记录</span>
                                        <input type="checkbox" class="toggle toggle-primary" id="skipDuplicates" checked>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer">
                                        <span class="label-text">启用数据验证</span>
                                        <input type="checkbox" class="toggle toggle-primary" id="validateData" checked>
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer">
                                        <span class="label-text">自动数据修复</span>
                                        <input type="checkbox" class="toggle toggle-secondary" id="autoRepair">
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="label cursor-pointer">
                                        <span class="label-text">生成导入报告</span>
                                        <input type="checkbox" class="toggle toggle-accent" id="generateReport" checked>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 错误处理选项 -->
                        <div class="card bg-base-200">
                            <div class="card-body">
                                <h3 class="card-title text-base">错误处理</h3>
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">重复记录策略</span>
                                    </label>
                                    <select class="select select-bordered" id="duplicateStrategy">
                                        <option value="skip">跳过重复记录</option>
                                        <option value="update">更新重复记录</option>
                                        <option value="error">报错停止</option>
                                    </select>
                                </div>
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">错误处理策略</span>
                                    </label>
                                    <select class="select select-bordered" id="errorStrategy">
                                        <option value="skip">跳过错误记录</option>
                                        <option value="stop">遇错停止</option>
                                    </select>
                                </div>
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">批处理大小</span>
                                    </label>
                                    <input type="range" min="100" max="2000" value="500" class="range range-primary" id="batchSize">
                                    <div class="w-full flex justify-between text-xs px-2">
                                        <span>100</span>
                                        <span>1000</span>
                                        <span>2000</span>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>

                    <!-- 操作按钮 -->
                    <div class="card-actions justify-between mt-6">
                        <button class="btn btn-outline" onclick="prevStep()">
                            <i class="fas fa-arrow-left mr-2"></i> 上一步
                        </button>
                        <button class="btn btn-primary" onclick="startImport()">
                            <i class="fas fa-upload mr-2"></i> 开始导入
                        </button>
                    </div>
                </div>

                <!-- 步骤4: 导入进度 -->
                <div id="step-4" class="step-content hidden">
                    <h2 class="card-title mb-6">
                        <i class="fas fa-spinner fa-spin text-primary"></i>
                        正在导入数据
                    </h2>

                    <!-- 进度条 -->
                    <div class="mb-6">
                        <div class="flex justify-between text-sm mb-2">
                            <span id="progressText">准备中...</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <progress class="progress progress-primary w-full" value="0" max="100" id="importProgress"></progress>
                    </div>

                    <!-- 统计信息 -->
                    <div class="stats shadow w-full mb-6">
                        <div class="stat">
                            <div class="stat-title">已处理</div>
                            <div class="stat-value text-primary" id="processedCount">0</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">成功</div>
                            <div class="stat-value text-success" id="successCount">0</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">跳过</div>
                            <div class="stat-value text-warning" id="skipCount">0</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">错误</div>
                            <div class="stat-value text-error" id="errorCount">0</div>
                        </div>
                    </div>

                    <!-- 导入日志 -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">导入日志</h3>
                        <div class="mockup-code bg-base-200 h-48 overflow-y-auto" id="importLog">
                            <pre data-prefix="$"><code>等待开始...</code></pre>
                        </div>
                    </div>

                    <!-- 导入结果 -->
                    <div id="importResult" class="alert alert-success hidden">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <h4 class="font-bold">导入完成</h4>
                            <div id="resultSummary"></div>
                        </div>
                    </div>

                    <!-- 导入错误 -->
                    <div id="importError" class="alert alert-error hidden">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div>
                            <h4 class="font-bold">导入失败</h4>
                            <p id="errorMessage"></p>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="card-actions justify-between mt-6">
                        <button class="btn btn-outline" onclick="resetImport()">
                            <i class="fas fa-redo mr-2"></i> 重新导入
                        </button>
                        <button class="btn btn-primary" onclick="viewImportedData()">
                            <i class="fas fa-eye mr-2"></i> 预览导入数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- DaisyUI Toast 通知容器 -->
    <div class="toast toast-top toast-end z-50" id="toastContainer"></div>

    <script>
        // 全局变量
        let currentStep = 1;
        let selectedFile = null;
        let previewData = null;

        // WebSocket连接
        let socket = null;
        let currentImportId = null;
        let currentImportResult = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据导入页面加载完成');
            initWebSocket();
        });

        // 初始化WebSocket连接
        function initWebSocket() {
            if (typeof io !== 'undefined') {
                socket = io();

                socket.on('connect', function() {
                    console.log('WebSocket连接成功');
                    addImportLog('🔗 WebSocket连接已建立', 'info');
                });

                socket.on('disconnect', function() {
                    console.log('WebSocket连接断开');
                    addImportLog('🔌 WebSocket连接已断开', 'warning');
                });

                // 监听导入进度更新
                socket.on('import_progress', function(data) {
                    console.log('收到进度更新:', data);
                    if (data.percent !== undefined) {
                        updateProgress(data.percent, data.status || '正在处理...');
                    }
                    if (data.processed_records !== undefined) {
                        document.getElementById('processedCount').textContent = data.processed_records;
                    }
                });

                // 监听导入日志
                socket.on('import_log', function(data) {
                    console.log('收到日志:', data);
                    addImportLog(data.message || data, data.level || 'info');
                });

                // 监听导入完成
                socket.on('import_complete', function(data) {
                    console.log('导入完成:', data);
                    showImportSuccess(data);
                });

                // 监听导入错误
                socket.on('import_error', function(data) {
                    console.log('导入错误:', data);
                    showImportError(data.message || data.error || '未知错误');
                });
            } else {
                console.warn('Socket.IO未加载，无法使用实时进度功能');
            }
        }

        // 文件选择处理
        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                processFile(file);
            }
        }

        // 拖拽处理
        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();

            const dropZone = document.getElementById('dropZone');
            dropZone.classList.remove('border-primary');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            document.getElementById('dropZone').classList.add('border-primary');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.stopPropagation();
            document.getElementById('dropZone').classList.remove('border-primary');
        }

        // 处理文件
        function processFile(file) {
            // 验证文件类型
            if (!file.name.match(/\.(xlsx|xls)$/i)) {
                showToast('❌ 请选择Excel文件（.xlsx或.xls格式）', 'error');
                return;
            }

            // 验证文件大小（50MB）
            if (file.size > 50 * 1024 * 1024) {
                showToast('⚠️ 文件大小不能超过50MB', 'warning');
                return;
            }

            selectedFile = file;
            showFileInfo(file);
            document.getElementById('nextBtn1').disabled = false;
        }

        // 显示文件信息
        function showFileInfo(file) {
            const fileInfo = document.getElementById('fileInfo');
            const fileDetails = document.getElementById('fileDetails');

            fileDetails.innerHTML = `
                <p><strong>文件名：</strong>${file.name}</p>
                <p><strong>文件大小：</strong>${formatFileSize(file.size)}</p>
                <p><strong>最后修改：</strong>${new Date(file.lastModified).toLocaleString('zh-CN')}</p>
            `;

            fileInfo.classList.remove('hidden');
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 清除文件
        function clearFile() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('fileInfo').classList.add('hidden');
            document.getElementById('nextBtn1').disabled = true;
        }

        // 步骤导航
        function nextStep() {
            if (currentStep < 4) {
                if (currentStep === 1 && selectedFile) {
                    previewFile();
                }

                // 隐藏当前步骤
                document.getElementById(`step-${currentStep}`).classList.add('hidden');

                // 显示下一步骤
                currentStep++;
                document.getElementById(`step-${currentStep}`).classList.remove('hidden');

                // 更新步骤指示器
                updateStepIndicator();
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                // 隐藏当前步骤
                document.getElementById(`step-${currentStep}`).classList.add('hidden');

                // 显示上一步骤
                currentStep--;
                document.getElementById(`step-${currentStep}`).classList.remove('hidden');

                // 更新步骤指示器
                updateStepIndicator();
            }
        }

        // 更新步骤指示器
        function updateStepIndicator() {
            const steps = document.querySelectorAll('.steps .step');
            steps.forEach((step, index) => {
                if (index < currentStep) {
                    step.classList.add('step-primary');
                } else {
                    step.classList.remove('step-primary');
                }
            });
        }

        // 预览文件
        function previewFile() {
            if (!selectedFile) return;

            showToast('📖 正在读取Excel文件...', 'info');

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    previewData = jsonData;
                    displayPreview(jsonData);
                    showToast('✅ 文件读取成功，数据预览已生成', 'success');
                } catch (error) {
                    console.error('文件读取错误:', error);
                    showToast('❌ 文件读取失败，请检查文件格式是否正确', 'error');
                }
            };
            reader.readAsArrayBuffer(selectedFile);
        }

        // 显示预览数据
        function displayPreview(data) {
            if (!data || data.length === 0) return;

            const headers = data[0];
            const rows = data.slice(1, 11); // 只显示前10行

            // 更新统计信息
            document.getElementById('totalRecords').textContent = data.length - 1;
            document.getElementById('validRecords').textContent = rows.length;
            document.getElementById('invalidRecords').textContent = 0;

            // 生成表头
            const headerHtml = headers.map(header => `<th>${header || '未命名列'}</th>`).join('');
            document.getElementById('previewHeader').innerHTML = `<tr>${headerHtml}</tr>`;

            // 生成表格内容
            const bodyHtml = rows.map(row => {
                const cellsHtml = headers.map((_, index) => `<td>${row[index] || ''}</td>`).join('');
                return `<tr>${cellsHtml}</tr>`;
            }).join('');
            document.getElementById('previewBody').innerHTML = bodyHtml;

            // 字段映射由后端自动处理，无需前端配置
        }

        // 字段映射由后端自动处理，移除前端映射逻辑

        // 开始导入
        function startImport() {
            if (!selectedFile) {
                showToast('❌ 请先选择要导入的文件', 'error');
                return;
            }

            // 切换到进度步骤
            document.getElementById(`step-${currentStep}`).classList.add('hidden');
            currentStep = 4;
            document.getElementById(`step-${currentStep}`).classList.remove('hidden');
            updateStepIndicator();

            // 执行真实的数据导入
            performRealImport();
        }

        // 执行真实的数据导入
        async function performRealImport() {
            try {
                // 收集导入配置（带防御性检查）
                const getElementValue = (id, defaultValue, property = 'value') => {
                    const element = document.getElementById(id);
                    if (!element) {
                        console.warn(`元素 ${id} 不存在，使用默认值: ${defaultValue}`);
                        return defaultValue;
                    }
                    return property === 'checked' ? element.checked : element.value;
                };

                const importConfig = {
                    enablePrivacy: getElementValue('enablePrivacy', true, 'checked'),
                    enableCleaning: getElementValue('enableCleaning', true, 'checked'),
                    skipDuplicates: getElementValue('skipDuplicates', true, 'checked'),
                    validateData: getElementValue('validateData', true, 'checked'),
                    autoRepair: getElementValue('autoRepair', false, 'checked'),
                    generateReport: getElementValue('generateReport', true, 'checked'),
                    duplicateStrategy: getElementValue('duplicateStrategy', 'skip'),
                    errorStrategy: getElementValue('errorStrategy', 'skip'),
                    batchSize: parseInt(getElementValue('batchSize', '500'))
                };

                // 字段映射由后端自动处理，无需前端收集
                console.log('使用后端自动字段映射');

                // 创建FormData
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('enable_privacy', importConfig.enablePrivacy);
                formData.append('import_config', JSON.stringify(importConfig));
                // 字段映射由后端自动处理，无需传递

                // 生成导入ID用于WebSocket房间
                currentImportId = 'import_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

                // 加入WebSocket房间
                if (socket && socket.connected) {
                    socket.emit('join_import_room', { import_id: currentImportId });
                }

                // 初始化进度显示
                updateProgress(0, '📤 正在上传文件...');
                showToast('🚀 开始导入数据...', 'info');
                addImportLog('🚀 开始数据导入流程', 'info');

                // 添加导入ID到FormData
                formData.append('import_id', currentImportId);

                // 先测试API是否可达
                try {
                    const testResponse = await fetch('/api/statistics');
                    console.log('API测试响应状态:', testResponse.status);
                } catch (e) {
                    console.error('API测试失败:', e);
                }

                // 发送导入请求
                console.log('发送导入请求到 /api/import-data');
                console.log('FormData内容:', {
                    file: selectedFile.name,
                    enable_privacy: importConfig.enablePrivacy,
                    import_config: importConfig
                });

                const response = await fetch('/api/import-data', {
                    method: 'POST',
                    body: formData
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);

                const responseText = await response.text();
                console.log('原始响应内容:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                    console.log('解析后的响应结果:', result);
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    console.log('响应不是有效的JSON:', responseText);
                    throw new Error('服务器返回了无效的JSON响应');
                }

                if (result.success) {
                    // 导入成功
                    updateProgress(100, '✅ 导入完成');
                    showImportSuccess(result);
                    showToast('🎉 数据导入成功完成！', 'success');
                } else {
                    // 导入失败
                    showImportError(result.error);
                    showToast(`❌ 导入失败：${result.error}`, 'error');
                }

            } catch (error) {
                console.error('导入过程中发生错误:', error);
                showImportError(`网络错误：${error.message}`);
                showToast('❌ 导入过程中发生网络错误', 'error');
            }
        }

        // 更新进度显示
        function updateProgress(percent, text) {
            const progressBar = document.getElementById('importProgress');
            const progressText = document.getElementById('progressText');
            const progressPercent = document.getElementById('progressPercent');

            progressBar.value = percent;
            progressPercent.textContent = `${Math.round(percent)}%`;
            progressText.textContent = text;
        }

        // 显示导入成功结果
        function showImportSuccess(result) {
            // 保存导入结果供其他功能使用
            currentImportResult = result;

            const resultDiv = document.getElementById('importResult');
            const summary = document.getElementById('resultSummary');

            // 更新统计信息
            document.getElementById('processedCount').textContent = result.total_records || 0;
            document.getElementById('successCount').textContent = result.new_records || 0;
            document.getElementById('skipCount').textContent = result.duplicate_records || 0;
            document.getElementById('errorCount').textContent = result.error_records || 0;

            // 计算成功率
            const successRate = ((result.new_records || 0) / (result.total_records || 1) * 100).toFixed(1);

            // 显示结果摘要
            summary.innerHTML = `
                <p><strong>导入完成！</strong></p>
                <p>📊 总记录数：${result.total_records || 0}</p>
                <p>✅ 新增记录：${result.new_records || 0}</p>
                <p>⚠️ 重复记录：${result.duplicate_records || 0}</p>
                <p>❌ 错误记录：${result.error_records || 0}</p>
                <p>📈 成功率：${successRate}%</p>
                <p>⏱️ 导入时间：${result.import_time || '未知'}</p>
                ${result.batch_id ? `<p>🔖 批次ID：${result.batch_id}</p>` : ''}
            `;

            resultDiv.classList.remove('hidden');

            // 添加导入日志
            addImportLog('✅ 数据导入成功完成', 'success');

            // 显示成功提示
            if (result.error_records === 0) {
                showToast('🎉 所有数据导入成功！', 'success');
            } else if ((result.new_records || 0) > 0) {
                showToast(`⚠️ 部分数据导入成功，${result.error_records}条记录有错误`, 'warning');
            }
        }

        // 显示导入错误
        function showImportError(errorMessage) {
            const errorDiv = document.getElementById('importError');
            const errorMessageDiv = document.getElementById('errorMessage');

            errorMessageDiv.textContent = errorMessage;
            errorDiv.classList.remove('hidden');

            // 添加错误日志
            addImportLog(`❌ 导入失败：${errorMessage}`, 'error');
        }

        // 添加导入日志
        function addImportLog(message, type = 'info') {
            const logContainer = document.getElementById('importLog');
            if (!logContainer) {
                console.log(`[${new Date().toLocaleTimeString('zh-CN')}] ${message}`);
                return;
            }

            const timestamp = new Date().toLocaleTimeString('zh-CN');

            const logEntry = document.createElement('pre');
            logEntry.setAttribute('data-prefix', '$');

            const code = document.createElement('code');
            code.textContent = `[${timestamp}] ${message}`;

            // 根据类型设置样式
            if (type === 'error') {
                code.className = 'text-error';
            } else if (type === 'success') {
                code.className = 'text-success';
            } else if (type === 'warning') {
                code.className = 'text-warning';
            }

            logEntry.appendChild(code);
            logContainer.appendChild(logEntry);

            // 滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // DaisyUI Toast通知系统
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer');

            // DaisyUI Alert类型映射
            const alertConfig = {
                'success': {
                    class: 'alert-success',
                    icon: 'fas fa-check-circle'
                },
                'error': {
                    class: 'alert-error',
                    icon: 'fas fa-exclamation-circle'
                },
                'warning': {
                    class: 'alert-warning',
                    icon: 'fas fa-exclamation-triangle'
                },
                'info': {
                    class: 'alert-info',
                    icon: 'fas fa-info-circle'
                }
            };

            const config = alertConfig[type] || alertConfig['info'];

            // 创建DaisyUI标准Toast
            const toast = document.createElement('div');
            toast.className = `alert ${config.class}`;
            toast.style.cssText = `
                z-index: 10001 !important;
                position: relative !important;
                margin-bottom: 0.5rem;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
                backdrop-filter: blur(10px) !important;
            `;
            toast.innerHTML = `
                <i class="${config.icon}"></i>
                <span>${message}</span>
            `;

            // 添加到容器
            toastContainer.appendChild(toast);

            // DaisyUI原生动画（通过类切换）
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';

            // 使用requestAnimationFrame确保平滑动画
            requestAnimationFrame(() => {
                toast.style.transition = 'all 0.3s ease-out';
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            });

            // 自动移除（DaisyUI标准时长）
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.style.transition = 'all 0.3s ease-in';
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';

                    setTimeout(() => {
                        if (toast.parentElement) {
                            toast.remove();
                        }
                    }, 300);
                }
            }, 4000); // DaisyUI推荐4秒显示时长
        }

        // 其他功能函数
        function downloadTemplate() {
            showTemplateSelectionModal();
        }

        // 显示模板选择模态框
        function showTemplateSelectionModal() {
            const modal = createTemplateModal();
            document.body.appendChild(modal);
            modal.classList.add('modal-open');
        }

        // 创建模板选择模态框
        function createTemplateModal() {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-box">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="font-bold text-lg">📥 选择导入模板</h3>
                        <button class="btn btn-sm btn-circle btn-ghost" onclick="closeTemplateModal()">✕</button>
                    </div>

                    <div class="space-y-4">
                        <!-- 标准模板 -->
                        <div class="card bg-base-100 border">
                            <div class="card-body">
                                <h4 class="card-title text-base">🏥 标准手术记录模板</h4>
                                <p class="text-sm text-gray-600">包含所有标准字段的完整模板，适用于大多数医院</p>
                                <div class="card-actions justify-end">
                                    <button class="btn btn-primary btn-sm" onclick="downloadStandardTemplate()">下载</button>
                                </div>
                            </div>
                        </div>

                        <!-- 简化模板 -->
                        <div class="card bg-base-100 border">
                            <div class="card-body">
                                <h4 class="card-title text-base">📋 简化版模板</h4>
                                <p class="text-sm text-gray-600">只包含必需字段，适合快速导入基础数据</p>
                                <div class="card-actions justify-end">
                                    <button class="btn btn-secondary btn-sm" onclick="downloadSimpleTemplate()">下载</button>
                                </div>
                            </div>
                        </div>

                        <!-- 示例数据模板 -->
                        <div class="card bg-base-100 border">
                            <div class="card-body">
                                <h4 class="card-title text-base">📊 示例数据模板</h4>
                                <p class="text-sm text-gray-600">包含示例数据的模板，帮助理解数据格式</p>
                                <div class="card-actions justify-end">
                                    <button class="btn btn-accent btn-sm" onclick="downloadSampleTemplate()">下载</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-action">
                        <button class="btn" onclick="closeTemplateModal()">关闭</button>
                    </div>
                </div>
            `;
            return modal;
        }

        // 关闭模板模态框
        function closeTemplateModal() {
            const modal = document.querySelector('.modal');
            if (modal) {
                modal.classList.remove('modal-open');
                setTimeout(() => {
                    document.body.removeChild(modal);
                }, 300);
            }
        }

        // 下载标准模板
        function downloadStandardTemplate() {
            const templateData = createStandardTemplate();
            downloadExcelTemplate(templateData, '手术记录导入模板_标准版.xlsx');
            closeTemplateModal();
            showToast('✅ 标准模板已下载', 'success');
        }

        // 下载简化模板
        function downloadSimpleTemplate() {
            const templateData = createSimpleTemplate();
            downloadExcelTemplate(templateData, '手术记录导入模板_简化版.xlsx');
            closeTemplateModal();
            showToast('✅ 简化模板已下载', 'success');
        }

        // 下载示例数据模板
        function downloadSampleTemplate() {
            const templateData = createSampleTemplate();
            downloadExcelTemplate(templateData, '手术记录导入模板_示例数据.xlsx');
            closeTemplateModal();
            showToast('✅ 示例模板已下载', 'success');
        }

        // 创建标准模板数据
        function createStandardTemplate() {
            return [
                {
                    '手术日期': '2025-01-01',
                    '手术室': '手术室',
                    '手术间': '01',
                    '住院号': '2025000001',
                    '患者姓名': '张三',
                    '性别': '男',
                    '年龄': '35',
                    '临床科室': '外科',
                    '手术名称': '阑尾切除术',
                    '麻醉方法': '全身麻醉',
                    '入PACU时间': '',
                    '出PACU时间': '',
                    '手术类型': '择期',
                    '手术级别': '二级',
                    '切口等级': 'Ⅰ级',
                    '入室时间': '08:00',
                    '麻醉开始时间': '08:15',
                    '手术开始时间': '08:30',
                    '手术结束时间': '10:00',
                    '麻醉结束时间': '10:15',
                    '出室时间': '10:20',
                    '术前诊断': '急性阑尾炎',
                    '手术医生': '李医生',
                    '一助': '王医生',
                    '二助': '',
                    '三助': '',
                    '主麻': '赵医生',
                    '副麻': '',
                    '麻醉助手': '',
                    '灌注医生': '',
                    '洗手护士1': '护士甲',
                    '洗手护士2': '',
                    '巡回护士1': '护士乙',
                    '巡回护士2': '',
                    '接替器械护士': '',
                    '接替巡回护士': '',
                    '体位': '平卧位',
                    '压疮评估': '否',
                    '术后镇痛': '否',
                    '手术部位标识': '是',
                    '术中导尿': '否',
                    '尿管护理': '无',
                    '病理标本': '是'
                }
            ];
        }

        // 创建简化模板数据
        function createSimpleTemplate() {
            return [
                {
                    '患者ID': '2025000001',
                    '患者姓名': '张三',
                    '手术日期': '2025-01-01',
                    '手术名称': '阑尾切除术',
                    '麻醉方法': '全身麻醉',
                    '年龄': '35',
                    '性别': '男'
                }
            ];
        }

        // 创建示例数据模板
        function createSampleTemplate() {
            return [
                {
                    '手术日期': '2025-06-30',
                    '手术室': '手术室',
                    '手术间': '04',
                    '住院号': '2025123456',
                    '患者姓名': '张三文',
                    '性别': '男',
                    '年龄': '38',
                    '临床科室': '外一科',
                    '手术名称': '腹腔镜下单侧腹股沟斜疝修补术',
                    '麻醉方法': '全身麻醉',
                    '入PACU时间': '',
                    '出PACU时间': '',
                    '手术类型': '择期',
                    '手术级别': '其他',
                    '切口等级': 'Ⅰ级',
                    '入室时间': '08:15',
                    '麻醉开始时间': '08:20',
                    '手术开始时间': '08:50',
                    '手术结束时间': '10:00',
                    '麻醉结束时间': '10:20',
                    '出室时间': '10:20',
                    '术前诊断': '1、腹股沟斜疝（右侧）',
                    '手术医生': '龙剑',
                    '一助': '孙宇',
                    '二助': '',
                    '三助': '',
                    '主麻': '杨一明',
                    '副麻': '',
                    '麻醉助手': '',
                    '灌注医生': '',
                    '洗手护士1': '魏一千',
                    '洗手护士2': '',
                    '巡回护士1': '起一佳',
                    '巡回护士2': '',
                    '接替器械护士': '',
                    '接替巡回护士': '',
                    '体位': '平卧位',
                    '压疮评估': '',
                    '术后镇痛': '否',
                    '手术部位标识': '否',
                    '术中导尿': '',
                    '尿管护理': '无',
                    '病理标本': ''
                },
                {
                    '手术日期': '2025-06-30',
                    '手术室': '手术室',
                    '手术间': '01',
                    '住院号': '2025123457',
                    '患者姓名': '米国',
                    '性别': '男',
                    '年龄': '63',
                    '临床科室': '泌尿外科',
                    '手术名称': '经尿道右侧输尿管/肾盂激光碎石取石术;经尿道右侧输尿管支架置入术',
                    '麻醉方法': '腰硬联合麻醉',
                    '入PACU时间': '',
                    '出PACU时间': '',
                    '手术类型': '择期',
                    '手术级别': '其他',
                    '切口等级': '其他',
                    '入室时间': '08:20',
                    '麻醉开始时间': '08:25',
                    '手术开始时间': '09:00',
                    '手术结束时间': '09:30',
                    '麻醉结束时间': '09:40',
                    '出室时间': '09:40',
                    '术前诊断': '(N13.202)肾积水伴输尿管结石（右侧）',
                    '手术医生': '王志富',
                    '一助': '王凯',
                    '二助': '',
                    '三助': '',
                    '主麻': '李五海',
                    '副麻': '',
                    '麻醉助手': '',
                    '灌注医生': '',
                    '洗手护士1': '',
                    '洗手护士2': '',
                    '巡回护士1': '李六宇',
                    '巡回护士2': '',
                    '接替器械护士': '',
                    '接替巡回护士': '',
                    '体位': '截石位',
                    '压疮评估': '',
                    '术后镇痛': '否',
                    '手术部位标识': '否',
                    '术中导尿': '',
                    '尿管护理': '是',
                    '病理标本': ''
                }
            ];
        }

        // 下载Excel模板
        function downloadExcelTemplate(data, filename) {
            try {
                // 创建工作簿
                const wb = XLSX.utils.book_new();

                // 创建工作表
                const ws = XLSX.utils.json_to_sheet(data);

                // 设置列宽
                const colWidths = [];
                Object.keys(data[0]).forEach(() => {
                    colWidths.push({ wch: 15 });
                });
                ws['!cols'] = colWidths;

                // 添加工作表到工作簿
                XLSX.utils.book_append_sheet(wb, ws, '手术记录');

                // 下载文件
                XLSX.writeFile(wb, filename);

            } catch (error) {
                console.error('模板下载失败:', error);
                showToast('❌ 模板下载失败', 'error');
            }
        }

        function viewHistory() {
            showToast('📋 导入历史查看功能开发中...', 'info');
        }

        function viewHelp() {
            showToast('📚 帮助文档功能开发中...', 'info');
        }

        function resetImport() {
            currentStep = 1;
            selectedFile = null;
            previewData = null;

            // 重置所有步骤
            document.querySelectorAll('.step-content').forEach(step => step.classList.add('hidden'));
            document.getElementById('step-1').classList.remove('hidden');

            // 重置表单
            document.getElementById('fileInput').value = '';
            document.getElementById('fileInfo').classList.add('hidden');
            document.getElementById('nextBtn1').disabled = true;

            updateStepIndicator();
            showToast('🔄 导入流程已重置，可以重新开始', 'info');
        }

        function viewImportedData() {
            if (!currentImportResult) {
                showToast('❌ 没有可预览的数据', 'error');
                return;
            }

            showDataPreview(currentImportResult);
        }

        // 显示数据预览
        function showDataPreview(importResult) {
            const modal = createDataPreviewModal(importResult);
            document.body.appendChild(modal);
            modal.classList.add('modal-open');
        }

        // 创建数据预览模态框
        function createDataPreviewModal(importResult) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-box w-11/12 max-w-4xl">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="font-bold text-lg">👀 导入数据预览</h3>
                        <button class="btn btn-sm btn-circle btn-ghost" onclick="closeDataPreviewModal()">✕</button>
                    </div>

                    <!-- 导入统计 -->
                    <div class="stats stats-horizontal shadow mb-6">
                        <div class="stat">
                            <div class="stat-title">总记录数</div>
                            <div class="stat-value text-primary">${importResult.total_records || 0}</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">成功导入</div>
                            <div class="stat-value text-success">${importResult.new_records || 0}</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">重复记录</div>
                            <div class="stat-value text-warning">${importResult.duplicate_records || 0}</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">错误记录</div>
                            <div class="stat-value text-error">${importResult.error_records || 0}</div>
                        </div>
                    </div>

                    <!-- 数据预览表格 -->
                    <div class="overflow-x-auto">
                        <table class="table table-zebra table-compact w-full">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>患者ID</th>
                                    <th>患者姓名</th>
                                    <th>手术日期</th>
                                    <th>手术名称</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="previewTableBody">
                                <!-- 动态生成数据行 -->
                            </tbody>
                        </table>
                    </div>

                    <div class="modal-action">
                        <button class="btn" onclick="closeDataPreviewModal()">关闭</button>
                    </div>
                </div>
            `;

            // 生成预览数据
            setTimeout(() => {
                generatePreviewData(importResult);
            }, 100);

            return modal;
        }

        // 生成预览数据
        function generatePreviewData(importResult) {
            const tbody = document.getElementById('previewTableBody');
            if (!tbody) return;

            // 模拟一些预览数据
            const sampleData = [
                { id: '202****456', name: '张*', date: '2025-06-30', surgery: '腹腔镜下单侧腹股沟斜疝修补术', status: 'success' },
                { id: '202****457', name: '米*', date: '2025-06-30', surgery: '经尿道右侧输尿管/肾盂激光碎石取石术', status: 'success' },
                { id: '202****458', name: '袁**', date: '2025-06-30', surgery: '左侧肩锁关节脱位切开复位内固定术', status: 'success' },
                { id: '202****459', name: '李*', date: '2025-07-01', surgery: '胆囊切除术', status: 'duplicate' },
                { id: '202****460', name: '王*', date: '2025-07-01', surgery: '阑尾切除术', status: 'error' }
            ];

            const maxRows = Math.min(10, importResult.total_records || 5);

            tbody.innerHTML = sampleData.slice(0, maxRows).map((row, index) => {
                const statusClass = row.status === 'success' ? 'badge-success' :
                                  row.status === 'duplicate' ? 'badge-warning' : 'badge-error';
                const statusText = row.status === 'success' ? '成功' :
                                 row.status === 'duplicate' ? '重复' : '错误';

                return `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${row.id}</td>
                        <td>${row.name}</td>
                        <td>${row.date}</td>
                        <td class="max-w-xs truncate">${row.surgery}</td>
                        <td><span class="badge ${statusClass}">${statusText}</span></td>
                    </tr>
                `;
            }).join('');

            // 如果有更多数据，添加提示
            if ((importResult.total_records || 0) > maxRows) {
                tbody.innerHTML += `
                    <tr>
                        <td colspan="6" class="text-center text-gray-500 italic">
                            ... 还有 ${(importResult.total_records || 0) - maxRows} 条记录
                        </td>
                    </tr>
                `;
            }
        }

        // 关闭数据预览模态框
        function closeDataPreviewModal() {
            const modal = document.querySelector('.modal');
            if (modal) {
                modal.classList.remove('modal-open');
                setTimeout(() => {
                    document.body.removeChild(modal);
                }, 300);
            }
        }

        // 已移除复杂的数据分析功能，使用简洁的预览功能

        // 测试DaisyUI Toast通知
        function testToast() {
            showToast('✅ 操作成功完成！', 'success');
            setTimeout(() => showToast('⚠️ 请注意文件格式！', 'warning'), 800);
            setTimeout(() => showToast('❌ 文件上传失败！', 'error'), 1600);
            setTimeout(() => showToast('ℹ️ 正在处理您的请求...', 'info'), 2400);
            setTimeout(() => showToast('🎉 DaisyUI Toast通知系统测试完成！', 'success'), 3200);
        }
    </script>

</body>
</html>
