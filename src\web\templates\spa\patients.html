<!-- SPA版本的患者管理内容 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">患者管理</h1>
            <p class="text-base-content/60 mt-1">查看和管理患者信息</p>
        </div>
        <div class="flex space-x-2">
            <a href="/patient-add" class="btn btn-primary" onclick="router.navigate('/patient-add'); return false;">
                <i class="fas fa-plus mr-2"></i>
                新增患者
            </a>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- 搜索框 -->
                <div class="form-control">
                    <input type="text" placeholder="搜索患者姓名、ID..." class="input input-bordered" id="search-input">
                </div>
                
                <!-- 年龄筛选 -->
                <div class="form-control">
                    <select class="select select-bordered" id="age-filter">
                        <option value="">所有年龄</option>
                        <option value="0-18">0-18岁</option>
                        <option value="19-40">19-40岁</option>
                        <option value="41-60">41-60岁</option>
                        <option value="61-80">61-80岁</option>
                        <option value="80+">80岁以上</option>
                    </select>
                </div>
                
                <!-- 性别筛选 -->
                <div class="form-control">
                    <select class="select select-bordered" id="gender-filter">
                        <option value="">所有性别</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                </div>
                
                <!-- 搜索按钮 -->
                <div class="form-control">
                    <button class="btn btn-primary" onclick="searchPatients()">
                        <i class="fas fa-search mr-2"></i>
                        搜索
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 患者列表 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <div class="flex items-center justify-between mb-4">
                <h3 class="card-title">患者列表</h3>
                <div class="text-sm text-base-content/60">
                    共 <span id="total-count">--</span> 名患者
                </div>
            </div>
            
            <!-- 表格 -->
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>患者ID</th>
                            <th>姓名</th>
                            <th>性别</th>
                            <th>年龄</th>
                            <th>联系电话</th>
                            <th>最后就诊</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="patients-table-body">
                        <!-- 加载中状态 -->
                        <tr>
                            <td colspan="7" class="text-center py-8">
                                <div class="flex items-center justify-center">
                                    <div class="loading loading-spinner loading-md mr-2"></div>
                                    <span>加载患者数据...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-center mt-6">
                <div class="join">
                    <button class="join-item btn btn-outline" onclick="previousPage()">«</button>
                    <button class="join-item btn btn-outline btn-active">1</button>
                    <button class="join-item btn btn-outline">2</button>
                    <button class="join-item btn btn-outline">3</button>
                    <button class="join-item btn btn-outline" onclick="nextPage()">»</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let totalPages = 1;
let patients = [];

function searchPatients() {
    const searchTerm = document.getElementById('search-input').value;
    const ageFilter = document.getElementById('age-filter').value;
    const genderFilter = document.getElementById('gender-filter').value;
    
    // 模拟搜索
    loadPatientsData();
    
    themeManager.showNotification('搜索完成', 'success');
}

function loadPatientsData() {
    // 模拟患者数据
    const mockPatients = [
        {
            id: 'P001',
            name: '张三',
            gender: '男',
            age: 45,
            phone: '138****1234',
            lastVisit: '2024-01-15'
        },
        {
            id: 'P002',
            name: '李四',
            gender: '女',
            age: 32,
            phone: '139****5678',
            lastVisit: '2024-01-14'
        },
        {
            id: 'P003',
            name: '王五',
            gender: '男',
            age: 58,
            phone: '137****9012',
            lastVisit: '2024-01-13'
        }
    ];
    
    patients = mockPatients;
    
    // 更新表格
    const tbody = document.getElementById('patients-table-body');
    tbody.innerHTML = patients.map(patient => `
        <tr>
            <td>${patient.id}</td>
            <td>${patient.name}</td>
            <td>${patient.gender}</td>
            <td>${patient.age}岁</td>
            <td>${patient.phone}</td>
            <td>${patient.lastVisit}</td>
            <td>
                <div class="flex space-x-2">
                    <button class="btn btn-ghost btn-xs" onclick="viewPatient('${patient.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-ghost btn-xs" onclick="editPatient('${patient.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // 更新总数
    document.getElementById('total-count').textContent = patients.length;
}

function viewPatient(patientId) {
    if (window.router) {
        window.router.navigate(`/patients/${patientId}`);
    } else {
        window.location.href = `/patients/${patientId}`;
    }
}

function editPatient(patientId) {
    if (window.router) {
        window.router.navigate(`/patients/${patientId}/edit`);
    } else {
        window.location.href = `/patients/${patientId}/edit`;
    }
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        loadPatientsData();
    }
}

function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        loadPatientsData();
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('患者管理页面加载完成');
    loadPatientsData();
});
</script>
