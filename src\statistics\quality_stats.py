"""
质量统计模块
"""
from typing import Dict, Any, List, Tuple
from database.models import DatabaseManager
from utils.logger import logger


class QualityStatistics:
    """数据质量统计分析"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_data_completeness_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取数据完整性分析"""
        logger.info("获取数据完整性分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 关键字段完整性检查
            cursor = conn.execute(f'''
                SELECT
                    COUNT(*) as total_records,
                    -- 基础信息完整性
                    COUNT(CASE WHEN patient_id IS NULL THEN 1 END) as missing_patient_id,
                    COUNT(CASE WHEN department IS NULL OR department = '' THEN 1 END) as missing_department,
                    
                    -- 手术信息完整性
                    COUNT(CASE WHEN surgery_type IS NULL OR surgery_type = '' THEN 1 END) as missing_surgery_type,
                    
                    -- 麻醉信息完整性
                    COUNT(CASE WHEN anesthesia_method IS NULL OR anesthesia_method = '' THEN 1 END) as missing_anesthesia_method,
                    COUNT(CASE WHEN anesthesiologist IS NULL OR anesthesiologist = '' THEN 1 END) as missing_anesthesiologist,
                    COUNT(CASE WHEN postoperative_analgesia IS NULL OR postoperative_analgesia = '' THEN 1 END) as missing_analgesia,
                    
                    -- 人员信息完整性
                    COUNT(CASE WHEN surgeon IS NULL OR surgeon = '' THEN 1 END) as missing_surgeon,

                    -- 时间信息完整性
                    COUNT(CASE WHEN surgery_date IS NULL THEN 1 END) as missing_surgery_date,
                    COUNT(CASE WHEN duration_minutes IS NULL THEN 1 END) as missing_duration
                FROM surgery_records {where_clause}
            ''', params)
            
            completeness_data = cursor.fetchone()
            total = completeness_data[0]
            
            if total == 0:
                return {'total_records': 0, 'completeness_rates': {}, 'missing_counts': {}}
            
            # 计算完整性率（基于实际字段）
            fields = [
                ('patient_id', '患者ID', completeness_data[1]),
                ('department', '科室', completeness_data[2]),
                ('surgery_type', '手术类型', completeness_data[3]),
                ('anesthesia_method', '麻醉方法', completeness_data[4]),
                ('anesthesiologist', '麻醉医生', completeness_data[5]),
                ('postoperative_analgesia', '术后镇痛', completeness_data[6]),
                ('surgeon', '手术医生', completeness_data[7]),
                ('surgery_date', '手术日期', completeness_data[8]),
                ('duration_minutes', '手术时长', completeness_data[9])
            ]
            
            completeness_rates = {}
            missing_counts = {}
            
            for field_key, field_name, missing_count in fields:
                completeness_rate = ((total - missing_count) / total) * 100
                completeness_rates[field_name] = round(completeness_rate, 2)
                missing_counts[field_name] = missing_count
            
            # 计算总体完整性评分
            avg_completeness = sum(completeness_rates.values()) / len(completeness_rates)
            
            return {
                'total_records': total,
                'avg_completeness': round(avg_completeness, 2),
                'completeness_rates': completeness_rates,
                'missing_counts': missing_counts
            }
    
    def get_data_consistency_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取数据一致性分析"""
        logger.info("获取数据一致性分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 简化的一致性检查（基于实际存在的字段）
            time_consistency_cursor = conn.execute(f'''
                SELECT
                    COUNT(*) as total_records,
                    -- 检查手术时长是否合理（0-24小时）
                    COUNT(CASE
                        WHEN duration_minutes IS NOT NULL
                             AND (duration_minutes < 0 OR duration_minutes > 1440)
                        THEN 1 END) as invalid_duration,
                    -- 检查手术日期是否合理
                    COUNT(CASE
                        WHEN surgery_date IS NOT NULL
                             AND (surgery_date < '2020-01-01' OR surgery_date > date('now', '+1 year'))
                        THEN 1 END) as invalid_date
                FROM surgery_records {where_clause}
            ''', params)
            
            time_consistency = time_consistency_cursor.fetchone()
            
            # 跳过数据范围一致性检查（因为age字段不存在）
            range_consistency = (0, 0, 0)  # 模拟结果
            
            # 重复数据检查（基于实际字段）
            duplicate_cursor = conn.execute(f'''
                SELECT
                    COUNT(*) as total_records,
                    COUNT(*) - COUNT(DISTINCT patient_id || surgery_date || anesthesia_method) as potential_duplicates
                FROM surgery_records {where_clause}
            ''', params)
            
            duplicate_data = duplicate_cursor.fetchone()
            
            return {
                'time_consistency': {
                    'total_records': time_consistency[0],
                    'invalid_duration': time_consistency[1],
                    'invalid_date': time_consistency[2]
                },
                'range_consistency': {
                    'total_records': range_consistency[0],
                    'invalid_age': range_consistency[1],
                    'invalid_duration': range_consistency[2]
                },
                'duplicate_analysis': {
                    'total_records': duplicate_data[0],
                    'potential_duplicates': duplicate_data[1]
                }
            }
    
    def get_data_quality_score(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取数据质量评分"""
        logger.info("获取数据质量评分")
        
        # 获取完整性分析
        completeness = self.get_data_completeness_analysis(start_date, end_date)
        
        # 获取一致性分析
        consistency = self.get_data_consistency_analysis(start_date, end_date)
        
        if completeness['total_records'] == 0:
            return {
                'overall_score': 0,
                'completeness_score': 0,
                'consistency_score': 0,
                'quality_level': '无数据'
            }
        
        # 计算完整性评分 (0-100)
        completeness_score = completeness['avg_completeness']
        
        # 计算一致性评分 (0-100)
        total_records = consistency['time_consistency']['total_records']
        if total_records > 0:
            time_errors = (
                consistency['time_consistency']['invalid_duration'] +
                consistency['time_consistency']['invalid_date']
            )
            time_consistency_rate = ((total_records - time_errors) / total_records) * 100
        else:
            time_consistency_rate = 100
        
        range_errors = (
            consistency['range_consistency']['invalid_age'] +
            consistency['range_consistency']['invalid_duration']
        )
        range_total = consistency['range_consistency']['total_records']
        if range_total > 0:
            range_consistency_rate = ((range_total - range_errors) / range_total) * 100
        else:
            range_consistency_rate = 100
        
        duplicate_rate = 0
        if consistency['duplicate_analysis']['total_records'] > 0:
            duplicate_rate = (consistency['duplicate_analysis']['potential_duplicates'] / 
                            consistency['duplicate_analysis']['total_records']) * 100
        
        uniqueness_rate = 100 - duplicate_rate
        
        # 一致性评分 = (时间一致性 + 范围一致性 + 唯一性) / 3
        consistency_score = (time_consistency_rate + range_consistency_rate + uniqueness_rate) / 3
        
        # 总体评分 = (完整性评分 * 0.6 + 一致性评分 * 0.4)
        overall_score = (completeness_score * 0.6 + consistency_score * 0.4)
        
        # 质量等级
        if overall_score >= 90:
            quality_level = '优秀'
        elif overall_score >= 80:
            quality_level = '良好'
        elif overall_score >= 70:
            quality_level = '一般'
        elif overall_score >= 60:
            quality_level = '较差'
        else:
            quality_level = '很差'
        
        return {
            'overall_score': round(overall_score, 2),
            'completeness_score': round(completeness_score, 2),
            'consistency_score': round(consistency_score, 2),
            'time_consistency_rate': round(time_consistency_rate, 2),
            'range_consistency_rate': round(range_consistency_rate, 2),
            'uniqueness_rate': round(uniqueness_rate, 2),
            'quality_level': quality_level
        }
    
    def _build_date_filter(self, start_date: str = None, end_date: str = None) -> Tuple[str, List]:
        """构建日期过滤条件"""
        conditions = []
        params = []
        
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
    
    def _build_where_clause(self, start_date: str = None, end_date: str = None, additional_conditions: List[str] = None) -> Tuple[str, List]:
        """构建完整的WHERE子句"""
        conditions = []
        params = []
        
        # 添加日期条件
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        # 添加额外条件
        if additional_conditions:
            conditions.extend(additional_conditions)
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
