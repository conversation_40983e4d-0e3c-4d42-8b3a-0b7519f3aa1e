<!-- SPA版本的主题设置内容 -->
<div class="space-y-6" id="theme-settings-container">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">主题设置</h1>
            <p class="text-base-content/60 mt-1">选择您喜欢的界面主题风格，共29个DaisyUI原生主题</p>
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline btn-sm" id="reset-theme-btn">
                <i class="fas fa-undo mr-2"></i>
                恢复默认
            </button>
        </div>
    </div>

    <!-- 当前主题预览 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">当前主题</h2>
            <div class="flex items-center justify-between p-4 bg-base-200 rounded-lg">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-palette text-primary-content text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg" id="current-theme-name">商务主题</h3>
                        <p class="text-sm text-base-content/60" id="current-theme-desc">专业的商务风格，适合正式场合</p>
                    </div>
                </div>
                <div class="flex space-x-2" id="current-theme-colors">
                    <div class="w-6 h-6 bg-primary rounded-full"></div>
                    <div class="w-6 h-6 bg-secondary rounded-full"></div>
                    <div class="w-6 h-6 bg-accent rounded-full"></div>
                    <div class="w-6 h-6 bg-success rounded-full"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主题分类 -->
    <div class="tabs tabs-boxed" id="theme-tabs">
        <button class="tab tab-active" data-category="all">全部主题 (29)</button>
        <button class="tab" data-category="light">浅色主题 (14)</button>
        <button class="tab" data-category="dark">深色主题 (11)</button>
        <button class="tab" data-category="colorful">彩色主题 (4)</button>
    </div>

    <!-- 主题网格 -->
    <div id="themes-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <!-- 主题卡片将通过JavaScript动态生成 -->
        <div class="col-span-full text-center py-8">
            <div class="loading loading-spinner loading-lg"></div>
            <p class="mt-2 text-base-content/60">正在加载主题...</p>
        </div>
    </div>

    <!-- 主题预览模态框 -->
    <div id="theme-preview-modal" class="modal">
        <div class="modal-box w-11/12 max-w-5xl">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-bold text-lg" id="preview-theme-title">主题预览</h3>
                <button class="btn btn-sm btn-circle btn-ghost" id="close-preview-btn">✕</button>
            </div>

            <div id="preview-content" class="space-y-4">
                <!-- 颜色展示 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-primary rounded-lg mx-auto mb-2"></div>
                        <p class="text-xs">Primary</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-secondary rounded-lg mx-auto mb-2"></div>
                        <p class="text-xs">Secondary</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-accent rounded-lg mx-auto mb-2"></div>
                        <p class="text-xs">Accent</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-neutral rounded-lg mx-auto mb-2"></div>
                        <p class="text-xs">Neutral</p>
                    </div>
                </div>

                <!-- 组件预览 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title">卡片示例</h2>
                            <p>这是一个示例卡片，展示主题的基础色彩。</p>
                            <div class="card-actions justify-end">
                                <button class="btn btn-primary">主要按钮</button>
                                <button class="btn btn-outline">次要按钮</button>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <span>信息提示框</span>
                        </div>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <span>成功提示框</span>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>警告提示框</span>
                        </div>
                        <div class="alert alert-error">
                            <i class="fas fa-times-circle"></i>
                            <span>错误提示框</span>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片预览 -->
                <div class="stats stats-vertical lg:stats-horizontal shadow w-full">
                    <div class="stat">
                        <div class="stat-title">总用户数</div>
                        <div class="stat-value text-primary">25.6K</div>
                        <div class="stat-desc">21% more than last month</div>
                    </div>
                    <div class="stat">
                        <div class="stat-title">页面浏览量</div>
                        <div class="stat-value text-secondary">2.6M</div>
                        <div class="stat-desc">21% more than last month</div>
                    </div>
                    <div class="stat">
                        <div class="stat-title">下载量</div>
                        <div class="stat-value text-accent">86%</div>
                        <div class="stat-desc">14% more than last month</div>
                    </div>
                </div>
            </div>

            <div class="modal-action">
                <button class="btn" id="cancel-preview-btn">取消</button>
                <button class="btn btn-primary" id="apply-preview-btn">应用此主题</button>
            </div>
        </div>
    </div>


</div>

<script>
// 主题设置页面 - 完整重构版本

// 所有DaisyUI主题数据
window.THEMES = window.THEMES || {
    // 浅色主题
    light: { name: '浅色主题', description: '经典的浅色界面，清爽明亮', category: 'light', icon: 'fas fa-sun' },
    cupcake: { name: '纸杯蛋糕', description: '温馨的粉色调，甜美可爱', category: 'light', icon: 'fas fa-heart' },
    bumblebee: { name: '大黄蜂', description: '活力的黄黑配色', category: 'colorful', icon: 'fas fa-bug' },
    emerald: { name: '翡翠主题', description: '清新的绿色调，自然舒适', category: 'light', icon: 'fas fa-gem' },
    corporate: { name: '商务主题', description: '专业的商务风格，适合正式场合', category: 'light', icon: 'fas fa-building' },
    valentine: { name: '情人节', description: '浪漫的粉红色调', category: 'colorful', icon: 'fas fa-heart' },
    garden: { name: '花园主题', description: '自然的绿色花园风格', category: 'light', icon: 'fas fa-seedling' },
    lofi: { name: '低保真', description: '简约的低保真风格', category: 'light', icon: 'fas fa-music' },
    pastel: { name: '粉彩主题', description: '柔和的粉彩色调', category: 'light', icon: 'fas fa-palette' },
    fantasy: { name: '幻想主题', description: '梦幻的紫色调', category: 'light', icon: 'fas fa-magic' },
    wireframe: { name: '线框主题', description: '极简的线框设计', category: 'light', icon: 'fas fa-border-all' },
    cmyk: { name: 'CMYK主题', description: '印刷色彩模式', category: 'light', icon: 'fas fa-print' },
    autumn: { name: '秋季主题', description: '温暖的秋季色调', category: 'light', icon: 'fas fa-leaf' },
    business: { name: '商业主题', description: '现代商业风格', category: 'light', icon: 'fas fa-briefcase' },
    acid: { name: '酸性主题', description: '鲜艳的荧光色调', category: 'colorful', icon: 'fas fa-flask' },
    lemonade: { name: '柠檬水', description: '清新的柠檬黄色调', category: 'light', icon: 'fas fa-lemon' },
    winter: { name: '冬季主题', description: '清冷的冬季色调', category: 'light', icon: 'fas fa-snowflake' },

    // 深色主题
    dark: { name: '深色主题', description: '经典的深色界面，护眼舒适', category: 'dark', icon: 'fas fa-moon' },
    synthwave: { name: '科技主题', description: '霓虹色彩，科技感十足', category: 'dark', icon: 'fas fa-wave-square' },
    retro: { name: '复古主题', description: '怀旧的复古风格', category: 'dark', icon: 'fas fa-tv' },
    cyberpunk: { name: '赛博朋克', description: '未来科幻风格', category: 'dark', icon: 'fas fa-robot' },
    halloween: { name: '万圣节', description: '神秘的万圣节风格', category: 'dark', icon: 'fas fa-ghost' },
    forest: { name: '森林主题', description: '深邃的森林绿色', category: 'dark', icon: 'fas fa-tree' },
    aqua: { name: '海洋主题', description: '清新的海洋蓝色', category: 'colorful', icon: 'fas fa-water' },
    black: { name: '黑色主题', description: '纯黑色的极简风格', category: 'dark', icon: 'fas fa-circle' },
    luxury: { name: '奢华主题', description: '高端奢华的金色调', category: 'dark', icon: 'fas fa-crown' },
    dracula: { name: '德古拉', description: '经典的德古拉配色', category: 'dark', icon: 'fas fa-vampire' },
    night: { name: '夜晚主题', description: '深邃的夜晚色调', category: 'dark', icon: 'fas fa-moon' },
    coffee: { name: '咖啡主题', description: '温暖的咖啡色调', category: 'dark', icon: 'fas fa-coffee' }
};

// 主题设置管理器
class ThemeSettingsManager {
    constructor() {
        this.currentCategory = 'all';
        this.selectedPreviewTheme = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateCurrentThemeDisplay();
        this.renderThemes();

        // 监听主题变更事件
        window.addEventListener('themeChanged', () => {
            this.updateCurrentThemeDisplay();
            this.renderThemes();
        });
    }

    bindEvents() {
        // 分类标签点击
        const tabs = document.getElementById('theme-tabs');
        if (tabs) {
            tabs.addEventListener('click', (e) => {
                if (e.target.classList.contains('tab')) {
                    this.switchCategory(e.target.dataset.category);
                }
            });
        }

        // 恢复默认按钮
        const resetBtn = document.getElementById('reset-theme-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetToDefault());
        }

        // 预览模态框事件
        const closeBtn = document.getElementById('close-preview-btn');
        const cancelBtn = document.getElementById('cancel-preview-btn');
        const applyBtn = document.getElementById('apply-preview-btn');

        if (closeBtn) closeBtn.addEventListener('click', () => this.closePreview());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.closePreview());
        if (applyBtn) applyBtn.addEventListener('click', () => this.applyPreviewTheme());
    }

    updateCurrentThemeDisplay() {
        // 安全检查 themeManager 是否存在且有 getCurrentTheme 方法
        let currentTheme = 'corporate'; // 默认主题

        if (window.themeManager && typeof window.themeManager.getCurrentTheme === 'function') {
            currentTheme = window.themeManager.getCurrentTheme();
        } else {
            // 如果 themeManager 不存在，尝试从 localStorage 获取
            currentTheme = localStorage.getItem('theme') || 'corporate';
        }

        const themeInfo = window.THEMES[currentTheme];

        if (themeInfo) {
            const nameEl = document.getElementById('current-theme-name');
            const descEl = document.getElementById('current-theme-desc');

            if (nameEl) nameEl.textContent = themeInfo.name;
            if (descEl) descEl.textContent = themeInfo.description;
        }
    }

    switchCategory(category) {
        this.currentCategory = category;

        // 更新标签状态
        document.querySelectorAll('#theme-tabs .tab').forEach(tab => {
            tab.classList.remove('tab-active');
            if (tab.dataset.category === category) {
                tab.classList.add('tab-active');
            }
        });

        this.renderThemes();
    }

    renderThemes() {
        const grid = document.getElementById('themes-grid');
        if (!grid) return;

        // 过滤主题
        const filteredThemes = Object.entries(window.THEMES).filter(([key, theme]) => {
            if (this.currentCategory === 'all') return true;
            return theme.category === this.currentCategory;
        });

        // 安全获取当前主题
        let currentTheme = 'corporate';
        if (window.themeManager && typeof window.themeManager.getCurrentTheme === 'function') {
            currentTheme = window.themeManager.getCurrentTheme();
        } else {
            currentTheme = localStorage.getItem('theme') || 'corporate';
        }

        grid.innerHTML = filteredThemes.map(([key, theme]) => `
            <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-200 cursor-pointer theme-card ${currentTheme === key ? 'ring-2 ring-primary' : ''}"
                 data-theme="${key}">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                            <i class="${theme.icon} text-primary-content"></i>
                        </div>
                        <div class="flex space-x-1">
                            <div class="w-3 h-3 bg-primary rounded-full"></div>
                            <div class="w-3 h-3 bg-secondary rounded-full"></div>
                            <div class="w-3 h-3 bg-accent rounded-full"></div>
                        </div>
                    </div>
                    <h3 class="card-title text-base">${theme.name}</h3>
                    <p class="text-sm text-base-content/60">${theme.description}</p>
                    <div class="card-actions justify-between mt-3">
                        <button class="btn btn-ghost btn-xs preview-btn" data-theme="${key}">
                            <i class="fas fa-eye mr-1"></i>预览
                        </button>
                        <button class="btn btn-primary btn-xs apply-btn" data-theme="${key}">
                            <i class="fas fa-check mr-1"></i>应用
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        // 绑定卡片事件
        this.bindThemeCardEvents();
    }

    bindThemeCardEvents() {
        const self = this; // 保存this引用

        // 预览按钮
        document.querySelectorAll('.preview-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                self.previewTheme(btn.dataset.theme);
            });
        });

        // 应用按钮
        document.querySelectorAll('.apply-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                self.applyTheme(btn.dataset.theme);
            });
        });

        // 卡片点击
        document.querySelectorAll('.theme-card').forEach(card => {
            card.addEventListener('click', () => {
                self.applyTheme(card.dataset.theme);
            });
        });
    }

    previewTheme(themeKey) {
        this.selectedPreviewTheme = themeKey;
        const theme = window.THEMES[themeKey];

        if (theme) {
            const modal = document.getElementById('theme-preview-modal');
            const title = document.getElementById('preview-theme-title');
            const content = document.getElementById('preview-content');

            if (title) title.textContent = `${theme.name} - 主题预览`;
            if (content) content.setAttribute('data-theme', themeKey);
            if (modal) modal.classList.add('modal-open');
        }
    }

    closePreview() {
        const modal = document.getElementById('theme-preview-modal');
        if (modal) modal.classList.remove('modal-open');
        this.selectedPreviewTheme = null;
    }

    applyPreviewTheme() {
        if (this.selectedPreviewTheme) {
            this.applyTheme(this.selectedPreviewTheme);
            this.closePreview();
        }
    }

    applyTheme(themeKey) {
        // 检查主题是否存在
        if (!window.THEMES[themeKey]) {
            console.error('主题不存在:', themeKey);
            return;
        }

        // 尝试使用 themeManager
        if (window.themeManager && typeof window.themeManager.setTheme === 'function') {
            window.themeManager.setTheme(themeKey);
        } else {
            // 如果 themeManager 不可用，直接设置主题
            document.documentElement.setAttribute('data-theme', themeKey);
            localStorage.setItem('theme', themeKey);

            // 触发主题变更事件
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme: themeKey }
            }));
        }

        // 显示通知
        if (window.themeManager && typeof window.themeManager.showNotification === 'function') {
            const theme = window.THEMES[themeKey];
            window.themeManager.showNotification(`已切换到${theme.name}`, 'success');
        } else {
            // 简单的通知替代
            console.log(`已切换到${window.THEMES[themeKey].name}`);
        }
    }

    resetToDefault() {
        if (confirm('确定要恢复到默认主题吗？')) {
            this.applyTheme('corporate');
        }
    }
}

// 初始化主题设置管理器
let themeSettingsManager;

function initThemeSettings() {
    // 立即初始化，不等待 ThemeManager
    // 如果 ThemeManager 不可用，使用降级模式
    themeSettingsManager = new ThemeSettingsManager();
}

// 立即初始化
initThemeSettings();
</script>
