<!-- 数据表格组件 -->

<!-- 表格工具栏模板 -->
<template id="datatable-toolbar-template">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
        <!-- 左侧：搜索和筛选 -->
        <div class="flex flex-wrap gap-2">
            <div class="form-control">
                <input type="text" 
                       placeholder="搜索..." 
                       class="input input-bordered input-sm w-full max-w-xs"
                       data-table-search>
            </div>
            <div class="dropdown">
                <div tabindex="0" role="button" class="btn btn-outline btn-sm">
                    <i class="fas fa-filter mr-2"></i>筛选
                </div>
                <div tabindex="0" class="dropdown-content card card-compact w-64 bg-base-100 shadow-xl border border-base-200 z-[1]">
                    <div class="card-body" data-table-filters>
                        <!-- 筛选选项将在这里生成 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧：操作按钮 -->
        <div class="flex flex-wrap gap-2">
            <button class="btn btn-outline btn-sm" data-table-export>
                <i class="fas fa-download mr-2"></i>导出
            </button>
            <button class="btn btn-outline btn-sm" data-table-refresh>
                <i class="fas fa-sync-alt mr-2"></i>刷新
            </button>
            <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-outline btn-sm">
                    <i class="fas fa-cog mr-2"></i>设置
                </div>
                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-lg border border-base-200">
                    <li><a data-table-columns>
                        <i class="fas fa-columns"></i> 显示列
                    </a></li>
                    <li><a data-table-density>
                        <i class="fas fa-compress-alt"></i> 表格密度
                    </a></li>
                    <li><a data-table-reset>
                        <i class="fas fa-undo"></i> 重置设置
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</template>

<!-- 分页组件模板 -->
<template id="datatable-pagination-template">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-4">
        <!-- 左侧：记录信息 -->
        <div class="flex items-center space-x-4">
            <span class="text-sm text-base-content/60" data-pagination-info>
                显示 1-10 条，共 100 条记录
            </span>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-base-content/60">每页显示</span>
                <select class="select select-bordered select-sm" data-pagination-size>
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="text-sm text-base-content/60">条</span>
            </div>
        </div>
        
        <!-- 右侧：分页控件 -->
        <div class="join">
            <button class="join-item btn btn-sm" data-pagination-first>
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="join-item btn btn-sm" data-pagination-prev>
                <i class="fas fa-angle-left"></i>
            </button>
            <span class="join-item btn btn-sm btn-active" data-pagination-current>1</span>
            <button class="join-item btn btn-sm" data-pagination-next>
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="join-item btn btn-sm" data-pagination-last>
                <i class="fas fa-angle-double-right"></i>
            </button>
        </div>
    </div>
</template>

<script>
// 数据表格管理器
class DataTableManager {
    constructor(tableSelector, options = {}) {
        this.table = document.querySelector(tableSelector);
        this.options = {
            searchable: true,
            sortable: true,
            filterable: true,
            exportable: true,
            pagination: true,
            pageSize: 25,
            serverSide: false,
            ...options
        };
        
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.filters = {};
        this.searchTerm = '';
        
        this.init();
    }

    init() {
        if (!this.table) return;
        
        this.setupToolbar();
        this.setupTable();
        this.setupPagination();
        this.bindEvents();
        
        if (this.options.data) {
            this.setData(this.options.data);
        }
    }

    setupToolbar() {
        if (!this.options.toolbar) return;
        
        const template = document.getElementById('datatable-toolbar-template');
        const toolbar = template.content.cloneNode(true);
        
        // 插入工具栏
        this.table.parentNode.insertBefore(toolbar, this.table);
    }

    setupTable() {
        this.table.classList.add('table', 'table-zebra', 'w-full');
        
        // 设置表头排序
        if (this.options.sortable) {
            const headers = this.table.querySelectorAll('thead th[data-sortable]');
            headers.forEach(header => {
                header.classList.add('cursor-pointer', 'select-none');
                header.innerHTML += ' <i class="fas fa-sort ml-1 opacity-50"></i>';
                
                header.addEventListener('click', () => {
                    this.sort(header.dataset.column || header.textContent.trim());
                });
            });
        }
    }

    setupPagination() {
        if (!this.options.pagination) return;
        
        const template = document.getElementById('datatable-pagination-template');
        const pagination = template.content.cloneNode(true);
        
        // 插入分页
        this.table.parentNode.appendChild(pagination);
    }

    bindEvents() {
        // 搜索
        const searchInput = document.querySelector('[data-table-search]');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.search(e.target.value);
            }, 300));
        }

        // 导出
        const exportBtn = document.querySelector('[data-table-export]');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.export());
        }

        // 刷新
        const refreshBtn = document.querySelector('[data-table-refresh]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refresh());
        }

        // 分页大小
        const pageSizeSelect = document.querySelector('[data-pagination-size]');
        if (pageSizeSelect) {
            pageSizeSelect.value = this.options.pageSize;
            pageSizeSelect.addEventListener('change', (e) => {
                this.setPageSize(parseInt(e.target.value));
            });
        }

        // 分页按钮
        this.bindPaginationEvents();
    }

    bindPaginationEvents() {
        const firstBtn = document.querySelector('[data-pagination-first]');
        const prevBtn = document.querySelector('[data-pagination-prev]');
        const nextBtn = document.querySelector('[data-pagination-next]');
        const lastBtn = document.querySelector('[data-pagination-last]');

        if (firstBtn) firstBtn.addEventListener('click', () => this.goToPage(1));
        if (prevBtn) prevBtn.addEventListener('click', () => this.goToPage(this.currentPage - 1));
        if (nextBtn) nextBtn.addEventListener('click', () => this.goToPage(this.currentPage + 1));
        if (lastBtn) lastBtn.addEventListener('click', () => this.goToPage(this.getTotalPages()));
    }

    setData(data) {
        this.data = Array.isArray(data) ? data : [];
        this.applyFilters();
        this.render();
    }

    search(term) {
        this.searchTerm = term.toLowerCase();
        this.currentPage = 1;
        this.applyFilters();
        this.render();
    }

    sort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.applyFilters();
        this.render();
        this.updateSortIcons();
    }

    filter(column, value) {
        if (value) {
            this.filters[column] = value;
        } else {
            delete this.filters[column];
        }

        this.currentPage = 1;
        this.applyFilters();
        this.render();
    }

    applyFilters() {
        let filtered = [...this.data];

        // 应用搜索
        if (this.searchTerm) {
            filtered = filtered.filter(row => {
                return Object.values(row).some(value => 
                    String(value).toLowerCase().includes(this.searchTerm)
                );
            });
        }

        // 应用筛选器
        Object.entries(this.filters).forEach(([column, value]) => {
            filtered = filtered.filter(row => {
                return String(row[column]).toLowerCase().includes(String(value).toLowerCase());
            });
        });

        // 应用排序
        if (this.sortColumn) {
            filtered.sort((a, b) => {
                const aVal = a[this.sortColumn];
                const bVal = b[this.sortColumn];
                
                let comparison = 0;
                if (aVal < bVal) comparison = -1;
                if (aVal > bVal) comparison = 1;
                
                return this.sortDirection === 'desc' ? -comparison : comparison;
            });
        }

        this.filteredData = filtered;
    }

    render() {
        const tbody = this.table.querySelector('tbody');
        if (!tbody) return;

        // 计算分页数据
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        // 清空表格
        tbody.innerHTML = '';

        // 渲染数据
        if (pageData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="100%" class="text-center py-8 text-base-content/60">
                        <i class="fas fa-inbox text-4xl mb-2"></i>
                        <div>暂无数据</div>
                    </td>
                </tr>
            `;
        } else {
            pageData.forEach(row => {
                const tr = this.createRow(row);
                tbody.appendChild(tr);
            });
        }

        this.updatePagination();
    }

    createRow(data) {
        const tr = document.createElement('tr');
        tr.className = 'hover:bg-base-50';

        // 根据表头创建单元格
        const headers = this.table.querySelectorAll('thead th');
        headers.forEach(header => {
            const td = document.createElement('td');
            const column = header.dataset.column || header.textContent.trim();
            
            if (data[column] !== undefined) {
                td.textContent = data[column];
            }
            
            tr.appendChild(td);
        });

        return tr;
    }

    updateSortIcons() {
        const headers = this.table.querySelectorAll('thead th[data-sortable]');
        headers.forEach(header => {
            const icon = header.querySelector('i');
            const column = header.dataset.column || header.textContent.trim();
            
            if (column === this.sortColumn) {
                icon.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'} ml-1`;
            } else {
                icon.className = 'fas fa-sort ml-1 opacity-50';
            }
        });
    }

    updatePagination() {
        const totalPages = this.getTotalPages();
        const startRecord = (this.currentPage - 1) * this.options.pageSize + 1;
        const endRecord = Math.min(this.currentPage * this.options.pageSize, this.filteredData.length);

        // 更新信息
        const infoElement = document.querySelector('[data-pagination-info]');
        if (infoElement) {
            infoElement.textContent = `显示 ${startRecord}-${endRecord} 条，共 ${this.filteredData.length} 条记录`;
        }

        // 更新当前页
        const currentElement = document.querySelector('[data-pagination-current]');
        if (currentElement) {
            currentElement.textContent = this.currentPage;
        }

        // 更新按钮状态
        const firstBtn = document.querySelector('[data-pagination-first]');
        const prevBtn = document.querySelector('[data-pagination-prev]');
        const nextBtn = document.querySelector('[data-pagination-next]');
        const lastBtn = document.querySelector('[data-pagination-last]');

        if (firstBtn) firstBtn.disabled = this.currentPage === 1;
        if (prevBtn) prevBtn.disabled = this.currentPage === 1;
        if (nextBtn) nextBtn.disabled = this.currentPage === totalPages;
        if (lastBtn) lastBtn.disabled = this.currentPage === totalPages;
    }

    goToPage(page) {
        const totalPages = this.getTotalPages();
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.render();
        }
    }

    setPageSize(size) {
        this.options.pageSize = size;
        this.currentPage = 1;
        this.render();
    }

    getTotalPages() {
        return Math.ceil(this.filteredData.length / this.options.pageSize);
    }

    export(format = 'csv') {
        const data = this.filteredData;
        const headers = Array.from(this.table.querySelectorAll('thead th')).map(th => 
            th.textContent.trim()
        );

        if (format === 'csv') {
            this.exportCSV(data, headers);
        } else if (format === 'json') {
            this.exportJSON(data);
        }
    }

    exportCSV(data, headers) {
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `export_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
    }

    exportJSON(data) {
        const jsonContent = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `export_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    refresh() {
        if (this.options.onRefresh) {
            this.options.onRefresh();
        } else {
            this.render();
        }
        
        showSuccess('数据已刷新');
    }

    destroy() {
        // 清理事件监听器和DOM元素
        const toolbar = this.table.parentNode.querySelector('[data-table-search]')?.closest('.flex');
        const pagination = this.table.parentNode.querySelector('[data-pagination-info]')?.closest('.flex');
        
        if (toolbar) toolbar.remove();
        if (pagination) pagination.remove();
    }
}

// 便捷函数
function createDataTable(selector, options = {}) {
    return new DataTableManager(selector, options);
}

// 导出到全局
window.DataTableManager = DataTableManager;
window.createDataTable = createDataTable;
</script>
