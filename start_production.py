#!/usr/bin/env python3
"""
生产级启动脚本 - 使用Waitress WSGI服务器
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def main():
    """启动生产级Web应用"""
    print("🚀 启动麻醉质控数据管理系统 (生产模式)...")
    print("=" * 50)
    
    try:
        # 导入应用
        from web.app import app
        
        # 尝试导入Waitress
        try:
            from waitress import serve
            print("✅ 使用Waitress WSGI服务器")
            
            print("🌐 Web服务器启动中...")
            print("📱 访问地址: http://localhost:5000")
            print("⏹️  按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            # 使用Waitress启动
            serve(
                app,
                host='0.0.0.0',
                port=5000,
                threads=6,  # 线程数
                connection_limit=1000,  # 连接限制
                cleanup_interval=30,  # 清理间隔
                channel_timeout=120,  # 通道超时
                log_socket_errors=True,
                expose_tracebacks=False
            )
            
        except ImportError:
            print("⚠️ Waitress未安装，使用Flask开发服务器")
            print("💡 建议安装: pip install waitress")
            
            print("🌐 Web服务器启动中...")
            print("📱 访问地址: http://localhost:5000")
            print("⏹️  按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            # 降级到Flask开发服务器
            app.run(
                host='0.0.0.0',
                port=5000,
                debug=False,
                threaded=True,
                use_reloader=False,
                use_debugger=False
            )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
