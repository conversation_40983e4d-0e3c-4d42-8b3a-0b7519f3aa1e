"""
时间统计模块
"""
from typing import Dict, Any, List, Tuple
from database.models import DatabaseManager
from utils.logger import logger


class TimeStatistics:
    """时间相关统计分析"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_daily_surgery_trend(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """获取每日手术量趋势"""
        logger.info("获取每日手术量趋势")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    DATE(surgery_date) as date,
                    COUNT(*) as count,
                    COUNT(DISTINCT anesthesiologist) as anesthesiologists,
                    COUNT(DISTINCT surgeon) as surgeons,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration
                FROM surgery_records 
                {where_clause}
                GROUP BY DATE(surgery_date)
                ORDER BY date
            ''', params)
            
            result = []
            for row in cursor.fetchall():
                result.append({
                    'date': row[0],
                    'count': row[1],
                    'anesthesiologists': row[2],
                    'surgeons': row[3],
                    'avg_duration': row[4] or 0
                })
            
            return result
    
    def get_monthly_surgery_trend(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """获取每月手术量趋势"""
        logger.info("获取每月手术量趋势")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    strftime('%Y-%m', surgery_date) as month,
                    COUNT(*) as count,
                    COUNT(DISTINCT anesthesiologist) as anesthesiologists,
                    COUNT(DISTINCT surgeon) as surgeons,
                    COUNT(DISTINCT patient_id) as patients,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration
                FROM surgery_records 
                {where_clause}
                GROUP BY strftime('%Y-%m', surgery_date)
                ORDER BY month
            ''', params)
            
            result = []
            for row in cursor.fetchall():
                result.append({
                    'month': row[0],
                    'count': row[1],
                    'anesthesiologists': row[2],
                    'surgeons': row[3],
                    'patients': row[4],
                    'avg_duration': row[5] or 0,
                    'total_duration': row[6] or 0
                })
            
            return result
    
    def get_weekday_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取星期分布统计"""
        logger.info("获取星期分布统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    CASE strftime('%w', surgery_date)
                        WHEN '0' THEN '周日'
                        WHEN '1' THEN '周一'
                        WHEN '2' THEN '周二'
                        WHEN '3' THEN '周三'
                        WHEN '4' THEN '周四'
                        WHEN '5' THEN '周五'
                        WHEN '6' THEN '周六'
                    END as weekday,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY strftime('%w', surgery_date)
                ORDER BY strftime('%w', surgery_date)
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def get_hourly_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取小时分布统计（基于手术开始时间）"""
        logger.info("获取小时分布统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    CASE 
                        WHEN substr(surgery_start_time, 1, 2) BETWEEN '06' AND '11' THEN '上午(6-12点)'
                        WHEN substr(surgery_start_time, 1, 2) BETWEEN '12' AND '17' THEN '下午(12-18点)'
                        WHEN substr(surgery_start_time, 1, 2) BETWEEN '18' AND '23' THEN '晚上(18-24点)'
                        ELSE '夜间(0-6点)'
                    END as time_period,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                AND surgery_start_time IS NOT NULL AND surgery_start_time != ''
                GROUP BY time_period
                ORDER BY 
                    CASE time_period
                        WHEN '上午(6-12点)' THEN 1
                        WHEN '下午(12-18点)' THEN 2
                        WHEN '晚上(18-24点)' THEN 3
                        ELSE 4
                    END
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def get_surgery_duration_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取手术时长分析"""
        logger.info("获取手术时长分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 基础统计
            cursor = conn.execute(f'''
                SELECT 
                    COUNT(*) as total_count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    MIN(CAST(duration_minutes AS REAL)) as min_duration,
                    MAX(CAST(duration_minutes AS REAL)) as max_duration,
                    ROUND(AVG(CAST(duration_minutes AS REAL)) / 60.0, 2) as avg_hours
                FROM surgery_records 
                {where_clause}
                AND duration_minutes IS NOT NULL AND duration_minutes != ''
                AND CAST(duration_minutes AS REAL) > 0
            ''', params)
            
            basic_stats = cursor.fetchone()
            
            # 时长分布
            distribution_cursor = conn.execute(f'''
                SELECT 
                    CASE 
                        WHEN CAST(duration_minutes AS REAL) <= 30 THEN '≤30分钟'
                        WHEN CAST(duration_minutes AS REAL) <= 60 THEN '30-60分钟'
                        WHEN CAST(duration_minutes AS REAL) <= 120 THEN '1-2小时'
                        WHEN CAST(duration_minutes AS REAL) <= 180 THEN '2-3小时'
                        WHEN CAST(duration_minutes AS REAL) <= 240 THEN '3-4小时'
                        WHEN CAST(duration_minutes AS REAL) <= 300 THEN '4-5小时'
                        ELSE '>5小时'
                    END as duration_range,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM surgery_records {where_clause} AND duration_minutes IS NOT NULL AND duration_minutes != '' AND CAST(duration_minutes AS REAL) > 0), 2) as percentage
                FROM surgery_records 
                {where_clause}
                AND duration_minutes IS NOT NULL AND duration_minutes != ''
                AND CAST(duration_minutes AS REAL) > 0
                GROUP BY duration_range
                ORDER BY 
                    CASE 
                        WHEN duration_range = '≤30分钟' THEN 1
                        WHEN duration_range = '30-60分钟' THEN 2
                        WHEN duration_range = '1-2小时' THEN 3
                        WHEN duration_range = '2-3小时' THEN 4
                        WHEN duration_range = '3-4小时' THEN 5
                        WHEN duration_range = '4-5小时' THEN 6
                        ELSE 7
                    END
            ''', params)
            
            duration_distribution = {}
            for row in distribution_cursor.fetchall():
                duration_distribution[row[0]] = {
                    'count': row[1],
                    'percentage': row[2]
                }
            
            return {
                'total_count': basic_stats[0] or 0,
                'avg_duration': basic_stats[1] or 0,
                'min_duration': basic_stats[2] or 0,
                'max_duration': basic_stats[3] or 0,
                'avg_hours': basic_stats[4] or 0,
                'duration_distribution': duration_distribution
            }
    
    def get_time_efficiency_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取时间效率分析"""
        logger.info("获取时间效率分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 计算各个时间段的平均时长
            cursor = conn.execute(f'''
                SELECT 
                    COUNT(*) as total_records,
                    -- 入室到麻醉开始的时间
                    AVG(
                        CASE 
                            WHEN room_entry_time IS NOT NULL AND room_entry_time != '' 
                                 AND anesthesia_start_time IS NOT NULL AND anesthesia_start_time != ''
                            THEN (strftime('%s', '2000-01-01 ' || anesthesia_start_time) - strftime('%s', '2000-01-01 ' || room_entry_time)) / 60.0
                        END
                    ) as avg_prep_time,
                    -- 麻醉开始到手术开始的时间
                    AVG(
                        CASE 
                            WHEN anesthesia_start_time IS NOT NULL AND anesthesia_start_time != '' 
                                 AND surgery_start_time IS NOT NULL AND surgery_start_time != ''
                            THEN (strftime('%s', '2000-01-01 ' || surgery_start_time) - strftime('%s', '2000-01-01 ' || anesthesia_start_time)) / 60.0
                        END
                    ) as avg_anesthesia_prep_time,
                    -- 手术结束到麻醉结束的时间
                    AVG(
                        CASE 
                            WHEN surgery_end_time IS NOT NULL AND surgery_end_time != '' 
                                 AND anesthesia_end_time IS NOT NULL AND anesthesia_end_time != ''
                            THEN (strftime('%s', '2000-01-01 ' || anesthesia_end_time) - strftime('%s', '2000-01-01 ' || surgery_end_time)) / 60.0
                        END
                    ) as avg_recovery_time,
                    -- 麻醉结束到出室的时间
                    AVG(
                        CASE 
                            WHEN anesthesia_end_time IS NOT NULL AND anesthesia_end_time != '' 
                                 AND room_exit_time IS NOT NULL AND room_exit_time != ''
                            THEN (strftime('%s', '2000-01-01 ' || room_exit_time) - strftime('%s', '2000-01-01 ' || anesthesia_end_time)) / 60.0
                        END
                    ) as avg_exit_time
                FROM surgery_records 
                {where_clause}
            ''', params)
            
            efficiency_stats = cursor.fetchone()
            
            return {
                'total_records': efficiency_stats[0] or 0,
                'avg_prep_time': round(efficiency_stats[1] or 0, 2),
                'avg_anesthesia_prep_time': round(efficiency_stats[2] or 0, 2),
                'avg_recovery_time': round(efficiency_stats[3] or 0, 2),
                'avg_exit_time': round(efficiency_stats[4] or 0, 2)
            }
    
    def _build_date_filter(self, start_date: str = None, end_date: str = None) -> Tuple[str, List]:
        """构建日期过滤条件"""
        conditions = []
        params = []
        
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
    
    def _build_where_clause(self, start_date: str = None, end_date: str = None, additional_conditions: List[str] = None) -> Tuple[str, List]:
        """构建完整的WHERE子句"""
        conditions = []
        params = []
        
        # 添加日期条件
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        # 添加额外条件
        if additional_conditions:
            conditions.extend(additional_conditions)
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
