"""
智能缓存管理器
"""
import time
from typing import List, Dict, Any, Optional
from utils.logger import logger
from utils.cache import cache, cache_invalidate_pattern


class CacheManager:
    """智能缓存管理器"""
    
    def __init__(self):
        self.cache_dependencies = {
            # 手术记录相关的缓存依赖
            'surgery_records': [
                '*surgery*',
                '*dashboard*', 
                '*stats*',
                '*comprehensive*'
            ],
            
            # 患者相关的缓存依赖
            'patients': [
                '*patient*',
                '*dashboard*',
                '*stats*'
            ],
            
            # 统计相关的缓存依赖
            'statistics': [
                '*stats*',
                '*dashboard*',
                '*comprehensive*'
            ]
        }
    
    def invalidate_related_cache(self, data_type: str, operation: str = 'update'):
        """
        根据数据类型失效相关缓存
        
        Args:
            data_type: 数据类型 (surgery_records, patients, statistics)
            operation: 操作类型 (create, update, delete)
        """
        if data_type not in self.cache_dependencies:
            logger.warning(f"未知的数据类型: {data_type}")
            return
        
        patterns = self.cache_dependencies[data_type]
        total_cleared = 0
        
        for pattern in patterns:
            cleared_count = cache_invalidate_pattern(pattern)
            total_cleared += cleared_count
        
        logger.info(f"{data_type} {operation} 操作清理了 {total_cleared} 个缓存条目")
        return total_cleared
    
    def invalidate_surgery_cache(self, operation: str = 'update'):
        """失效手术记录相关缓存"""
        return self.invalidate_related_cache('surgery_records', operation)
    
    def invalidate_patient_cache(self, operation: str = 'update'):
        """失效患者相关缓存"""
        return self.invalidate_related_cache('patients', operation)
    
    def invalidate_statistics_cache(self, operation: str = 'update'):
        """失效统计相关缓存"""
        return self.invalidate_related_cache('statistics', operation)
    
    def invalidate_all_data_cache(self):
        """失效所有数据相关缓存"""
        patterns = [
            '*surgery*',
            '*patient*', 
            '*dashboard*',
            '*stats*',
            '*comprehensive*'
        ]
        
        total_cleared = 0
        for pattern in patterns:
            cleared_count = cache_invalidate_pattern(pattern)
            total_cleared += cleared_count
        
        logger.info(f"清理了所有数据相关缓存，共 {total_cleared} 个条目")
        return total_cleared
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return cache.get_stats()
    
    def cleanup_expired_cache(self) -> int:
        """清理过期缓存"""
        return cache.cleanup_expired()


# 全局缓存管理器实例
cache_manager = CacheManager()


def invalidate_surgery_cache(operation: str = 'update'):
    """便捷函数：失效手术记录相关缓存"""
    return cache_manager.invalidate_surgery_cache(operation)


def invalidate_patient_cache(operation: str = 'update'):
    """便捷函数：失效患者相关缓存"""
    return cache_manager.invalidate_patient_cache(operation)


def invalidate_statistics_cache(operation: str = 'update'):
    """便捷函数：失效统计相关缓存"""
    return cache_manager.invalidate_statistics_cache(operation)


def auto_invalidate_cache(data_type: str):
    """
    装饰器：自动失效缓存
    
    Args:
        data_type: 数据类型
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                # 执行原函数
                result = func(*args, **kwargs)
                
                # 如果操作成功，失效相关缓存
                if result:  # 假设返回True表示成功
                    cache_manager.invalidate_related_cache(data_type, 'update')
                
                return result
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        
        return wrapper
    return decorator


class CacheInvalidationMiddleware:
    """缓存失效中间件"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        app.after_request(self.after_request)
    
    def after_request(self, response):
        """请求后处理"""
        # 检查是否是数据修改操作
        if hasattr(response, 'status_code') and response.status_code == 200:
            # 根据请求路径判断是否需要失效缓存
            request_path = getattr(response, 'request_path', '')
            
            if '/api/surgery-records' in request_path and response.request.method in ['POST', 'PUT', 'DELETE']:
                cache_manager.invalidate_surgery_cache('update')
            elif '/api/patients' in request_path and response.request.method in ['POST', 'PUT', 'DELETE']:
                cache_manager.invalidate_patient_cache('update')
        
        return response


def enable_cache_invalidation(app):
    """启用缓存失效中间件"""
    middleware = CacheInvalidationMiddleware(app)
    logger.info("缓存失效中间件已启用")
    return middleware


# 缓存预热功能
def warm_up_cache():
    """预热关键缓存"""
    logger.info("开始预热缓存...")
    
    try:
        # 这里可以添加预热逻辑
        # 例如：预加载仪表盘数据、统计数据等
        pass
    except Exception as e:
        logger.error(f"缓存预热失败: {e}")


# 缓存健康检查
def cache_health_check() -> Dict[str, Any]:
    """缓存健康检查"""
    stats = cache_manager.get_cache_stats()
    
    health_status = {
        'status': 'healthy',
        'cache_stats': stats,
        'recommendations': []
    }
    
    # 检查缓存使用情况
    if stats.get('memory_usage_mb', 0) > 100:  # 超过100MB
        health_status['recommendations'].append('缓存内存使用过高，建议清理')
    
    if stats.get('expired_entries', 0) > 100:  # 超过100个过期条目
        health_status['recommendations'].append('存在大量过期缓存，建议清理')
    
    return health_status
