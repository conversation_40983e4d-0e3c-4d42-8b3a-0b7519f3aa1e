# 麻醉质控数据管理系统 - 数据库设计文档

## 📋 目录
- [系统概述](#系统概述)
- [数据库架构](#数据库架构)
- [表结构设计](#表结构设计)
- [字段映射规则](#字段映射规则)
- [数据脱敏策略](#数据脱敏策略)
- [索引设计](#索引设计)
- [数据完整性约束](#数据完整性约束)
- [迁移策略](#迁移策略)

## 🎯 系统概述

### 业务目标
- 管理医院麻醉科手术记录数据
- 确保患者隐私数据安全
- 支持数据质量控制和统计分析
- 提供数据导入导出功能

### 技术架构
- **数据库引擎**: SQLite 3.x
- **ORM框架**: 原生SQL + Python sqlite3
- **数据脱敏**: 自研脱敏算法
- **数据验证**: 多层验证机制

## 🏗️ 数据库架构

### 核心设计原则
1. **隐私优先**: 所有敏感数据必须脱敏存储
2. **数据统一**: 不允许同一字段有多种格式
3. **完整性保证**: 关键字段不能为空
4. **可追溯性**: 记录数据来源和处理过程

### 数据流向
```
原始Excel文件 → 数据验证 → 字段映射 → 数据脱敏 → 数据库存储
```

## 📊 表结构设计

### 1. 手术记录表 (surgery_records)

**表名**: `surgery_records`  
**用途**: 存储脱敏后的手术麻醉记录  
**记录数**: 当前65条，预计增长到数万条

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| `id` | INTEGER | - | 是 | AUTO_INCREMENT | 主键，自增ID |
| `patient_id` | TEXT | 50 | 是 | - | **脱敏后的患者ID** (如: 2025****52) |
| `patient_name` | TEXT | 100 | 是 | - | **脱敏后的患者姓名** (如: 张*) |
| `surgery_date` | DATE | - | 是 | - | 手术日期 (YYYY-MM-DD) |
| `surgery_name` | TEXT | 200 | 是 | - | **具体手术名称** (如: 阑尾切除术) |
| `anesthesia_method` | TEXT | 100 | 否 | - | 原始麻醉方法 |
| `primary_anesthesia` | TEXT | 100 | 否 | - | **标准化麻醉方法** (清洗后) |
| `compound_anesthesia` | TEXT | 100 | 否 | - | 复合麻醉方法 |
| `surgery_type` | TEXT | 50 | 否 | - | 手术类型 (择期/急诊/限期) |
| `postoperative_analgesia` | TEXT | 100 | 否 | - | 术后镇痛 |
| `anesthesiologist` | TEXT | 100 | 否 | - | 麻醉医师姓名 |
| `surgeon` | TEXT | 100 | 否 | - | 主刀医师姓名 |
| `department` | TEXT | 100 | 否 | - | 临床科室 |
| `duration_minutes` | INTEGER | - | 否 | - | 手术时长(分钟) |
| `asa_grade` | TEXT | 10 | 否 | - | ASA分级 |
| `complications` | TEXT | 500 | 否 | - | 并发症记录 |
| `notes` | TEXT | 1000 | 否 | - | 备注信息 |
| `data_hash` | TEXT | 64 | 是 | - | 数据哈希值(用于去重) |
| `import_batch_id` | TEXT | 50 | 是 | - | 导入批次ID |
| `created_at` | TIMESTAMP | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| `updated_at` | TIMESTAMP | - | 是 | CURRENT_TIMESTAMP | 更新时间 |

#### 关键约束
- `patient_id` + `surgery_date` + `surgery_name` 组合唯一
- `data_hash` 字段唯一，防止重复导入
- `patient_name` 和 `surgery_name` 不能为空

### 2. 导入批次表 (import_batches)

**表名**: `import_batches`  
**用途**: 记录数据导入的批次信息

| 字段名 | 数据类型 | 长度 | 是否必填 | 说明 |
|--------|----------|------|----------|------|
| `id` | INTEGER | - | 是 | 主键，自增ID |
| `batch_id` | TEXT | 50 | 是 | 批次唯一标识符 |
| `filename` | TEXT | 255 | 是 | 原始文件名 |
| `file_size` | INTEGER | - | 否 | 文件大小(字节) |
| `total_records` | INTEGER | - | 是 | 总记录数 |
| `success_records` | INTEGER | - | 是 | 成功导入记录数 |
| `error_records` | INTEGER | - | 是 | 错误记录数 |
| `duplicate_records` | INTEGER | - | 是 | 重复记录数 |
| `privacy_enabled` | BOOLEAN | - | 是 | 是否启用脱敏 |
| `import_config` | TEXT | - | 否 | 导入配置(JSON) |
| `status` | TEXT | 20 | 是 | 导入状态 |
| `error_message` | TEXT | 1000 | 否 | 错误信息 |
| `created_at` | TIMESTAMP | - | 是 | 创建时间 |
| `completed_at` | TIMESTAMP | - | 否 | 完成时间 |

### 3. 数据验证规则表 (validation_rules)

**表名**: `validation_rules`  
**用途**: 存储数据验证规则配置

| 字段名 | 数据类型 | 长度 | 是否必填 | 说明 |
|--------|----------|------|----------|------|
| `id` | INTEGER | - | 是 | 主键 |
| `field_name` | TEXT | 100 | 是 | 字段名 |
| `rule_type` | TEXT | 50 | 是 | 规则类型 |
| `rule_value` | TEXT | 500 | 是 | 规则值 |
| `error_message` | TEXT | 200 | 是 | 错误提示 |
| `is_active` | BOOLEAN | - | 是 | 是否启用 |
| `created_at` | TIMESTAMP | - | 是 | 创建时间 |

## 🗺️ 字段映射规则

### Excel列名到数据库字段的映射

#### 患者信息
| Excel列名 | 数据库字段 | 数据处理 | 示例 |
|-----------|------------|----------|------|
| 患者ID / 住院号 | `patient_id` | 脱敏处理 | ********** → 2025****56 |
| 患者姓名 / 姓名 | `patient_name` | 脱敏处理 | 张三文 → 张** |

#### 手术信息
| Excel列名 | 数据库字段 | 数据处理 | 示例 |
|-----------|------------|----------|------|
| 手术日期 | `surgery_date` | 日期格式化 | 2025/8/24 → 2025-08-24 |
| 手术名称 | `surgery_name` | 直接存储 | 阑尾切除术 |
| 手术类型 | `surgery_type` | 标准化 | 择期/急诊/限期 |

#### 麻醉信息
| Excel列名 | 数据库字段 | 数据处理 | 示例 |
|-----------|------------|----------|------|
| 麻醉方法 | `anesthesia_method` | 原始值 | 全身麻醉 |
| 麻醉方法 | `primary_anesthesia` | 标准化清洗 | 全身麻醉 |

#### 医护人员
| Excel列名 | 数据库字段 | 数据处理 | 示例 |
|-----------|------------|----------|------|
| 麻醉医师 | `anesthesiologist` | **脱敏处理** | 李医生 → 李** |
| 主刀医师 | `surgeon` | **脱敏处理** | 王医生 → 王** |
| 一助 | `assistant_1` | **脱敏处理** | 张助手 → 张** |
| 洗手护士 | `scrub_nurse_1` | **脱敏处理** | 刘护士 → 刘** |
| 巡回护士 | `circulating_nurse_1` | **脱敏处理** | 陈护士 → 陈** |

#### 科室信息
| Excel列名 | 数据库字段 | 数据处理 | 示例 |
|-----------|------------|----------|------|
| 临床科室 / 科室 | `department` | 标准化 | 外一科 |

## 🔒 数据脱敏策略

### 脱敏字段
1. **患者ID** (`patient_id`)
   - 规则: 保留前4位和后2位，中间用****替换
   - 示例: `**********` → `2025****56`

2. **患者姓名** (`patient_name`)
   - 规则: 保留姓氏，名字用*替换
   - 示例: `张三文` → `张**`

3. **医护人员姓名** (`anesthesiologist`, `surgeon`)
   - 规则: 保留姓氏，名字用*替换
   - 示例: `李医生` → `李**`
   - 适用字段: 麻醉医师、主刀医师、助手、护士等所有医护人员

### 脱敏算法
```python
def mask_hospital_id(original_id: str) -> str:
    """脱敏患者ID"""
    if len(original_id) >= 6:
        return original_id[:4] + "****" + original_id[-2:]
    return original_id

def mask_name(original_name: str) -> str:
    """脱敏姓名（患者和医护人员通用）"""
    if len(original_name) >= 2:
        return original_name[0] + "*" * (len(original_name) - 1)
    return original_name

# 应用示例
patient_name = mask_name("张三文")      # → "张**"
doctor_name = mask_name("李医生")       # → "李**"
nurse_name = mask_name("王护士")        # → "王**"
```

### 安全措施
- **全面脱敏**: 患者信息和医护人员信息全部脱敏
- **原始数据删除**: 原始敏感数据导入后立即删除
- **不可逆性**: 脱敏过程不可逆，无法还原原始信息
- **统一标准**: 所有姓名字段使用相同的脱敏算法
- **审计追踪**: 记录所有脱敏操作的日志

## 📈 索引设计

### 主要索引
```sql
-- 主键索引
CREATE UNIQUE INDEX idx_surgery_records_pk ON surgery_records(id);

-- 唯一性索引
CREATE UNIQUE INDEX idx_surgery_records_hash ON surgery_records(data_hash);

-- 查询优化索引
CREATE INDEX idx_surgery_records_date ON surgery_records(surgery_date);
CREATE INDEX idx_surgery_records_patient ON surgery_records(patient_id);
CREATE INDEX idx_surgery_records_department ON surgery_records(department);
CREATE INDEX idx_surgery_records_batch ON surgery_records(import_batch_id);

-- 复合索引
CREATE INDEX idx_surgery_records_date_dept ON surgery_records(surgery_date, department);
```

### 索引使用场景
- **按日期查询**: 手术日期范围查询
- **按科室筛选**: 科室统计和筛选
- **按患者查询**: 患者历史记录查询
- **重复检测**: 基于data_hash的快速去重

## ✅ 数据完整性约束

### 必填字段约束
```sql
-- 核心字段不能为空
CHECK (patient_id IS NOT NULL AND patient_id != '')
CHECK (patient_name IS NOT NULL AND patient_name != '')
CHECK (surgery_date IS NOT NULL)
CHECK (surgery_name IS NOT NULL AND surgery_name != '')
```

### 数据格式约束
```sql
-- 日期格式约束
CHECK (surgery_date LIKE '____-__-__')

-- 患者ID格式约束
CHECK (patient_id LIKE '____****__')
```

### 业务逻辑约束
- 手术日期不能是未来日期
- 患者ID必须是脱敏格式
- 患者姓名必须是脱敏格式

## 🔄 迁移策略

### 版本1.0 → 1.1 迁移
```sql
-- 添加新字段
ALTER TABLE surgery_records ADD COLUMN patient_name TEXT;
ALTER TABLE surgery_records ADD COLUMN surgery_name TEXT;

-- 数据迁移（如果有历史数据需要处理）
-- 注意：现有数据缺少这些字段，需要重新导入
```

### 数据一致性检查
```sql
-- 检查数据完整性
SELECT COUNT(*) as incomplete_records 
FROM surgery_records 
WHERE patient_name IS NULL 
   OR patient_name = '' 
   OR surgery_name IS NULL 
   OR surgery_name = '';
```

## 📊 数据统计

### 当前数据状态
- **总记录数**: 65条
- **完整记录数**: 0条（需要重新导入）
- **缺失患者姓名**: 65条
- **缺失手术名称**: 65条

### 数据质量要求
- **完整性**: 核心字段完整率 100%
- **准确性**: 脱敏格式正确率 100%
- **一致性**: 字段格式统一率 100%
- **唯一性**: 重复记录率 < 1%

## 🎯 使用建议

### 数据导入
1. 确保Excel文件包含所有必填字段
2. 使用标准的列名格式
3. 启用数据脱敏功能
4. 验证导入结果的完整性

### 数据查询
1. 优先使用索引字段进行查询
2. 避免全表扫描
3. 使用分页查询处理大量数据

### 数据维护
1. 定期检查数据完整性
2. 监控数据库性能
3. 及时清理无效数据

## 🔧 技术实现细节

### 数据库连接配置
```python
# 数据库配置
DATABASE_CONFIG = {
    'path': 'data/anesthesia_qc.db',
    'timeout': 30,
    'check_same_thread': False,
    'isolation_level': None
}
```

### 表创建SQL
```sql
-- 手术记录表
CREATE TABLE IF NOT EXISTS surgery_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id TEXT NOT NULL,
    patient_name TEXT NOT NULL,
    surgery_date DATE NOT NULL,
    surgery_name TEXT NOT NULL,
    anesthesia_method TEXT,
    primary_anesthesia TEXT,
    compound_anesthesia TEXT,
    surgery_type TEXT,
    postoperative_analgesia TEXT,
    anesthesiologist TEXT,
    surgeon TEXT,
    department TEXT,
    duration_minutes INTEGER,
    asa_grade TEXT,
    complications TEXT,
    notes TEXT,
    data_hash TEXT UNIQUE NOT NULL,
    import_batch_id TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 导入批次表
CREATE TABLE IF NOT EXISTS import_batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id TEXT UNIQUE NOT NULL,
    filename TEXT NOT NULL,
    file_size INTEGER,
    total_records INTEGER NOT NULL DEFAULT 0,
    success_records INTEGER NOT NULL DEFAULT 0,
    error_records INTEGER NOT NULL DEFAULT 0,
    duplicate_records INTEGER NOT NULL DEFAULT 0,
    privacy_enabled BOOLEAN NOT NULL DEFAULT 1,
    import_config TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

## 📋 API接口设计

### 查询接口
```python
# 获取麻醉记录列表
GET /api/anesthesia-records
参数:
- page: 页码 (默认: 1)
- page_size: 每页记录数 (默认: 10)
- sort_field: 排序字段 (默认: surgery_date)
- sort_order: 排序方向 (asc/desc, 默认: desc)
- department: 科室筛选
- anesthesia_method: 麻醉方法筛选
- start_date: 开始日期
- end_date: 结束日期
- search: 搜索关键词

返回格式:
{
    "success": true,
    "data": [...],
    "total": 65,
    "page": 1,
    "page_size": 10,
    "total_pages": 7
}
```

### 统计接口
```python
# 获取统计信息
GET /api/anesthesia-statistics

返回格式:
{
    "success": true,
    "data": {
        "total_count": 65,
        "today_count": 0,
        "department_count": 8,
        "anesthesia_method_count": 4
    }
}
```

## 🚨 数据安全规范

### 脱敏范围
**患者敏感信息**:
- ✅ 患者ID: `**********` → `2025****56`
- ✅ 患者姓名: `张三文` → `张*文`

**医护人员敏感信息**:
- ✅ 麻醉医师: `李医生` → `李*生`
- ✅ 主刀医师: `王医生` → `王*生`
- ✅ 助手: `刘助手` → `刘*手`
- ✅ 洗手护士: `陈护士` → `陈*士`
- ✅ 巡回护士: `赵护士` → `赵*士`
- ✅ 其他医护人员: 统一使用姓名脱敏算法

**非敏感信息**:
- ❌ 手术名称: 保持原样
- ❌ 科室名称: 保持原样
- ❌ 手术日期: 保持原样
- ❌ 麻醉方法: 保持原样

### 访问控制
- 数据库文件权限: 600 (仅所有者可读写)
- 应用程序运行用户: 专用用户账户
- 网络访问: 仅本地访问，禁止远程连接

### 数据备份
```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d)
sqlite3 data/anesthesia_qc.db ".backup backup/anesthesia_qc_$DATE.db"
```

### 审计日志
- 记录所有数据导入操作
- 记录数据查询访问
- 记录系统配置变更

## 🔍 故障排查指南

### 常见问题

#### 1. 数据导入失败
**症状**: 导入过程中断或报错
**排查步骤**:
```sql
-- 检查最近的导入批次
SELECT * FROM import_batches
ORDER BY created_at DESC
LIMIT 5;

-- 检查错误记录
SELECT batch_id, error_message
FROM import_batches
WHERE status = 'error';
```

#### 2. 数据显示不完整
**症状**: 麻醉列表显示记录数为0
**排查步骤**:
```sql
-- 检查数据完整性
SELECT
    COUNT(*) as total_records,
    COUNT(patient_name) as has_patient_name,
    COUNT(surgery_name) as has_surgery_name
FROM surgery_records;

-- 查找不完整的记录
SELECT id, patient_id, patient_name, surgery_name
FROM surgery_records
WHERE patient_name IS NULL
   OR patient_name = ''
   OR surgery_name IS NULL
   OR surgery_name = '';
```

#### 3. 性能问题
**症状**: 查询响应缓慢
**排查步骤**:
```sql
-- 检查索引使用情况
EXPLAIN QUERY PLAN
SELECT * FROM surgery_records
WHERE surgery_date BETWEEN '2025-01-01' AND '2025-12-31';

-- 检查表统计信息
SELECT name, sql FROM sqlite_master
WHERE type='index' AND tbl_name='surgery_records';
```

## 📈 性能优化建议

### 查询优化
1. **使用索引**: 确保查询条件使用已建立的索引
2. **分页查询**: 避免一次性加载大量数据
3. **字段选择**: 只查询需要的字段，避免SELECT *

### 存储优化
1. **定期清理**: 删除过期的导入批次记录
2. **数据压缩**: 定期执行VACUUM命令
3. **分区策略**: 考虑按年份分表存储

### 内存优化
```python
# 连接池配置
CONNECTION_POOL = {
    'max_connections': 10,
    'timeout': 30,
    'recycle': 3600
}
```

## 🔮 未来扩展计划

### 版本2.0规划
- 支持多医院数据隔离
- 增加数据分析报表功能
- 实现实时数据同步
- 添加数据导出功能

### 表结构扩展
```sql
-- 医院信息表
CREATE TABLE hospitals (
    id INTEGER PRIMARY KEY,
    hospital_code TEXT UNIQUE,
    hospital_name TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户权限表
CREATE TABLE user_permissions (
    id INTEGER PRIMARY KEY,
    user_id TEXT,
    hospital_id INTEGER,
    permission_level TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

**文档版本**: 1.1
**最后更新**: 2025-08-24
**维护人员**: 系统管理员
**审核状态**: 已审核
**下次更新**: 2025-09-24
