<!-- SPA版本的统计分析内容 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">统计分析</h1>
            <p class="text-base-content/60 mt-1">数据统计和质控分析</p>
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline btn-sm" onclick="exportReport()">
                <i class="fas fa-download mr-2"></i>
                导出报告
            </button>
        </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="flex flex-wrap gap-2">
        <button class="btn btn-sm btn-primary" onclick="setTimeRange('week')">最近一周</button>
        <button class="btn btn-sm btn-outline" onclick="setTimeRange('month')">最近一月</button>
        <button class="btn btn-sm btn-outline" onclick="setTimeRange('quarter')">最近三月</button>
        <button class="btn btn-sm btn-outline" onclick="setTimeRange('year')">最近一年</button>
        <button class="btn btn-sm btn-outline" onclick="toggleCustomRange()">自定义</button>
    </div>

    <!-- 关键指标概览 -->
    <div class="stats stats-vertical lg:stats-horizontal shadow-lg">
        <div class="stat bg-primary text-primary-content">
            <div class="stat-figure">
                <i class="fas fa-procedures text-4xl opacity-80"></i>
            </div>
            <div class="stat-title opacity-80">总手术量</div>
            <div class="stat-value" id="total-surgeries">--</div>
            <div class="stat-desc opacity-80">
                <span class="text-success">+8.2%</span> 较上期
            </div>
        </div>

        <div class="stat bg-secondary text-secondary-content">
            <div class="stat-figure">
                <i class="fas fa-clock text-4xl opacity-80"></i>
            </div>
            <div class="stat-title opacity-80">平均手术时长</div>
            <div class="stat-value" id="avg-duration">--</div>
            <div class="stat-desc opacity-80">小时</div>
        </div>

        <div class="stat bg-accent text-accent-content">
            <div class="stat-figure">
                <i class="fas fa-exclamation-triangle text-4xl opacity-80"></i>
            </div>
            <div class="stat-title opacity-80">并发症率</div>
            <div class="stat-value" id="complication-rate">2.1</div>
            <div class="stat-desc opacity-80">%</div>
        </div>

        <div class="stat bg-success text-success-content">
            <div class="stat-figure">
                <i class="fas fa-star text-4xl opacity-80"></i>
            </div>
            <div class="stat-title opacity-80">质控评分</div>
            <div class="stat-value" id="quality-score">94.5</div>
            <div class="stat-desc opacity-80">分</div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 手术量趋势 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h3 class="card-title">手术量趋势</h3>
                <p class="text-sm text-base-content/60 mb-4">每日手术数量变化趋势</p>
                <div class="h-64 flex items-center justify-center chart-bg rounded-lg">
                    <div class="text-center chart-placeholder">
                        <i class="fas fa-chart-line text-4xl mb-4"></i>
                        <p>图表加载中...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 麻醉方式分布 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h3 class="card-title">麻醉方式分布</h3>
                <p class="text-sm text-base-content/60 mb-4">各种麻醉方式使用比例</p>
                <div class="h-64 flex items-center justify-center chart-bg rounded-lg">
                    <div class="text-center chart-placeholder">
                        <i class="fas fa-chart-pie text-4xl mb-4"></i>
                        <p>图表加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 更多图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 年龄分布 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h3 class="card-title">患者年龄分布</h3>
                <div class="h-48 flex items-center justify-center chart-bg rounded-lg">
                    <div class="text-center chart-placeholder">
                        <i class="fas fa-chart-bar text-3xl mb-2"></i>
                        <p class="text-sm">图表加载中...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 手术时长分布 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h3 class="card-title">手术时长分布</h3>
                <div class="h-48 flex items-center justify-center chart-bg rounded-lg">
                    <div class="text-center chart-placeholder">
                        <i class="fas fa-chart-bar text-3xl mb-2"></i>
                        <p class="text-sm">图表加载中...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 风险等级分布 -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h3 class="card-title">风险等级分布</h3>
                <div class="h-48 flex items-center justify-center chart-bg rounded-lg">
                    <div class="text-center chart-placeholder">
                        <i class="fas fa-chart-doughnut text-3xl mb-2"></i>
                        <p class="text-sm">图表加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentTimeRange = 'week';

function setTimeRange(range) {
    currentTimeRange = range;
    
    // 更新按钮状态
    document.querySelectorAll('.btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline');
    });
    
    event.target.classList.remove('btn-outline');
    event.target.classList.add('btn-primary');
    
    // 重新加载数据
    loadStatisticsData();
    
    themeManager.showNotification(`已切换到${getTimeRangeName(range)}`, 'info');
}

function getTimeRangeName(range) {
    const names = {
        'week': '最近一周',
        'month': '最近一月',
        'quarter': '最近三月',
        'year': '最近一年'
    };
    return names[range] || range;
}

function toggleCustomRange() {
    window.utils.showNotification('自定义时间范围功能开发中...', 'info');
}

function exportReport() {
    window.utils.showNotification('报告导出功能开发中...', 'info');
}

function loadStatisticsData() {
    // 模拟数据加载
    setTimeout(() => {
        document.getElementById('total-surgeries').textContent = '456';
        document.getElementById('avg-duration').textContent = '2.3';
        
        themeManager.showNotification('统计数据已更新', 'success');
    }, 500);
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('统计分析页面加载完成');
    loadStatisticsData();
});
</script>
