"""
API路由模块
"""
from flask import Blueprint, request, jsonify
import os
import json
import uuid
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any
from werkzeug.utils import secure_filename

# 导入服务
from services.data_service import DataImportService
from statistics.statistics_service import StatisticsService
from database.models import DatabaseManager
from config.settings import UPLOAD_DIR, ALLOWED_EXTENSIONS
from utils.logger import logger
from utils.cache import cached
from utils.error_handler import handle_web_error
from utils.performance import convert_to_json_serializable
from web.websocket_service import get_websocket_service

# 创建蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')

# 初始化服务
data_service = DataImportService()
stats_service = StatisticsService()

# 设置WebSocket进度回调
def websocket_progress_callback(import_id: str, data: Dict[str, Any]):
    """WebSocket进度回调函数"""
    try:
        ws_service = get_websocket_service()
        if data.get('type') == 'log':
            ws_service.send_import_log(import_id, data['data'])
        elif data.get('type') == 'progress':
            ws_service.send_import_progress(import_id, data['data'])
        elif data.get('type') == 'complete':
            ws_service.send_import_complete(import_id, data['data'])
        elif data.get('type') == 'error':
            ws_service.send_import_error(import_id, data['data'])
    except Exception as e:
        logger.error(f"WebSocket回调失败: {str(e)}")

data_service.set_progress_callback(websocket_progress_callback)

# 延迟初始化数据库管理器
db_manager = None

def get_db_manager():
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager


def allowed_file(filename):
    """检查文件类型是否允许"""
    if '.' not in filename:
        return False
    ext = '.' + filename.rsplit('.', 1)[1].lower()
    return ext in ALLOWED_EXTENSIONS


def convert_to_json_serializable(obj):
    """转换为JSON可序列化的对象"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    else:
        return obj


@api_bp.route('/dashboard-data')
@handle_web_error
@cached(ttl=300, key_prefix="dashboard_")  # 缓存5分钟
def get_dashboard_data():
    """获取仪表盘数据 - 优化版本"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # 如果没有指定日期，默认获取最近30天的数据
    if not start_date or not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

    try:
        # 使用简化的查询获取关键数据
        with db_manager.get_connection() as conn:
            # 基础统计 - 单次查询获取多个指标
            cursor = conn.execute('''
                SELECT
                    COUNT(*) as total_surgeries,
                    COUNT(DISTINCT patient_id) as total_patients,
                    AVG(duration_minutes) as avg_duration
                FROM surgery_records
                WHERE surgery_date BETWEEN ? AND ?
            ''', (start_date, end_date))

            basic_stats = cursor.fetchone()

            # 麻醉方法分布 - 限制结果数量
            cursor = conn.execute('''
                SELECT anesthesia_method, COUNT(*) as count
                FROM surgery_records
                WHERE surgery_date BETWEEN ? AND ?
                  AND anesthesia_method IS NOT NULL
                  AND anesthesia_method != ''
                GROUP BY anesthesia_method
                ORDER BY count DESC
                LIMIT 5
            ''', (start_date, end_date))

            anesthesia_methods = {row[0]: row[1] for row in cursor.fetchall()}

            # 手术类型分布 - 限制结果数量
            cursor = conn.execute('''
                SELECT surgery_type, COUNT(*) as count
                FROM surgery_records
                WHERE surgery_date BETWEEN ? AND ?
                  AND surgery_type IS NOT NULL
                  AND surgery_type != ''
                GROUP BY surgery_type
                ORDER BY count DESC
                LIMIT 5
            ''', (start_date, end_date))

            surgery_types = {row[0]: row[1] for row in cursor.fetchall()}

            # 科室分布 - 限制结果数量
            cursor = conn.execute('''
                SELECT department, COUNT(*) as count
                FROM surgery_records
                WHERE surgery_date BETWEEN ? AND ?
                  AND department IS NOT NULL
                  AND department != ''
                GROUP BY department
                ORDER BY count DESC
                LIMIT 5
            ''', (start_date, end_date))

            department_distribution = {row[0]: row[1] for row in cursor.fetchall()}

        # 构建响应数据
        dashboard_data = {
            'overview': {
                'total_surgeries': basic_stats[0] if basic_stats else 0,
                'total_patients': basic_stats[1] if basic_stats else 0,
                'avg_duration': round(basic_stats[2], 1) if basic_stats and basic_stats[2] else 0
            },
            'anesthesia_methods': anesthesia_methods,
            'surgery_types': surgery_types,
            'department_distribution': department_distribution,
            'age_distribution': {},  # 简化版本暂时为空
            'gender_distribution': {},  # 简化版本暂时为空
            'daily_trend': [],  # 简化版本暂时为空
            'quality_score': {'overall_score': 85.0}  # 默认值
        }

        return jsonify({
            'success': True,
            'data': convert_to_json_serializable(dashboard_data)
        })

    except Exception as e:
        logger.error(f"获取仪表盘数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@api_bp.route('/import-data', methods=['POST'])
@handle_web_error
def import_data():
    """导入数据API - 增强版本"""
    try:
        # 1. 验证文件上传
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有选择文件',
                'error_code': 'NO_FILE'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '文件名为空',
                'error_code': 'EMPTY_FILENAME'
            }), 400

        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': f'不支持的文件格式。支持的格式: {", ".join(ALLOWED_EXTENSIONS)}',
                'error_code': 'INVALID_FORMAT'
            }), 400

        # 2. 验证文件大小
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头

        max_size = 50 * 1024 * 1024  # 50MB
        if file_size > max_size:
            return jsonify({
                'success': False,
                'error': f'文件大小超过限制（最大 {max_size // (1024*1024)}MB）',
                'error_code': 'FILE_TOO_LARGE'
            }), 400

        # 3. 获取导入配置
        privacy_enabled = request.form.get('enable_privacy', 'false').lower() == 'true'

        # 获取字段映射（如果提供）
        field_mappings_str = request.form.get('field_mappings', '{}')
        try:
            field_mappings = json.loads(field_mappings_str) if field_mappings_str else {}
        except json.JSONDecodeError:
            return jsonify({
                'success': False,
                'error': '字段映射格式错误',
                'error_code': 'INVALID_MAPPING'
            }), 400

        # 获取导入配置（如果提供）
        import_config_str = request.form.get('import_config', '{}')
        try:
            import_config = json.loads(import_config_str) if import_config_str else {}
        except json.JSONDecodeError:
            return jsonify({
                'success': False,
                'error': '导入配置格式错误',
                'error_code': 'INVALID_CONFIG'
            }), 400

        # 4. 保存上传的文件
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_filename = f"{timestamp}_{filename}"
        file_path = UPLOAD_DIR / unique_filename

        # 确保上传目录存在
        UPLOAD_DIR.mkdir(exist_ok=True)

        file.save(file_path)
        logger.info(f"文件上传成功: {file_path} (大小: {file_size} bytes)")

        # 5. 验证文件内容
        try:
            # 简单验证Excel文件是否可读
            import pandas as pd
            test_df = pd.read_excel(file_path, nrows=1)
            if test_df.empty:
                raise ValueError("文件中没有数据")
        except Exception as e:
            os.remove(file_path)
            return jsonify({
                'success': False,
                'error': f'文件格式错误或损坏: {str(e)}',
                'error_code': 'CORRUPTED_FILE'
            }), 400

        # 6. 执行导入
        try:
            result = data_service.import_excel_file(
                str(file_path),
                privacy_enabled=privacy_enabled,
                field_mappings=field_mappings,
                import_config=import_config
            )

            # 添加额外信息
            result['file_info'] = {
                'original_name': file.filename,
                'size': file_size,
                'upload_time': datetime.now().isoformat()
            }

            logger.info(f"数据导入完成: {result}")

        except ValueError as e:
            # 数据验证错误
            return jsonify({
                'success': False,
                'error': str(e),
                'error_code': 'VALIDATION_ERROR'
            }), 400

        except Exception as e:
            # 其他导入错误
            logger.error(f"导入过程中发生错误: {str(e)}")
            return jsonify({
                'success': False,
                'error': f'导入过程中发生错误: {str(e)}',
                'error_code': 'IMPORT_ERROR'
            }), 500

        finally:
            # 7. 清理上传的文件
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info(f"临时文件已清理: {file_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")

        return jsonify(convert_to_json_serializable(result))

    except Exception as e:
        logger.error(f"导入API发生未预期错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误，请稍后重试',
            'error_code': 'INTERNAL_ERROR'
        }), 500


@api_bp.route('/preview-data', methods=['POST'])
@handle_web_error
def preview_data():
    """预览数据API"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    if not allowed_file(file.filename):
        return jsonify({'success': False, 'error': '不支持的文件格式'})
    
    # 保存上传的文件
    filename = secure_filename(file.filename)
    file_path = UPLOAD_DIR / filename
    file.save(file_path)
    
    try:
        # 快速预览 - 只读取前几行数据
        max_rows = int(request.form.get('max_rows', 10))

        # 先读取少量数据获取基本信息
        df_sample = pd.read_excel(file_path, nrows=max_rows)

        # 获取完整行数（快速方法）
        try:
            # 尝试快速获取总行数
            df_full = pd.read_excel(file_path, usecols=[0])  # 只读第一列来计算行数
            total_rows = len(df_full)
        except:
            # 如果失败，使用样本数据
            total_rows = len(df_sample)

        # 获取基本信息
        file_info = {
            'total_rows': total_rows,
            'total_columns': len(df_sample.columns),
            'columns': df_sample.columns.tolist(),
            'file_size': os.path.getsize(file_path),
            'sheet_name': 'Sheet1'  # 默认工作表名
        }

        # 获取预览数据
        preview_data = []
        for index, row in df_sample.iterrows():
            row_data = {}
            for col in df_sample.columns:
                value = row[col]
                if pd.isna(value):
                    row_data[col] = None
                else:
                    row_data[col] = str(value)
            preview_data.append(row_data)
        
        # 检查敏感列
        sensitive_columns = []
        potential_sensitive = ['患者姓名', '患者ID', '住院号', '病案号', '手机号', '联系电话', '身份证号']
        for col in potential_sensitive:
            if col in df_sample.columns:
                sensitive_columns.append(col)
        
        # 清理上传的文件
        os.remove(file_path)
        
        result = {
            'success': True,
            'file_info': file_info,
            'preview_data': preview_data,
            'sensitive_columns': sensitive_columns,
            'preview_rows': len(preview_data)
        }
        
        return jsonify(convert_to_json_serializable(result))
        
    except Exception as e:
        # 确保清理上传的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        raise e


@api_bp.route('/import-history')
@handle_web_error
def get_import_history():
    """获取导入历史 - 增强版本"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        status = request.args.get('status', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        search = request.args.get('search', '')

        # 构建筛选条件
        filters = {}
        if status:
            filters['status'] = status
        if start_date:
            filters['start_date'] = start_date
        if end_date:
            filters['end_date'] = end_date
        if search:
            filters['search'] = search

        # 获取数据库管理器
        db = get_db_manager()

        # 获取导入历史
        history_result = db.get_import_history(
            page=page,
            limit=limit,
            filters=filters
        )

        # 获取统计信息
        statistics = db.get_import_statistics(filters)

        return jsonify({
            'success': True,
            'data': {
                'records': convert_to_json_serializable(history_result['records']),
                'total_count': history_result['total_count'],
                'total_pages': history_result['total_pages'],
                'current_page': page,
                'statistics': convert_to_json_serializable(statistics)
            }
        })

    except Exception as e:
        logger.error(f"获取导入历史失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取导入历史失败: {str(e)}'
        }), 500


@api_bp.route('/import-history/<import_id>')
@handle_web_error
def get_import_detail(import_id):
    """获取导入详情"""
    try:
        db = get_db_manager()

        # 获取导入记录详情
        import_detail = db.get_import_detail(import_id)

        if not import_detail:
            return jsonify({
                'success': False,
                'error': '导入记录不存在'
            }), 404

        # 获取导入日志
        import_logs = db.get_import_logs(import_id)

        return jsonify({
            'success': True,
            'data': {
                'detail': convert_to_json_serializable(import_detail),
                'logs': convert_to_json_serializable(import_logs)
            }
        })

    except Exception as e:
        logger.error(f"获取导入详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取导入详情失败: {str(e)}'
        }), 500


@api_bp.route('/import-history/<import_id>/report')
@handle_web_error
def download_import_report(import_id):
    """下载导入报告"""
    try:
        db = get_db_manager()

        # 获取导入详情
        import_detail = db.get_import_detail(import_id)
        if not import_detail:
            return jsonify({
                'success': False,
                'error': '导入记录不存在'
            }), 404

        # 生成报告内容
        report_data = {
            'import_id': import_id,
            'filename': import_detail.get('filename', ''),
            'import_time': import_detail.get('import_time', ''),
            'status': import_detail.get('status', ''),
            'total_records': import_detail.get('total_records', 0),
            'new_records': import_detail.get('new_records', 0),
            'duplicate_records': import_detail.get('duplicate_records', 0),
            'error_records': import_detail.get('error_records', 0),
            'logs': db.get_import_logs(import_id)
        }

        # 这里可以生成Excel或PDF报告
        # 暂时返回JSON格式
        return jsonify({
            'success': True,
            'data': convert_to_json_serializable(report_data)
        })

    except Exception as e:
        logger.error(f"生成导入报告失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'生成导入报告失败: {str(e)}'
        }), 500


@api_bp.route('/download-template')
@handle_web_error
def download_template():
    """下载导入模板"""
    try:
        template_type = request.args.get('type', 'surgery_records')

        # 创建模板数据
        if template_type == 'surgery_records':
            template_data = create_surgery_records_template()
        elif template_type == 'patient_info':
            template_data = create_patient_info_template()
        else:
            return jsonify({
                'success': False,
                'error': '不支持的模板类型'
            }), 400

        # 生成Excel文件
        import io
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        wb = Workbook()
        ws = wb.active
        ws.title = template_data['sheet_name']

        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        # 写入表头
        for col, header in enumerate(template_data['headers'], 1):
            cell = ws.cell(row=1, column=col, value=header['name'])
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

            # 设置列宽
            ws.column_dimensions[cell.column_letter].width = header.get('width', 15)

        # 写入示例数据
        for row_idx, example_row in enumerate(template_data['examples'], 2):
            for col_idx, value in enumerate(example_row, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 添加数据验证和说明
        if 'validations' in template_data:
            # 这里可以添加数据验证规则
            pass

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 返回文件
        from flask import send_file
        return send_file(
            output,
            as_attachment=True,
            download_name=template_data['filename'],
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f"生成模板失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'生成模板失败: {str(e)}'
        }), 500


def create_surgery_records_template():
    """创建手术记录模板"""
    return {
        'sheet_name': '手术记录模板',
        'filename': '手术记录导入模板.xlsx',
        'headers': [
            {'name': '患者ID', 'width': 15, 'required': True},
            {'name': '患者姓名', 'width': 12, 'required': True},
            {'name': '手术日期', 'width': 12, 'required': True},
            {'name': '手术类型', 'width': 20, 'required': False},
            {'name': '麻醉方式', 'width': 15, 'required': False},
            {'name': '麻醉医师', 'width': 12, 'required': False},
            {'name': '主刀医师', 'width': 12, 'required': False},
            {'name': '年龄', 'width': 8, 'required': False},
            {'name': '性别', 'width': 8, 'required': False},
            {'name': '手术时长(分钟)', 'width': 15, 'required': False},
            {'name': '麻醉开始时间', 'width': 15, 'required': False},
            {'name': '麻醉结束时间', 'width': 15, 'required': False},
            {'name': '备注', 'width': 25, 'required': False}
        ],
        'examples': [
            ['P001', '张三', '2024-01-15', '胆囊切除术', '全身麻醉', '李医生', '王医生', '45', '男', '120', '09:00', '11:00', '手术顺利'],
            ['P002', '李四', '2024-01-16', '阑尾切除术', '局部麻醉', '赵医生', '陈医生', '32', '女', '60', '14:00', '15:00', ''],
            ['P003', '王五', '2024-01-17', '骨折内固定术', '全身麻醉', '孙医生', '刘医生', '28', '男', '90', '10:30', '12:00', '术后恢复良好']
        ]
    }


def create_patient_info_template():
    """创建患者信息模板"""
    return {
        'sheet_name': '患者信息模板',
        'filename': '患者信息导入模板.xlsx',
        'headers': [
            {'name': '患者ID', 'width': 15, 'required': True},
            {'name': '患者姓名', 'width': 12, 'required': True},
            {'name': '性别', 'width': 8, 'required': True},
            {'name': '年龄', 'width': 8, 'required': True},
            {'name': '出生日期', 'width': 12, 'required': False},
            {'name': '联系电话', 'width': 15, 'required': False},
            {'name': '身份证号', 'width': 20, 'required': False},
            {'name': '住址', 'width': 30, 'required': False},
            {'name': '紧急联系人', 'width': 12, 'required': False},
            {'name': '紧急联系电话', 'width': 15, 'required': False},
            {'name': '过敏史', 'width': 25, 'required': False},
            {'name': '既往病史', 'width': 30, 'required': False}
        ],
        'examples': [
            ['P001', '张三', '男', '45', '1979-03-15', '13800138001', '110101197903150001', '北京市朝阳区', '张四', '13800138002', '青霉素过敏', '高血压'],
            ['P002', '李四', '女', '32', '1992-07-22', '13800138003', '110101199207220002', '北京市海淀区', '李五', '13800138004', '无', '无'],
            ['P003', '王五', '男', '28', '1996-11-08', '13800138005', '110101199611080003', '北京市西城区', '王六', '13800138006', '海鲜过敏', '无']
        ]
    }


@api_bp.route('/batch-import', methods=['POST'])
@handle_web_error
def batch_import():
    """批量导入多个文件"""
    try:
        # 检查是否有文件上传
        if 'files' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有选择文件',
                'error_code': 'NO_FILES'
            }), 400

        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({
                'success': False,
                'error': '没有有效的文件',
                'error_code': 'NO_VALID_FILES'
            }), 400

        # 验证文件数量限制
        max_files = 10
        if len(files) > max_files:
            return jsonify({
                'success': False,
                'error': f'一次最多只能上传 {max_files} 个文件',
                'error_code': 'TOO_MANY_FILES'
            }), 400

        # 获取批量导入配置
        batch_config = {
            'privacy_enabled': request.form.get('enable_privacy', 'false').lower() == 'true',
            'stop_on_error': request.form.get('stop_on_error', 'false').lower() == 'true',
            'parallel_processing': request.form.get('parallel_processing', 'true').lower() == 'true'
        }

        # 生成批量导入ID
        batch_import_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 验证所有文件
        valid_files = []
        invalid_files = []

        for file in files:
            if file.filename == '':
                continue

            # 验证文件类型和大小
            if not allowed_file(file.filename):
                invalid_files.append({
                    'filename': file.filename,
                    'error': '不支持的文件格式'
                })
                continue

            # 验证文件大小
            file.seek(0, 2)
            file_size = file.tell()
            file.seek(0)

            max_size = 50 * 1024 * 1024  # 50MB
            if file_size > max_size:
                invalid_files.append({
                    'filename': file.filename,
                    'error': f'文件大小超过限制（最大 {max_size // (1024*1024)}MB）'
                })
                continue

            valid_files.append({
                'file': file,
                'filename': file.filename,
                'size': file_size
            })

        if not valid_files:
            return jsonify({
                'success': False,
                'error': '没有有效的文件可以导入',
                'invalid_files': invalid_files,
                'error_code': 'NO_VALID_FILES'
            }), 400

        # 保存文件并开始批量导入
        import_results = []
        total_success = 0
        total_failed = 0

        for file_info in valid_files:
            try:
                # 保存文件
                filename = secure_filename(file_info['filename'])
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                unique_filename = f"{timestamp}_{filename}"
                file_path = UPLOAD_DIR / unique_filename

                file_info['file'].save(file_path)

                # 执行单个文件导入
                result = data_service.import_excel_file(
                    str(file_path),
                    privacy_enabled=batch_config['privacy_enabled']
                )

                import_results.append({
                    'filename': file_info['filename'],
                    'status': 'success' if result.get('success', True) else 'failed',
                    'total_records': result.get('total_records', 0),
                    'new_records': result.get('new_records', 0),
                    'duplicate_records': result.get('duplicate_records', 0),
                    'error_records': result.get('error_records', 0),
                    'message': '导入成功'
                })

                total_success += 1

                # 清理临时文件
                if os.path.exists(file_path):
                    os.remove(file_path)

            except Exception as e:
                import_results.append({
                    'filename': file_info['filename'],
                    'status': 'failed',
                    'error': str(e),
                    'message': f'导入失败: {str(e)}'
                })

                total_failed += 1

                # 清理临时文件
                if 'file_path' in locals() and os.path.exists(file_path):
                    os.remove(file_path)

                # 如果配置为遇到错误停止，则中断处理
                if batch_config['stop_on_error']:
                    break

        # 记录批量导入结果
        batch_result = {
            'batch_id': batch_import_id,
            'total_files': len(valid_files),
            'success_files': total_success,
            'failed_files': total_failed,
            'invalid_files': len(invalid_files),
            'results': import_results,
            'invalid_files_detail': invalid_files,
            'config': batch_config
        }

        logger.info(f"批量导入完成: {batch_result}")

        return jsonify({
            'success': True,
            'data': batch_result
        })

    except Exception as e:
        logger.error(f"批量导入失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'批量导入失败: {str(e)}',
            'error_code': 'BATCH_IMPORT_ERROR'
        }), 500


@api_bp.route('/statistics')
@handle_web_error
@cached(ttl=300, key_prefix="stats_api_")  # 缓存5分钟
def get_statistics():
    """获取统计数据"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    analysis_type = request.args.get('type', 'dashboard')

    try:
        if analysis_type == 'dashboard':
            # 获取仪表盘数据
            stats = stats_service.get_dashboard_data(start_date, end_date)
        elif analysis_type == 'comprehensive':
            # 获取综合分析数据
            stats = stats_service.get_comprehensive_analysis(start_date, end_date)
        else:
            # 获取自定义分析数据
            stats = stats_service.get_custom_analysis(analysis_type, start_date, end_date)

        return jsonify({
            'success': True,
            'data': convert_to_json_serializable(stats)
        })

    except Exception as e:
        logger.error(f"获取统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/anesthesia-records', methods=['GET'])
def get_anesthesia_records():
    """获取麻醉记录列表"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        sort_field = request.args.get('sort_field', 'surgery_date')
        sort_order = request.args.get('sort_order', 'desc')

        # 筛选条件
        department = request.args.get('department', '')
        anesthesia_method = request.args.get('anesthesia_method', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        search = request.args.get('search', '')

        # 构建查询条件
        conditions = []
        params = []

        if department:
            conditions.append("department = ?")
            params.append(department)

        if anesthesia_method:
            conditions.append("(primary_anesthesia = ? OR anesthesia_method = ?)")
            params.extend([anesthesia_method, anesthesia_method])

        if start_date:
            conditions.append("surgery_date >= ?")
            params.append(start_date)

        if end_date:
            conditions.append("surgery_date <= ?")
            params.append(end_date)

        if search:
            conditions.append("(sr.patient_id LIKE ? OR sr.surgery_type LIKE ?)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param])

        # 构建WHERE子句
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)

        # 构建ORDER BY子句
        order_clause = f"ORDER BY {sort_field} {sort_order.upper()}"

        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询数据
        db = get_db_manager()
        with db.get_connection() as conn:
            # 获取总记录数 - 只计算有完整数据的记录
            count_query = f"""
                SELECT COUNT(*) as total
                FROM surgery_records sr
                WHERE sr.patient_name IS NOT NULL
                  AND sr.patient_name != ''
                  AND sr.surgery_name IS NOT NULL
                  AND sr.surgery_name != ''
                  {' AND ' + ' AND '.join(conditions) if conditions else ''}
            """
            total_result = conn.execute(count_query, params).fetchone()
            total_records = total_result['total'] if total_result else 0

            # 获取分页数据 - 只显示有完整数据的记录
            data_query = f"""
                SELECT
                    sr.id,
                    sr.surgery_date,
                    sr.patient_id as hospital_id,
                    sr.patient_name,
                    COALESCE(sr.department, '') as department,
                    sr.surgery_name,
                    COALESCE(sr.primary_anesthesia, sr.anesthesia_method, '') as anesthesia_method,
                    COALESCE(sr.anesthesiologist, '') as anesthesiologist,
                    COALESCE(sr.surgeon, '') as surgeon,
                    sr.duration_minutes,
                    sr.asa_grade,
                    sr.complications
                FROM surgery_records sr
                WHERE sr.patient_name IS NOT NULL
                  AND sr.patient_name != ''
                  AND sr.surgery_name IS NOT NULL
                  AND sr.surgery_name != ''
                  {' AND ' + ' AND '.join(conditions) if conditions else ''}
                {order_clause}
                LIMIT ? OFFSET ?
            """

            query_params = params + [page_size, offset]
            cursor = conn.execute(data_query, query_params)
            records = [dict(row) for row in cursor.fetchall()]

        return jsonify({
            'success': True,
            'data': records,
            'total': total_records,
            'page': page,
            'page_size': page_size,
            'total_pages': (total_records + page_size - 1) // page_size
        })

    except Exception as e:
        logger.error(f"获取麻醉记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/anesthesia-statistics', methods=['GET'])
def get_anesthesia_statistics():
    """获取麻醉记录统计信息"""
    try:
        db = get_db_manager()
        with db.get_connection() as conn:
            # 总记录数
            total_query = "SELECT COUNT(*) as total FROM surgery_records"
            total_result = conn.execute(total_query).fetchone()
            total_count = total_result['total'] if total_result else 0

            # 今日记录数
            today = datetime.now().strftime('%Y-%m-%d')
            today_query = "SELECT COUNT(*) as today FROM surgery_records WHERE surgery_date = ?"
            today_result = conn.execute(today_query, [today]).fetchone()
            today_count = today_result['today'] if today_result else 0

            # 科室数量
            dept_query = "SELECT COUNT(DISTINCT department) as dept_count FROM surgery_records WHERE department IS NOT NULL AND department != ''"
            dept_result = conn.execute(dept_query).fetchone()
            department_count = dept_result['dept_count'] if dept_result else 0

            # 麻醉方法数量
            anesthesia_query = "SELECT COUNT(DISTINCT primary_anesthesia) as anesthesia_count FROM surgery_records WHERE primary_anesthesia IS NOT NULL AND primary_anesthesia != ''"
            anesthesia_result = conn.execute(anesthesia_query).fetchone()
            anesthesia_method_count = anesthesia_result['anesthesia_count'] if anesthesia_result else 0

        return jsonify({
            'success': True,
            'data': {
                'total_count': total_count,
                'today_count': today_count,
                'department_count': department_count,
                'anesthesia_method_count': anesthesia_method_count
            }
        })

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
