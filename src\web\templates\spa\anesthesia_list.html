<!-- SPA版本的麻醉列表内容 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">麻醉列表</h1>
            <p class="text-base-content/60 mt-1">查看和管理麻醉记录</p>
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline btn-sm" onclick="refreshAnesthesiaList()">
                <i class="fas fa-sync-alt mr-2"></i>
                刷新
            </button>
            <a href="/data-import" class="btn btn-primary btn-sm" onclick="router.navigate('/data-import'); return false;">
                <i class="fas fa-plus mr-2"></i>
                导入数据
            </a>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">科室筛选</span>
                    </label>
                    <select class="select select-bordered" id="department-filter">
                        <option value="">所有科室</option>
                        <option value="外一科">外一科</option>
                        <option value="外二科">外二科</option>
                        <option value="泌尿外科">泌尿外科</option>
                        <option value="骨科">骨科</option>
                        <option value="神经外科">神经外科</option>
                        <option value="胸外科">胸外科</option>
                        <option value="妇产科">妇产科</option>
                        <option value="眼科">眼科</option>
                        <option value="耳鼻喉科">耳鼻喉科</option>
                    </select>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">麻醉方法</span>
                    </label>
                    <select class="select select-bordered" id="anesthesia-filter">
                        <option value="">所有方法</option>
                        <option value="全身麻醉">全身麻醉</option>
                        <option value="腰硬联合麻醉">腰硬联合麻醉</option>
                        <option value="神经阻滞麻醉">神经阻滞麻醉</option>
                        <option value="局部麻醉">局部麻醉</option>
                        <option value="椎管内麻醉">椎管内麻醉</option>
                    </select>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">开始日期</span>
                    </label>
                    <input type="date" class="input input-bordered" id="start-date">
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">结束日期</span>
                    </label>
                    <input type="date" class="input input-bordered" id="end-date">
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">&nbsp;</span>
                    </label>
                    <button class="btn btn-primary" onclick="filterAnesthesiaList()">
                        <i class="fas fa-search mr-2"></i>
                        筛选
                    </button>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="divider">搜索</div>
            <div class="form-control">
                <div class="input-group">
                    <input type="text" placeholder="搜索住院号、患者姓名或手术名称..." 
                           class="input input-bordered w-full" id="search-input">
                    <button class="btn btn-square" onclick="searchAnesthesia()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats stats-horizontal shadow w-full">
        <div class="stat">
            <div class="stat-figure text-primary">
                <i class="fas fa-procedures text-2xl"></i>
            </div>
            <div class="stat-title">总记录数</div>
            <div class="stat-value text-primary" id="total-count">0</div>
            <div class="stat-desc">麻醉记录总数</div>
        </div>
        
        <div class="stat">
            <div class="stat-figure text-secondary">
                <i class="fas fa-calendar-day text-2xl"></i>
            </div>
            <div class="stat-title">今日记录</div>
            <div class="stat-value text-secondary" id="today-count">0</div>
            <div class="stat-desc">今日新增记录</div>
        </div>
        
        <div class="stat">
            <div class="stat-figure text-accent">
                <i class="fas fa-hospital text-2xl"></i>
            </div>
            <div class="stat-title">科室数量</div>
            <div class="stat-value text-accent" id="department-count">0</div>
            <div class="stat-desc">涉及科室数量</div>
        </div>
        
        <div class="stat">
            <div class="stat-figure text-info">
                <i class="fas fa-syringe text-2xl"></i>
            </div>
            <div class="stat-title">麻醉方法</div>
            <div class="stat-value text-info" id="anesthesia-method-count">0</div>
            <div class="stat-desc">不同麻醉方法</div>
        </div>
    </div>

    <!-- 麻醉记录列表 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <div class="flex justify-between items-center mb-4">
                <h3 class="card-title">麻醉记录</h3>
                <div class="flex space-x-2">
                    <div class="dropdown dropdown-end">
                        <label tabindex="0" class="btn btn-outline btn-sm">
                            <i class="fas fa-download mr-2"></i>
                            导出
                        </label>
                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li><a onclick="exportToExcel()"><i class="fas fa-file-excel mr-2"></i>导出Excel</a></li>
                            <li><a onclick="exportToPDF()"><i class="fas fa-file-pdf mr-2"></i>导出PDF</a></li>
                            <li><a onclick="exportToCSV()"><i class="fas fa-file-csv mr-2"></i>导出CSV</a></li>
                        </ul>
                    </div>
                    <div class="dropdown dropdown-end">
                        <label tabindex="0" class="btn btn-outline btn-sm">
                            <i class="fas fa-cog mr-2"></i>
                            设置
                        </label>
                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li><a onclick="toggleColumnVisibility()"><i class="fas fa-columns mr-2"></i>显示/隐藏列</a></li>
                            <li><a onclick="resetFilters()"><i class="fas fa-undo mr-2"></i>重置筛选</a></li>
                            <li><a onclick="saveCurrentView()"><i class="fas fa-save mr-2"></i>保存当前视图</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="table table-zebra table-pin-rows w-full">
                    <thead>
                        <tr>
                            <th>
                                <button class="btn btn-ghost btn-sm" onclick="sortBy('surgery_date')">
                                    手术日期 <i class="fas fa-sort ml-1"></i>
                                </button>
                            </th>
                            <th>
                                <button class="btn btn-ghost btn-sm" onclick="sortBy('hospital_id')">
                                    住院号 <i class="fas fa-sort ml-1"></i>
                                </button>
                            </th>
                            <th>
                                <button class="btn btn-ghost btn-sm" onclick="sortBy('patient_name')">
                                    患者姓名 <i class="fas fa-sort ml-1"></i>
                                </button>
                            </th>
                            <th>
                                <button class="btn btn-ghost btn-sm" onclick="sortBy('department')">
                                    临床科室 <i class="fas fa-sort ml-1"></i>
                                </button>
                            </th>
                            <th>
                                <button class="btn btn-ghost btn-sm" onclick="sortBy('surgery_name')">
                                    手术名称 <i class="fas fa-sort ml-1"></i>
                                </button>
                            </th>
                            <th>
                                <button class="btn btn-ghost btn-sm" onclick="sortBy('anesthesia_method')">
                                    麻醉方法 <i class="fas fa-sort ml-1"></i>
                                </button>
                            </th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="anesthesia-table-body">
                        <!-- 加载中状态 -->
                        <tr>
                            <td colspan="7" class="text-center py-8">
                                <div class="flex items-center justify-center">
                                    <div class="loading loading-spinner loading-md mr-2"></div>
                                    <span>加载麻醉记录...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-between items-center mt-6">
                <div class="text-sm text-base-content/60">
                    显示第 <span id="page-start">1</span> - <span id="page-end">10</span> 条，
                    共 <span id="total-records">0</span> 条记录
                </div>
                <div class="join">
                    <button class="join-item btn btn-outline" onclick="previousPage()" id="prev-btn">«</button>
                    <button class="join-item btn btn-outline" id="page-info">第 1 页</button>
                    <button class="join-item btn btn-outline" onclick="nextPage()" id="next-btn">»</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 全局变量
    let currentPage = 1;
    let pageSize = 10;
    let totalRecords = 0;
    let currentSort = { field: 'surgery_date', order: 'desc' };
    let anesthesiaData = [];

    // 页面初始化 - 支持SPA路由
    function initAnesthesiaList() {
        console.log('🚀 麻醉列表页面初始化');

        // 确保DOM元素存在
        const tbody = document.getElementById('anesthesia-table-body');
        if (!tbody) {
            console.error('❌ 找不到表格元素，延迟重试...');
            setTimeout(initAnesthesiaList, 200);
            return;
        }

        console.log('✅ DOM元素已就绪，开始加载数据');
        loadAnesthesiaData();
        updateStatistics();
    }

    // 立即执行初始化（用于SPA）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAnesthesiaList);
    } else {
        // 如果DOM已经加载完成，立即执行
        setTimeout(initAnesthesiaList, 100);
    }

    // 为SPA路由提供全局初始化函数
    window.initAnesthesiaList = initAnesthesiaList;

    // 额外的初始化检查
    setTimeout(() => {
        const tbody = document.getElementById('anesthesia-table-body');
        if (tbody && tbody.innerHTML.includes('正在加载')) {
            console.warn('⚠️ 页面仍在加载状态，强制重新初始化');
            initAnesthesiaList();
        }
    }, 2000);

    // 防止其他页面的函数调用错误 - 全局函数定义
    window.handleFileSelect = function() {
        // 麻醉列表页面不需要文件选择功能
        console.warn('handleFileSelect called on anesthesia list page');
    };

    // 其他可能缺失的全局函数
    window.processFile = function() {
        console.warn('processFile called on anesthesia list page');
    };

    window.handleDrop = function() {
        console.warn('handleDrop called on anesthesia list page');
    };

    // 加载麻醉数据
    async function loadAnesthesiaData() {
        try {
            console.log('🔄 开始加载麻醉数据...');
            showLoading();

            // 构建查询参数
            const params = new URLSearchParams({
                page: currentPage,
                page_size: pageSize,
                sort_field: currentSort.field,
                sort_order: currentSort.order
            });

            console.log('📊 查询参数:', params.toString());

            // 添加筛选条件
            const departmentFilter = document.getElementById('department-filter')?.value;
            const anesthesiaFilter = document.getElementById('anesthesia-filter')?.value;
            const startDate = document.getElementById('start-date')?.value;
            const endDate = document.getElementById('end-date')?.value;
            const searchQuery = document.getElementById('search-input')?.value;

            if (departmentFilter) params.append('department', departmentFilter);
            if (anesthesiaFilter) params.append('anesthesia_method', anesthesiaFilter);
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            if (searchQuery) params.append('search', searchQuery);

            const url = `/api/anesthesia-records?${params}`;
            console.log('🌐 请求URL:', url);

            const response = await fetch(url);
            console.log('📡 响应状态:', response.status);

            const result = await response.json();
            console.log('📊 响应数据:', result);

            if (result.success) {
                anesthesiaData = result.data;
                totalRecords = result.total;
                console.log('✅ 数据加载成功:', anesthesiaData.length, '条记录');
                renderAnesthesiaTable(anesthesiaData);
                updatePagination();
            } else {
                console.error('❌ 数据加载失败:', result.error);
                showError('加载数据失败：' + result.error);
            }
        } catch (error) {
            console.error('❌ 加载麻醉数据失败:', error);
            showError('网络错误，请稍后重试');
        }
    }

    // 渲染麻醉记录表格
    function renderAnesthesiaTable(data) {
        console.log('🎨 开始渲染表格，数据条数:', data.length);
        const tbody = document.getElementById('anesthesia-table-body');

        if (!tbody) {
            console.error('❌ 找不到表格body元素');
            return;
        }

        if (data.length === 0) {
            console.log('📭 没有数据，显示空状态');
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-8">
                        <div class="text-base-content/60">
                            <i class="fas fa-inbox text-4xl mb-4"></i>
                            <p>暂无麻醉记录</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        console.log('📋 渲染数据，第一条记录:', data[0]);
        tbody.innerHTML = data.map(record => `
            <tr class="hover">
                <td>
                    <div class="text-sm font-medium">${formatDate(record.surgery_date)}</div>
                </td>
                <td>
                    <div class="font-mono text-sm">${record.hospital_id || '-'}</div>
                </td>
                <td>
                    <div class="font-medium">${record.patient_name || '-'}</div>
                </td>
                <td>
                    <div class="badge badge-outline">${record.department || '-'}</div>
                </td>
                <td>
                    <div class="max-w-xs truncate" title="${record.surgery_name || '-'}">
                        ${record.surgery_name || '-'}
                    </div>
                </td>
                <td>
                    <div class="badge badge-primary">${record.anesthesia_method || '-'}</div>
                </td>
                <td>
                    <div class="flex space-x-1">
                        <button class="btn btn-ghost btn-xs" onclick="viewRecord('${record.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-ghost btn-xs" onclick="editRecord('${record.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <div class="dropdown dropdown-end">
                            <label tabindex="0" class="btn btn-ghost btn-xs">
                                <i class="fas fa-ellipsis-v"></i>
                            </label>
                            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-32">
                                <li><a onclick="duplicateRecord('${record.id}')"><i class="fas fa-copy mr-2"></i>复制</a></li>
                                <li><a onclick="deleteRecord('${record.id}')" class="text-error"><i class="fas fa-trash mr-2"></i>删除</a></li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // 显示加载状态
    function showLoading() {
        const tbody = document.getElementById('anesthesia-table-body');
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-8">
                    <div class="flex items-center justify-center">
                        <div class="loading loading-spinner loading-md mr-2"></div>
                        <span>加载麻醉记录...</span>
                    </div>
                </td>
            </tr>
        `;
    }

    // 显示错误信息
    function showError(message) {
        const tbody = document.getElementById('anesthesia-table-body');
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-8">
                    <div class="text-error">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>${message}</p>
                        <button class="btn btn-sm btn-outline mt-2" onclick="loadAnesthesiaData()">重试</button>
                    </div>
                </td>
            </tr>
        `;
    }

    // 更新分页信息
    function updatePagination() {
        const pageStart = (currentPage - 1) * pageSize + 1;
        const pageEnd = Math.min(currentPage * pageSize, totalRecords);
        const totalPages = Math.ceil(totalRecords / pageSize);

        document.getElementById('page-start').textContent = pageStart;
        document.getElementById('page-end').textContent = pageEnd;
        document.getElementById('total-records').textContent = totalRecords;
        document.getElementById('page-info').textContent = `第 ${currentPage} 页`;

        // 更新按钮状态
        document.getElementById('prev-btn').disabled = currentPage <= 1;
        document.getElementById('next-btn').disabled = currentPage >= totalPages;
    }

    // 更新统计信息
    async function updateStatistics() {
        try {
            const response = await fetch('/api/anesthesia-statistics');
            const result = await response.json();

            if (result.success) {
                document.getElementById('total-count').textContent = result.data.total_count || 0;
                document.getElementById('today-count').textContent = result.data.today_count || 0;
                document.getElementById('department-count').textContent = result.data.department_count || 0;
                document.getElementById('anesthesia-method-count').textContent = result.data.anesthesia_method_count || 0;
            }
        } catch (error) {
            console.error('更新统计信息失败:', error);
        }
    }

    // 排序功能
    function sortBy(field) {
        if (currentSort.field === field) {
            currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
        } else {
            currentSort.field = field;
            currentSort.order = 'asc';
        }

        currentPage = 1; // 重置到第一页
        loadAnesthesiaData();
    }

    // 筛选功能
    function filterAnesthesiaList() {
        currentPage = 1; // 重置到第一页
        loadAnesthesiaData();
    }

    // 搜索功能
    function searchAnesthesia() {
        currentPage = 1; // 重置到第一页
        loadAnesthesiaData();
    }

    // 分页功能
    function previousPage() {
        if (currentPage > 1) {
            currentPage--;
            loadAnesthesiaData();
        }
    }

    function nextPage() {
        const totalPages = Math.ceil(totalRecords / pageSize);
        if (currentPage < totalPages) {
            currentPage++;
            loadAnesthesiaData();
        }
    }

    // 刷新数据
    function refreshAnesthesiaList() {
        loadAnesthesiaData();
        updateStatistics();
        showToast('数据已刷新', 'success');
    }

    // 格式化日期
    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    // 查看记录详情
    function viewRecord(recordId) {
        // TODO: 实现查看详情功能
        showToast('查看详情功能开发中...', 'info');
    }

    // 编辑记录
    function editRecord(recordId) {
        // TODO: 实现编辑功能
        showToast('编辑功能开发中...', 'info');
    }

    // 复制记录
    function duplicateRecord(recordId) {
        // TODO: 实现复制功能
        showToast('复制功能开发中...', 'info');
    }

    // 删除记录
    function deleteRecord(recordId) {
        if (confirm('确定要删除这条记录吗？此操作不可恢复。')) {
            // TODO: 实现删除功能
            showToast('删除功能开发中...', 'warning');
        }
    }

    // 导出功能
    function exportToExcel() {
        showToast('Excel导出功能开发中...', 'info');
    }

    function exportToPDF() {
        showToast('PDF导出功能开发中...', 'info');
    }

    function exportToCSV() {
        showToast('CSV导出功能开发中...', 'info');
    }

    // 列显示/隐藏
    function toggleColumnVisibility() {
        showToast('列显示设置功能开发中...', 'info');
    }

    // 重置筛选
    function resetFilters() {
        document.getElementById('department-filter').value = '';
        document.getElementById('anesthesia-filter').value = '';
        document.getElementById('start-date').value = '';
        document.getElementById('end-date').value = '';
        document.getElementById('search-input').value = '';

        currentPage = 1;
        loadAnesthesiaData();
        showToast('筛选条件已重置', 'success');
    }

    // 保存当前视图
    function saveCurrentView() {
        showToast('保存视图功能开发中...', 'info');
    }

    // Toast通知函数
    function showToast(message, type = 'info') {
        // 创建toast容器（如果不存在）
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast toast-top toast-end z-50';
            document.body.appendChild(toastContainer);
        }

        // 创建toast元素
        const toast = document.createElement('div');
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-error' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        toast.className = `alert ${alertClass}`;
        toast.innerHTML = `
            <span>${message}</span>
        `;

        // 添加到容器
        toastContainer.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
</script>
