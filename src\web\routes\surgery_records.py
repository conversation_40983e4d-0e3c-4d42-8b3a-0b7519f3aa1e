"""
手术记录API路由模块
"""
from flask import Blueprint, request, jsonify
from database.models import DatabaseManager
from utils.logger import logger
from utils.error_handler import handle_web_error

# 创建蓝图
surgery_records_bp = Blueprint('surgery_records', __name__, url_prefix='/api/surgery-records')

# 延迟初始化数据库管理器
db_manager = None

def get_db_manager():
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager


def convert_to_json_serializable(obj):
    """转换为JSON可序列化的对象"""
    import numpy as np
    import pandas as pd
    from datetime import datetime
    
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    else:
        return obj


@surgery_records_bp.route('')
@handle_web_error
def get_surgery_records():
    """获取手术记录列表（分页）"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 25))
        sort_field = request.args.get('sort_field', 'surgery_date')
        sort_order = request.args.get('sort_order', 'desc')
        search = request.args.get('search', '')

        # 筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        anesthesia_method = request.args.get('anesthesia_method', '')
        department = request.args.get('department', '')
        asa_grade = request.args.get('asa_grade', '')

        # 构建查询条件
        conditions = []
        params = []

        if search:
            conditions.append('''(
                COALESCE(p.masked_name, p.original_name) LIKE ? OR
                COALESCE(p.masked_id, p.original_id) LIKE ? OR
                sr.surgery_type LIKE ? OR
                sr.anesthesiologist LIKE ? OR
                sr.surgeon LIKE ?
            )''')
            search_param = f'%{search}%'
            params.extend([search_param] * 5)

        if start_date:
            conditions.append('sr.surgery_date >= ?')
            params.append(start_date)

        if end_date:
            conditions.append('sr.surgery_date <= ?')
            params.append(end_date)

        if anesthesia_method:
            conditions.append('sr.anesthesia_method = ?')
            params.append(anesthesia_method)

        if department:
            conditions.append('sr.department = ?')
            params.append(department)

        if asa_grade:
            conditions.append('sr.asa_grade = ?')
            params.append(asa_grade)

        # 构建WHERE子句
        where_clause = ''
        if conditions:
            where_clause = 'WHERE ' + ' AND '.join(conditions)

        # 验证排序字段
        valid_sort_fields = ['surgery_date', 'patient_name', 'anesthesia_method', 'surgery_type', 'department', 'duration_minutes', 'asa_grade']
        if sort_field not in valid_sort_fields:
            sort_field = 'surgery_date'

        if sort_order not in ['asc', 'desc']:
            sort_order = 'desc'

        # 处理排序字段的表前缀
        if sort_field == 'admission_number':
            order_by_field = 'COALESCE(p.masked_id, p.original_id)'  # 使用脱敏后的患者ID排序
        elif sort_field == 'patient_name':
            order_by_field = 'patient_name'  # 使用别名
        else:
            order_by_field = f'sr.{sort_field}'  # 使用手术记录表字段

        # 获取总记录数
        count_query = f'''
            SELECT COUNT(*)
            FROM surgery_records sr
            LEFT JOIN patients p ON sr.patient_id = p.id
            {where_clause}
        '''

        with db_manager.get_connection() as conn:
            cursor = conn.execute(count_query, params)
            total_records = cursor.fetchone()[0]

        # 计算分页信息
        total_pages = (total_records + page_size - 1) // page_size
        offset = (page - 1) * page_size

        # 获取记录数据 - 使用实际的数据库结构
        data_query = f'''
            SELECT
                sr.id,
                sr.surgery_date,
                COALESCE(p.masked_name, p.original_name, '未知') as patient_name,
                COALESCE(p.masked_id, p.original_id, '未知') as admission_number,
                sr.anesthesia_method,
                sr.surgery_type,
                sr.department,
                sr.duration_minutes,
                sr.asa_grade,
                sr.anesthesiologist,
                sr.surgeon,
                sr.complications,
                sr.created_at,
                sr.updated_at
            FROM surgery_records sr
            LEFT JOIN patients p ON sr.patient_id = p.id
            {where_clause}
            ORDER BY {order_by_field} {sort_order.upper()}
            LIMIT ? OFFSET ?
        '''

        with db_manager.get_connection() as conn:
            cursor = conn.execute(data_query, params + [page_size, offset])
            records = [dict(row) for row in cursor.fetchall()]

        # 构建分页信息
        pagination = {
            'current_page': page,
            'total_pages': total_pages,
            'total_records': total_records,
            'page_size': page_size,
            'has_prev': page > 1,
            'has_next': page < total_pages
        }

        return jsonify({
            'success': True,
            'records': convert_to_json_serializable(records),
            'pagination': pagination
        })

    except Exception as e:
        logger.error(f"获取手术记录列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@surgery_records_bp.route('/<int:record_id>')
@handle_web_error
def get_surgery_record_detail(record_id):
    """获取手术记录详情"""
    try:
        record = db_manager.get_surgery_record_by_id(record_id)
        if not record:
            return jsonify({
                'success': False,
                'error': '记录不存在'
            }), 404

        return jsonify({
            'success': True,
            'record': convert_to_json_serializable(record)
        })
    except Exception as e:
        logger.error(f"获取手术记录详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@surgery_records_bp.route('/<int:record_id>/navigation')
@handle_web_error
def get_surgery_record_navigation(record_id):
    """获取手术记录导航信息（上一条/下一条）"""
    try:
        navigation = db_manager.get_record_navigation(record_id)
        return jsonify({
            'success': True,
            'prev_id': navigation.get('prev_id'),
            'next_id': navigation.get('next_id')
        })
    except Exception as e:
        logger.error(f"获取记录导航信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@surgery_records_bp.route('/<int:record_id>', methods=['DELETE'])
@handle_web_error
def delete_surgery_record(record_id):
    """删除手术记录"""
    try:
        success = db_manager.delete_surgery_record(record_id)
        if success:
            # 使用智能缓存管理器清理相关缓存
            from utils.cache_manager import invalidate_surgery_cache
            cleared_count = invalidate_surgery_cache('delete')

            logger.info(f"手术记录 {record_id} 删除成功，清理了 {cleared_count} 个缓存条目")

            return jsonify({
                'success': True,
                'message': '记录删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '记录不存在或删除失败'
            }), 404
    except Exception as e:
        logger.error(f"删除手术记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@surgery_records_bp.route('/batch-delete', methods=['POST'])
@handle_web_error
def batch_delete_surgery_records():
    """批量删除手术记录"""
    try:
        data = request.get_json()
        record_ids = data.get('ids', [])

        if not record_ids:
            return jsonify({
                'success': False,
                'error': '没有选择要删除的记录'
            }), 400

        deleted_count = db_manager.batch_delete_surgery_records(record_ids)

        if deleted_count > 0:
            # 使用智能缓存管理器清理相关缓存
            from utils.cache_manager import invalidate_surgery_cache
            cleared_count = invalidate_surgery_cache('delete')

            logger.info(f"批量删除 {deleted_count} 条手术记录成功，清理了 {cleared_count} 个缓存条目")

        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'成功删除 {deleted_count} 条记录'
        })
    except Exception as e:
        logger.error(f"批量删除手术记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
