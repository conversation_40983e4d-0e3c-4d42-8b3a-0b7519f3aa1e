"""
Flask性能优化中间件
"""
import gzip
import io
import time
from functools import wraps
from flask import request, g, has_request_context
from utils.logger import logger


class FlaskPerformanceMiddleware:
    """Flask性能优化中间件"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        app.teardown_appcontext(self.teardown_request)
        
        # 注册性能优化配置
        self.setup_performance_config(app)
    
    def setup_performance_config(self, app):
        """设置性能优化配置"""
        # 禁用不必要的功能
        app.config.setdefault('SEND_FILE_MAX_AGE_DEFAULT', 31536000)  # 1年
        app.config.setdefault('JSON_SORT_KEYS', False)
        app.config.setdefault('JSONIFY_PRETTYPRINT_REGULAR', False)
        
        logger.info("Flask性能优化配置已应用")
    
    def before_request(self):
        """请求前处理"""
        # 记录请求开始时间
        g.start_time = time.time()
        
        # 检查是否需要压缩
        g.compress_response = self.should_compress_response()
        
        # 设置请求ID用于日志追踪
        g.request_id = f"{int(time.time() * 1000)}-{id(request)}"
    
    def after_request(self, response):
        """请求后处理"""
        # 计算请求处理时间
        if hasattr(g, 'start_time'):
            duration = (time.time() - g.start_time) * 1000
            response.headers['X-Response-Time'] = f"{duration:.2f}ms"
            
            # 记录慢请求
            if duration > 1000:  # 超过1秒的请求
                logger.warning(f"慢请求: {request.path} - {duration:.2f}ms")
        
        # 应用响应压缩
        if hasattr(g, 'compress_response') and g.compress_response:
            response = self.compress_response(response)
        
        # 添加性能相关的响应头
        self.add_performance_headers(response)
        
        return response
    
    def teardown_request(self, exception):
        """请求结束处理"""
        if exception:
            try:
                # 尝试获取请求路径，如果上下文已销毁则使用默认值
                path = getattr(request, 'path', 'unknown') if has_request_context() else 'unknown'
                logger.error(f"请求异常: {path} - {str(exception)}")
            except RuntimeError:
                # 如果仍然出现运行时错误，只记录异常信息
                logger.error(f"请求异常: {str(exception)}")
    
    def should_compress_response(self):
        """判断是否应该压缩响应"""
        # 检查客户端是否支持gzip
        accept_encoding = request.headers.get('Accept-Encoding', '')
        if 'gzip' not in accept_encoding.lower():
            return False
        
        # 检查请求类型
        if request.endpoint == 'static':
            return True
        
        # 检查内容类型
        return True
    
    def compress_response(self, response):
        """压缩响应内容"""
        # 只压缩文本内容
        if not response.direct_passthrough and response.status_code == 200:
            content_type = response.headers.get('Content-Type', '')
            
            # 检查是否为可压缩的内容类型
            compressible_types = [
                'text/', 'application/json', 'application/javascript',
                'application/xml', 'application/rss+xml'
            ]
            
            if any(ct in content_type for ct in compressible_types):
                # 获取响应数据
                data = response.get_data()
                
                # 只压缩大于1KB的内容
                if len(data) > 1024:
                    # 压缩数据
                    gzip_buffer = io.BytesIO()
                    with gzip.GzipFile(fileobj=gzip_buffer, mode='wb') as gzip_file:
                        gzip_file.write(data)
                    
                    compressed_data = gzip_buffer.getvalue()
                    
                    # 只有在压缩后更小时才使用压缩
                    if len(compressed_data) < len(data):
                        response.set_data(compressed_data)
                        response.headers['Content-Encoding'] = 'gzip'
                        response.headers['Content-Length'] = len(compressed_data)
                        response.headers['Vary'] = 'Accept-Encoding'
        
        return response
    
    def add_performance_headers(self, response):
        """添加性能相关的响应头"""
        # 添加缓存控制头
        if request.endpoint == 'static':
            # 静态文件长期缓存
            response.headers['Cache-Control'] = 'public, max-age=31536000, immutable'
            response.headers['Expires'] = 'Thu, 31 Dec 2037 23:55:55 GMT'
        elif request.endpoint and 'api' in request.endpoint:
            # API短期缓存
            response.headers['Cache-Control'] = 'public, max-age=300'
        else:
            # 页面缓存
            response.headers['Cache-Control'] = 'public, max-age=60'
        
        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # 添加性能提示头
        response.headers['X-Powered-By'] = 'Flask-Optimized'


def enable_flask_performance(app):
    """启用Flask性能优化"""
    middleware = FlaskPerformanceMiddleware(app)
    logger.info("Flask性能中间件已启用")
    return middleware


# 装饰器：缓存视图函数
def cache_view(timeout=300):
    """视图函数缓存装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 生成缓存键
            cache_key = f"view_{f.__name__}_{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            from utils.cache import cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = f(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            
            return result
        return decorated_function
    return decorator


# 装饰器：性能监控
def monitor_performance(f):
    """性能监控装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = f(*args, **kwargs)
            duration = (time.time() - start_time) * 1000
            
            # 记录性能指标
            if duration > 500:  # 超过500ms的函数
                logger.warning(f"慢函数: {f.__name__} - {duration:.2f}ms")
            
            return result
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            logger.error(f"函数异常: {f.__name__} - {duration:.2f}ms - {str(e)}")
            raise
    
    return decorated_function


# 优化Flask JSON编码器
class OptimizedJSONEncoder:
    """优化的JSON编码器"""
    
    @staticmethod
    def default(obj):
        """处理特殊对象的JSON序列化"""
        import decimal
        import datetime
        
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        elif isinstance(obj, (datetime.datetime, datetime.date)):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        
        return str(obj)


def setup_optimized_json(app):
    """设置优化的JSON处理"""
    app.json_encoder = OptimizedJSONEncoder
    app.config['JSON_SORT_KEYS'] = False
    app.config['JSONIFY_PRETTYPRINT_REGULAR'] = False
    
    logger.info("优化的JSON编码器已设置")
