"""
数据处理管道
整合数据清洗、脱敏、验证等功能
"""
import pandas as pd
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json

from .data_cleaner import DataCleaner
from .data_anonymizer import DataAnonymizer
from utils.logger import logger


class DataProcessingPipeline:
    """数据处理管道"""
    
    def __init__(self):
        self.cleaner = DataCleaner()
        self.anonymizer = DataAnonymizer()
        self.processing_history = []
        logger.info("数据处理管道初始化完成")
    
    def process_file(self, 
                    file_path: str,
                    output_dir: str = 'output',
                    clean_data: bool = True,
                    anonymize_data: bool = True,
                    field_mapping: Dict[str, str] = None,
                    anonymize_config: Dict[str, Dict[str, str]] = None,
                    save_intermediate: bool = True) -> Dict[str, Any]:
        """
        处理单个文件
        
        Args:
            file_path: 输入文件路径
            output_dir: 输出目录
            clean_data: 是否进行数据清洗
            anonymize_data: 是否进行数据脱敏
            field_mapping: 字段映射配置
            anonymize_config: 脱敏配置
            save_intermediate: 是否保存中间结果
        
        Returns:
            处理结果字典
        """
        logger.info(f"开始处理文件: {file_path}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取文件信息
        file_name = os.path.basename(file_path)
        file_name_without_ext = os.path.splitext(file_name)[0]
        
        try:
            # 1. 读取原始数据
            logger.info("读取原始数据...")
            if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                original_df = pd.read_excel(file_path, engine='openpyxl')
            elif file_path.endswith('.csv'):
                original_df = pd.read_csv(file_path, encoding='utf-8')
            else:
                raise ValueError(f"不支持的文件格式: {file_path}")
            
            logger.info(f"原始数据: {len(original_df)} 行, {len(original_df.columns)} 列")
            
            # 处理结果
            result = {
                'file_path': file_path,
                'file_name': file_name,
                'original_rows': len(original_df),
                'original_columns': len(original_df.columns),
                'processing_steps': [],
                'output_files': [],
                'errors': [],
                'warnings': []
            }
            
            current_df = original_df.copy()
            
            # 2. 数据清洗
            if clean_data:
                logger.info("执行数据清洗...")
                try:
                    cleaned_df = self.cleaner.clean_dataframe(current_df, field_mapping)
                    cleaning_summary = self.cleaner.get_cleaning_summary(current_df, cleaned_df)
                    
                    result['processing_steps'].append({
                        'step': 'data_cleaning',
                        'status': 'success',
                        'summary': cleaning_summary
                    })
                    
                    # 保存清洗后的数据
                    if save_intermediate:
                        cleaned_file = os.path.join(output_dir, f"{file_name_without_ext}_cleaned.xlsx")
                        cleaned_df.to_excel(cleaned_file, index=False)
                        result['output_files'].append(cleaned_file)
                        logger.info(f"清洗后数据已保存: {cleaned_file}")
                    
                    current_df = cleaned_df
                    
                except Exception as e:
                    error_msg = f"数据清洗失败: {str(e)}"
                    logger.error(error_msg)
                    result['errors'].append(error_msg)
                    result['processing_steps'].append({
                        'step': 'data_cleaning',
                        'status': 'failed',
                        'error': error_msg
                    })
            
            # 3. 数据脱敏
            if anonymize_data:
                logger.info("执行数据脱敏...")
                try:
                    anonymized_df = self.anonymizer.anonymize_dataframe(current_df, anonymize_config)
                    anonymization_summary = self.anonymizer.get_anonymization_summary(current_df, anonymized_df)
                    
                    result['processing_steps'].append({
                        'step': 'data_anonymization',
                        'status': 'success',
                        'summary': anonymization_summary
                    })
                    
                    # 保存脱敏后的数据
                    if save_intermediate:
                        anonymized_file = os.path.join(output_dir, f"{file_name_without_ext}_anonymized.xlsx")
                        anonymized_df.to_excel(anonymized_file, index=False)
                        result['output_files'].append(anonymized_file)
                        logger.info(f"脱敏后数据已保存: {anonymized_file}")
                    
                    current_df = anonymized_df
                    
                except Exception as e:
                    error_msg = f"数据脱敏失败: {str(e)}"
                    logger.error(error_msg)
                    result['errors'].append(error_msg)
                    result['processing_steps'].append({
                        'step': 'data_anonymization',
                        'status': 'failed',
                        'error': error_msg
                    })
            
            # 4. 保存最终结果
            final_file = os.path.join(output_dir, f"{file_name_without_ext}_processed.xlsx")
            current_df.to_excel(final_file, index=False)
            result['output_files'].append(final_file)
            result['final_file'] = final_file
            result['final_rows'] = len(current_df)
            result['final_columns'] = len(current_df.columns)
            
            logger.info(f"最终结果已保存: {final_file}")
            
            # 5. 生成处理报告
            report_file = os.path.join(output_dir, f"{file_name_without_ext}_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            result['report_file'] = report_file
            
            # 记录处理历史
            self.processing_history.append({
                'timestamp': datetime.now(),
                'file_path': file_path,
                'result': result
            })
            
            logger.info(f"文件处理完成: {file_path}")
            return result
            
        except Exception as e:
            error_msg = f"文件处理失败: {str(e)}"
            logger.error(error_msg)
            result = {
                'file_path': file_path,
                'file_name': file_name,
                'status': 'failed',
                'error': error_msg,
                'processing_steps': [],
                'output_files': [],
                'errors': [error_msg]
            }
            return result
    
    def process_batch(self, 
                     file_paths: List[str],
                     output_dir: str = 'output',
                     **kwargs) -> List[Dict[str, Any]]:
        """批量处理文件"""
        logger.info(f"开始批量处理 {len(file_paths)} 个文件")
        
        results = []
        for file_path in file_paths:
            try:
                result = self.process_file(file_path, output_dir, **kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                results.append({
                    'file_path': file_path,
                    'status': 'failed',
                    'error': str(e)
                })
        
        # 生成批量处理报告
        batch_report = {
            'timestamp': datetime.now(),
            'total_files': len(file_paths),
            'successful_files': len([r for r in results if 'error' not in r or not r['error']]),
            'failed_files': len([r for r in results if 'error' in r and r['error']]),
            'results': results
        }
        
        batch_report_file = os.path.join(output_dir, f"batch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(batch_report_file, 'w', encoding='utf-8') as f:
            json.dump(batch_report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"批量处理完成，报告已保存: {batch_report_file}")
        return results
    
    def get_processing_history(self) -> List[Dict[str, Any]]:
        """获取处理历史"""
        return self.processing_history
    
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """验证文件格式和内容"""
        validation_result = {
            'file_path': file_path,
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                validation_result['is_valid'] = False
                validation_result['errors'].append("文件不存在")
                return validation_result
            
            # 检查文件格式
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.xlsx', '.xls', '.csv']:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"不支持的文件格式: {file_ext}")
                return validation_result
            
            # 读取文件基本信息
            if file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path, engine='openpyxl')
            else:
                df = pd.read_csv(file_path, encoding='utf-8')
            
            validation_result['file_info'] = {
                'rows': len(df),
                'columns': len(df.columns),
                'column_names': list(df.columns),
                'file_size': os.path.getsize(file_path)
            }
            
            # 检查是否有数据
            if len(df) == 0:
                validation_result['warnings'].append("文件中没有数据行")
            
            # 检查是否有必要的列
            required_columns = ['麻醉方法', '患者姓名', '手术日期']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                validation_result['warnings'].append(f"缺少推荐的列: {missing_columns}")
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"文件验证失败: {str(e)}")
        
        return validation_result


def create_default_config() -> Dict[str, Any]:
    """创建默认配置"""
    return {
        'field_mapping': {
            '麻醉方法': 'anesthesia_method',
            '患者姓名': 'patient_name',
            '年龄': 'age',
            '性别': 'gender',
            '联系电话': 'phone',
            '身份证号': 'id_card',
            '手术日期': 'surgery_date',
            '手术类型': 'surgery_type',
            '科室': 'department',
            '麻醉医生': 'anesthesiologist',
            '手术医生': 'surgeon'
        },
        'anonymize_config': {
            '患者姓名': {'type': 'name', 'method': 'mask'},
            '联系电话': {'type': 'phone', 'method': 'mask'},
            '身份证号': {'type': 'id_card', 'method': 'mask'}
        },
        'processing_options': {
            'clean_data': True,
            'anonymize_data': True,
            'save_intermediate': True
        }
    }
