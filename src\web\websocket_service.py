"""
WebSocket服务 - 用于实时进度更新和状态通知
"""

import json
from typing import Dict, Set, Any
from flask import Flask
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from utils.logger import logger


class WebSocketService:
    """WebSocket服务管理器"""
    
    def __init__(self, app: Flask = None):
        self.socketio = None
        self.active_connections: Dict[str, Set[str]] = {}  # room_id -> set of session_ids
        self.import_sessions: Dict[str, Dict[str, Any]] = {}  # session_id -> import_info
        
        if app:
            self.init_app(app)
    
    def init_app(self, app: Flask):
        """初始化WebSocket服务"""
        self.socketio = SocketIO(
            app,
            cors_allowed_origins="*",
            async_mode='threading',
            logger=False,
            engineio_logger=False
        )
        
        # 注册事件处理器
        self._register_handlers()
        logger.info("WebSocket服务初始化完成")
    
    def _register_handlers(self):
        """注册WebSocket事件处理器"""
        
        @self.socketio.on('connect')
        def handle_connect(auth):
            from flask import request
            session_id = request.sid
            logger.info(f"客户端连接: {session_id}")
            emit('connected', {'status': 'success', 'message': '连接成功'})

        @self.socketio.on('disconnect')
        def handle_disconnect():
            from flask import request
            try:
                session_id = request.sid
                logger.info(f"客户端断开连接: {session_id}")

                # 清理连接记录
                self._cleanup_session(session_id)
            except Exception as e:
                logger.error(f"处理断开连接时出错: {str(e)}")
        
        @self.socketio.on('join_import_room')
        def handle_join_import_room(data):
            """加入导入进度房间"""
            from flask import request
            session_id = request.sid
            import_id = data.get('import_id')
            
            if not import_id:
                emit('error', {'message': '缺少导入ID'})
                return
            
            # 加入房间
            join_room(import_id)
            
            # 记录连接
            if import_id not in self.active_connections:
                self.active_connections[import_id] = set()
            self.active_connections[import_id].add(session_id)
            
            logger.info(f"客户端 {session_id} 加入导入房间: {import_id}")
            emit('joined_room', {'import_id': import_id, 'status': 'success'})
        
        @self.socketio.on('leave_import_room')
        def handle_leave_import_room(data):
            """离开导入进度房间"""
            from flask import request
            session_id = request.sid
            import_id = data.get('import_id')
            
            if import_id:
                leave_room(import_id)
                
                # 移除连接记录
                if import_id in self.active_connections:
                    self.active_connections[import_id].discard(session_id)
                    if not self.active_connections[import_id]:
                        del self.active_connections[import_id]
                
                logger.info(f"客户端 {session_id} 离开导入房间: {import_id}")
                emit('left_room', {'import_id': import_id, 'status': 'success'})
    
    def _cleanup_session(self, session_id: str):
        """清理会话相关的所有记录"""
        # 从所有房间中移除
        for import_id, sessions in list(self.active_connections.items()):
            if session_id in sessions:
                sessions.discard(session_id)
                if not sessions:
                    del self.active_connections[import_id]
        
        # 清理导入会话记录
        if session_id in self.import_sessions:
            del self.import_sessions[session_id]
    
    def send_import_progress(self, import_id: str, progress_data: Dict[str, Any]):
        """发送导入进度更新"""
        if not self.socketio:
            return
        
        try:
            # 确保数据可序列化
            serializable_data = self._make_serializable(progress_data)
            
            # 发送到指定房间
            self.socketio.emit(
                'import_progress',
                serializable_data,
                room=import_id
            )
            
            logger.debug(f"发送进度更新到房间 {import_id}: {serializable_data}")
            
        except Exception as e:
            logger.error(f"发送进度更新失败: {str(e)}")
    
    def send_import_log(self, import_id: str, log_data: Dict[str, Any]):
        """发送导入日志"""
        if not self.socketio:
            return
        
        try:
            serializable_data = self._make_serializable(log_data)
            
            self.socketio.emit(
                'import_log',
                serializable_data,
                room=import_id
            )
            
            logger.debug(f"发送日志到房间 {import_id}: {serializable_data}")
            
        except Exception as e:
            logger.error(f"发送日志失败: {str(e)}")
    
    def send_import_complete(self, import_id: str, result_data: Dict[str, Any]):
        """发送导入完成通知"""
        if not self.socketio:
            return
        
        try:
            serializable_data = self._make_serializable(result_data)
            
            self.socketio.emit(
                'import_complete',
                serializable_data,
                room=import_id
            )
            
            logger.info(f"发送完成通知到房间 {import_id}: {serializable_data}")
            
        except Exception as e:
            logger.error(f"发送完成通知失败: {str(e)}")
    
    def send_import_error(self, import_id: str, error_data: Dict[str, Any]):
        """发送导入错误通知"""
        if not self.socketio:
            return
        
        try:
            serializable_data = self._make_serializable(error_data)
            
            self.socketio.emit(
                'import_error',
                serializable_data,
                room=import_id
            )
            
            logger.error(f"发送错误通知到房间 {import_id}: {serializable_data}")
            
        except Exception as e:
            logger.error(f"发送错误通知失败: {str(e)}")
    
    def _make_serializable(self, data: Any) -> Any:
        """确保数据可以JSON序列化"""
        try:
            # 尝试序列化，如果失败则转换
            json.dumps(data)
            return data
        except (TypeError, ValueError):
            # 转换不可序列化的对象
            if isinstance(data, dict):
                return {k: self._make_serializable(v) for k, v in data.items()}
            elif isinstance(data, (list, tuple)):
                return [self._make_serializable(item) for item in data]
            else:
                return str(data)
    
    def get_active_connections(self, import_id: str = None) -> Dict[str, Any]:
        """获取活跃连接信息"""
        if import_id:
            return {
                'import_id': import_id,
                'connections': len(self.active_connections.get(import_id, set())),
                'session_ids': list(self.active_connections.get(import_id, set()))
            }
        else:
            return {
                'total_rooms': len(self.active_connections),
                'total_connections': sum(len(sessions) for sessions in self.active_connections.values()),
                'rooms': {
                    room_id: len(sessions) 
                    for room_id, sessions in self.active_connections.items()
                }
            }


# 全局WebSocket服务实例
websocket_service = WebSocketService()


def init_websocket(app: Flask) -> WebSocketService:
    """初始化WebSocket服务"""
    websocket_service.init_app(app)
    return websocket_service


def get_websocket_service() -> WebSocketService:
    """获取WebSocket服务实例"""
    return websocket_service
