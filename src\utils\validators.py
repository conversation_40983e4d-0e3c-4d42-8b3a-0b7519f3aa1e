"""
数据验证工具模块
"""
import re
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from utils.logger import logger


class ValidationError(Exception):
    """验证错误异常"""
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(message)


class Validator:
    """数据验证器基类"""
    
    def __init__(self, required: bool = False, message: str = None):
        self.required = required
        self.message = message
    
    def validate(self, value: Any, field_name: str = None) -> Any:
        """验证数据"""
        if value is None or value == '':
            if self.required:
                raise ValidationError(
                    self.message or f"{field_name or 'Field'} is required",
                    field_name
                )
            return value
        
        return self._validate_value(value, field_name)
    
    def _validate_value(self, value: Any, field_name: str = None) -> Any:
        """子类实现具体验证逻辑"""
        return value


class StringValidator(Validator):
    """字符串验证器"""
    
    def __init__(self, min_length: int = None, max_length: int = None, 
                 pattern: str = None, **kwargs):
        super().__init__(**kwargs)
        self.min_length = min_length
        self.max_length = max_length
        self.pattern = re.compile(pattern) if pattern else None
    
    def _validate_value(self, value: Any, field_name: str = None) -> str:
        if not isinstance(value, str):
            value = str(value)
        
        if self.min_length and len(value) < self.min_length:
            raise ValidationError(
                f"{field_name or 'Field'} must be at least {self.min_length} characters",
                field_name
            )
        
        if self.max_length and len(value) > self.max_length:
            raise ValidationError(
                f"{field_name or 'Field'} must be at most {self.max_length} characters",
                field_name
            )
        
        if self.pattern and not self.pattern.match(value):
            raise ValidationError(
                f"{field_name or 'Field'} format is invalid",
                field_name
            )
        
        return value


class IntegerValidator(Validator):
    """整数验证器"""
    
    def __init__(self, min_value: int = None, max_value: int = None, **kwargs):
        super().__init__(**kwargs)
        self.min_value = min_value
        self.max_value = max_value
    
    def _validate_value(self, value: Any, field_name: str = None) -> int:
        try:
            value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(
                f"{field_name or 'Field'} must be an integer",
                field_name
            )
        
        if self.min_value is not None and value < self.min_value:
            raise ValidationError(
                f"{field_name or 'Field'} must be at least {self.min_value}",
                field_name
            )
        
        if self.max_value is not None and value > self.max_value:
            raise ValidationError(
                f"{field_name or 'Field'} must be at most {self.max_value}",
                field_name
            )
        
        return value


class DateValidator(Validator):
    """日期验证器"""
    
    def __init__(self, date_format: str = "%Y-%m-%d", **kwargs):
        super().__init__(**kwargs)
        self.date_format = date_format
    
    def _validate_value(self, value: Any, field_name: str = None) -> str:
        if isinstance(value, datetime):
            return value.strftime(self.date_format)
        
        if isinstance(value, str):
            try:
                datetime.strptime(value, self.date_format)
                return value
            except ValueError:
                raise ValidationError(
                    f"{field_name or 'Field'} must be in format {self.date_format}",
                    field_name
                )
        
        raise ValidationError(
            f"{field_name or 'Field'} must be a valid date",
            field_name
        )


class ChoiceValidator(Validator):
    """选择验证器"""
    
    def __init__(self, choices: List[Any], **kwargs):
        super().__init__(**kwargs)
        self.choices = choices
    
    def _validate_value(self, value: Any, field_name: str = None) -> Any:
        if value not in self.choices:
            raise ValidationError(
                f"{field_name or 'Field'} must be one of: {', '.join(map(str, self.choices))}",
                field_name
            )
        return value


class EmailValidator(StringValidator):
    """邮箱验证器"""
    
    def __init__(self, **kwargs):
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        super().__init__(pattern=email_pattern, **kwargs)
    
    def _validate_value(self, value: Any, field_name: str = None) -> str:
        value = super()._validate_value(value, field_name)
        if not self.pattern.match(value):
            raise ValidationError(
                f"{field_name or 'Field'} must be a valid email address",
                field_name
            )
        return value


class PhoneValidator(StringValidator):
    """手机号验证器"""
    
    def __init__(self, **kwargs):
        # 中国手机号格式
        phone_pattern = r'^1[3-9]\d{9}$'
        super().__init__(pattern=phone_pattern, **kwargs)
    
    def _validate_value(self, value: Any, field_name: str = None) -> str:
        value = super()._validate_value(value, field_name)
        if not self.pattern.match(value):
            raise ValidationError(
                f"{field_name or 'Field'} must be a valid phone number",
                field_name
            )
        return value


class DataSchema:
    """数据模式验证器"""
    
    def __init__(self, schema: Dict[str, Validator]):
        self.schema = schema
    
    def validate(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据字典"""
        validated_data = {}
        errors = {}
        
        for field_name, validator in self.schema.items():
            try:
                value = data.get(field_name)
                validated_data[field_name] = validator.validate(value, field_name)
            except ValidationError as e:
                errors[field_name] = e.message
        
        if errors:
            raise ValidationError(f"Validation failed: {errors}")
        
        return validated_data


# 预定义的验证模式
PATIENT_SCHEMA = DataSchema({
    'patient_id': StringValidator(required=True, max_length=50),
    'original_name': StringValidator(max_length=100),
    'masked_name': StringValidator(max_length=100),
    'age': IntegerValidator(min_value=0, max_value=150),
    'gender': ChoiceValidator(choices=['男', '女']),
    'phone': PhoneValidator(),
    'contact': StringValidator(max_length=200)
})

SURGERY_RECORD_SCHEMA = DataSchema({
    'patient_id': IntegerValidator(required=True, min_value=1),
    'surgery_date': DateValidator(required=True),
    'anesthesia_method': StringValidator(required=True, max_length=100),
    'surgery_type': StringValidator(required=True, max_length=200),
    'department': StringValidator(max_length=100),
    'duration_minutes': IntegerValidator(min_value=0, max_value=2000),
    'anesthesiologist': StringValidator(max_length=100),
    'surgeon': StringValidator(max_length=100)
})

PAGINATION_SCHEMA = DataSchema({
    'page': IntegerValidator(min_value=1, required=False),
    'page_size': IntegerValidator(min_value=1, max_value=1000, required=False),
    'sort_field': StringValidator(max_length=50, required=False),
    'sort_order': ChoiceValidator(choices=['asc', 'desc'], required=False)
})


def validate_pagination_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """验证分页参数"""
    try:
        return PAGINATION_SCHEMA.validate(params)
    except ValidationError as e:
        logger.warning(f"分页参数验证失败: {e.message}")
        # 返回默认值
        return {
            'page': 1,
            'page_size': 25,
            'sort_field': 'created_at',
            'sort_order': 'desc'
        }


def validate_search_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """验证搜索参数"""
    validated = {}
    
    # 搜索关键词
    search = params.get('search', '').strip()
    if search and len(search) > 100:
        search = search[:100]
    validated['search'] = search
    
    # 日期范围
    start_date = params.get('start_date', '').strip()
    end_date = params.get('end_date', '').strip()
    
    if start_date:
        try:
            DateValidator().validate(start_date, 'start_date')
            validated['start_date'] = start_date
        except ValidationError:
            validated['start_date'] = ''
    
    if end_date:
        try:
            DateValidator().validate(end_date, 'end_date')
            validated['end_date'] = end_date
        except ValidationError:
            validated['end_date'] = ''
    
    return validated


def sanitize_sql_input(value: str) -> str:
    """清理SQL输入，防止注入"""
    if not isinstance(value, str):
        return str(value)
    
    # 移除危险字符
    dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
    for char in dangerous_chars:
        value = value.replace(char, '')
    
    return value.strip()


def validate_file_upload(file_data: Dict[str, Any]) -> Dict[str, Any]:
    """验证文件上传参数"""
    schema = DataSchema({
        'filename': StringValidator(required=True, max_length=255),
        'file_size': IntegerValidator(required=True, min_value=1, max_value=50*1024*1024),  # 50MB
        'file_type': ChoiceValidator(choices=['xlsx', 'xls'], required=True)
    })
    
    return schema.validate(file_data)
