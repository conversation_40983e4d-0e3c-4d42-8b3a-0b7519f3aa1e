"""
手术统计模块
"""
from typing import Dict, Any, List, Tuple
from database.models import DatabaseManager
from utils.logger import logger


class SurgeryStatistics:
    """手术相关统计分析"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_surgery_type_distribution(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取手术类型分布统计"""
        logger.info("获取手术类型分布统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    COALESCE(surgery_type, '未知') as type,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY surgery_type
                ORDER BY count DESC
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def get_surgery_name_top(self, start_date: str = None, end_date: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """获取手术名称排行榜"""
        logger.info(f"获取手术名称排行榜 (Top {limit})")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    surgery_name,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM surgery_records {where_clause}), 2) as percentage
                FROM surgery_records 
                {where_clause}
                AND surgery_name IS NOT NULL AND surgery_name != ''
                GROUP BY surgery_name
                ORDER BY count DESC
                LIMIT ?
            ''', params + [limit])
            
            result = []
            for i, row in enumerate(cursor.fetchall(), 1):
                result.append({
                    'rank': i,
                    'surgery_name': row[0],
                    'count': row[1],
                    'percentage': row[2]
                })
            
            return result
    
    def get_operating_room_utilization(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取手术室利用率统计"""
        logger.info("获取手术室利用率统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 手术室使用统计
            cursor = conn.execute(f'''
                SELECT 
                    operating_room,
                    operating_room_number,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT DATE(surgery_date)) as usage_days,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration
                FROM surgery_records 
                {where_clause}
                AND operating_room IS NOT NULL AND operating_room != ''
                GROUP BY operating_room, operating_room_number
                ORDER BY surgery_count DESC
            ''', params)
            
            room_stats = []
            for row in cursor.fetchall():
                room_name = f"{row[0]}{row[1]}" if row[1] else row[0]
                room_stats.append({
                    'room_name': room_name,
                    'surgery_count': row[2],
                    'usage_days': row[3],
                    'avg_duration': row[4] or 0,
                    'total_duration': row[5] or 0,
                    'avg_surgeries_per_day': round(row[2] / max(row[3], 1), 2)
                })
            
            # 手术室号分布
            room_number_cursor = conn.execute(f'''
                SELECT 
                    COALESCE(operating_room_number, '未知') as room_number,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY operating_room_number
                ORDER BY 
                    CASE 
                        WHEN operating_room_number GLOB '[0-9]*' THEN CAST(operating_room_number AS INTEGER)
                        ELSE 999
                    END
            ''', params)
            
            room_distribution = {row[0]: row[1] for row in room_number_cursor.fetchall()}
            
            return {
                'room_stats': room_stats,
                'room_distribution': room_distribution
            }
    
    def get_surgeon_workload(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """获取外科医生工作量统计"""
        logger.info("获取外科医生工作量统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    surgeon,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT DATE(surgery_date)) as work_days,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration,
                    COUNT(DISTINCT surgery_name) as surgery_types
                FROM surgery_records 
                {where_clause}
                AND surgeon IS NOT NULL AND surgeon != ''
                GROUP BY surgeon
                ORDER BY surgery_count DESC
            ''', params)
            
            result = []
            for row in cursor.fetchall():
                result.append({
                    'surgeon': row[0],
                    'surgery_count': row[1],
                    'work_days': row[2],
                    'avg_duration': row[3] or 0,
                    'total_duration': row[4] or 0,
                    'surgery_types': row[5],
                    'avg_surgeries_per_day': round(row[1] / max(row[2], 1), 2)
                })
            
            return result
    
    def get_assistant_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取助手参与分析"""
        logger.info("获取助手参与分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 一助统计
            first_assistant_cursor = conn.execute(f'''
                SELECT 
                    first_assistant,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                AND first_assistant IS NOT NULL AND first_assistant != ''
                GROUP BY first_assistant
                ORDER BY count DESC
                LIMIT 10
            ''', params)
            
            first_assistants = {row[0]: row[1] for row in first_assistant_cursor.fetchall()}
            
            # 二助统计
            second_assistant_cursor = conn.execute(f'''
                SELECT 
                    second_assistant,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                AND second_assistant IS NOT NULL AND second_assistant != ''
                GROUP BY second_assistant
                ORDER BY count DESC
                LIMIT 10
            ''', params)
            
            second_assistants = {row[0]: row[1] for row in second_assistant_cursor.fetchall()}
            
            # 助手参与率
            participation_cursor = conn.execute(f'''
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN first_assistant IS NOT NULL AND first_assistant != '' THEN 1 END) as with_first,
                    COUNT(CASE WHEN second_assistant IS NOT NULL AND second_assistant != '' THEN 1 END) as with_second,
                    COUNT(CASE WHEN third_assistant IS NOT NULL AND third_assistant != '' THEN 1 END) as with_third
                FROM surgery_records 
                {where_clause}
            ''', params)
            
            participation = participation_cursor.fetchone()
            total = participation[0]
            
            participation_rates = {
                'first_assistant_rate': round((participation[1] / max(total, 1)) * 100, 2),
                'second_assistant_rate': round((participation[2] / max(total, 1)) * 100, 2),
                'third_assistant_rate': round((participation[3] / max(total, 1)) * 100, 2)
            }
            
            return {
                'first_assistants': first_assistants,
                'second_assistants': second_assistants,
                'participation_rates': participation_rates
            }
    
    def get_diagnosis_analysis(self, start_date: str = None, end_date: str = None, limit: int = 15) -> List[Dict[str, Any]]:
        """获取术前诊断分析"""
        logger.info(f"获取术前诊断分析 (Top {limit})")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    preoperative_diagnosis,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM surgery_records {where_clause}), 2) as percentage
                FROM surgery_records 
                {where_clause}
                AND preoperative_diagnosis IS NOT NULL AND preoperative_diagnosis != ''
                GROUP BY preoperative_diagnosis
                ORDER BY count DESC
                LIMIT ?
            ''', params + [limit])
            
            result = []
            for i, row in enumerate(cursor.fetchall(), 1):
                result.append({
                    'rank': i,
                    'diagnosis': row[0],
                    'count': row[1],
                    'percentage': row[2]
                })
            
            return result
    
    def get_pathological_specimen_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, int]:
        """获取病理标本分析"""
        logger.info("获取病理标本分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    CASE 
                        WHEN pathological_specimen = '无' OR pathological_specimen = '' OR pathological_specimen IS NULL THEN '无病理标本'
                        ELSE '有病理标本'
                    END as specimen_status,
                    COUNT(*) as count
                FROM surgery_records 
                {where_clause}
                GROUP BY specimen_status
                ORDER BY count DESC
            ''', params)
            
            return {row[0]: row[1] for row in cursor.fetchall()}
    
    def _build_date_filter(self, start_date: str = None, end_date: str = None) -> Tuple[str, List]:
        """构建日期过滤条件"""
        conditions = []
        params = []
        
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
    
    def _build_where_clause(self, start_date: str = None, end_date: str = None, additional_conditions: List[str] = None) -> Tuple[str, List]:
        """构建完整的WHERE子句"""
        conditions = []
        params = []
        
        # 添加日期条件
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        # 添加额外条件
        if additional_conditions:
            conditions.extend(additional_conditions)
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
