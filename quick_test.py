#!/usr/bin/env python3
"""
快速测试数据导入功能
"""

import requests
import pandas as pd
import tempfile
import os

def test_basic_functionality():
    """测试基本功能"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 开始快速功能测试...")
    
    # 1. 测试主页访问
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 主页访问正常")
        else:
            print(f"❌ 主页访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False
    
    # 2. 测试数据导入页面
    try:
        response = requests.get(f"{base_url}/data-import", timeout=5)
        if response.status_code == 200:
            print("✅ 数据导入页面访问正常")
        else:
            print(f"❌ 数据导入页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据导入页面访问异常: {e}")
        return False
    
    # 3. 测试导入历史页面
    try:
        response = requests.get(f"{base_url}/import-history", timeout=5)
        if response.status_code == 200:
            print("✅ 导入历史页面访问正常")
        else:
            print(f"❌ 导入历史页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 导入历史页面访问异常: {e}")
        return False
    
    # 4. 测试模板下载API
    try:
        response = requests.get(f"{base_url}/api/download-template?type=surgery_records", timeout=10)
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            if 'excel' in content_type or 'spreadsheet' in content_type:
                print("✅ 手术记录模板下载正常")
            else:
                print(f"⚠️ 模板下载成功但格式可能不正确: {content_type}")
        else:
            print(f"❌ 模板下载失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模板下载异常: {e}")
        return False
    
    # 5. 测试导入历史API
    try:
        response = requests.get(f"{base_url}/api/import-history?limit=5", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if 'success' in result:
                print("✅ 导入历史API正常")
            else:
                print("⚠️ 导入历史API响应格式异常")
        else:
            print(f"❌ 导入历史API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 导入历史API异常: {e}")
        return False
    
    # 6. 测试简单的数据导入（创建一个小的测试文件）
    try:
        # 创建测试Excel文件
        test_data = {
            '患者ID': ['P001', 'P002'],
            '患者姓名': ['测试患者1', '测试患者2'],
            '手术日期': ['2024-01-15', '2024-01-16'],
            '手术类型': ['测试手术A', '测试手术B'],
            '麻醉方式': ['全身麻醉', '局部麻醉'],
            '麻醉医师': ['医生A', '医生B'],
            '主刀医师': ['医生C', '医生D']
        }
        
        df = pd.DataFrame(test_data)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            df.to_excel(tmp.name, index=False)
            test_file_path = tmp.name
        
        # 尝试上传测试文件
        with open(test_file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'enable_privacy': 'true',
                'field_mappings': '{}',
                'import_config': '{}'
            }
            
            response = requests.post(f"{base_url}/api/import-data", 
                                   files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 数据导入测试成功")
                    print(f"   - 总记录数: {result.get('total_records', 0)}")
                    print(f"   - 新记录数: {result.get('new_records', 0)}")
                else:
                    print(f"⚠️ 数据导入失败: {result.get('error', '未知错误')}")
            else:
                print(f"❌ 数据导入API调用失败: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"   错误信息: {error_info.get('error', '无详细信息')}")
                except:
                    print(f"   响应内容: {response.text[:200]}")
        
        # 清理测试文件
        os.unlink(test_file_path)
        
    except Exception as e:
        print(f"❌ 数据导入测试异常: {e}")
        return False
    
    print("\n🎉 基本功能测试完成！")
    return True

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n✅ 所有基本功能测试通过")
        print("📝 建议：")
        print("   1. 在浏览器中访问 http://127.0.0.1:5000/data-import 测试完整的用户界面")
        print("   2. 尝试下载模板并导入真实数据")
        print("   3. 测试批量导入功能")
        print("   4. 查看导入历史和详细日志")
    else:
        print("\n❌ 部分功能测试失败，请检查相关问题")
