"""
人员统计模块
"""
from typing import Dict, Any, List, Tuple
from database.models import DatabaseManager
from utils.logger import logger


class StaffStatistics:
    """医护人员统计分析"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_staff_overview(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取人员概览统计"""
        logger.info("获取人员概览统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    COUNT(DISTINCT anesthesiologist) as total_anesthesiologists,
                    COUNT(DISTINCT surgeon) as total_surgeons,
                    COUNT(DISTINCT first_assistant) as total_first_assistants,
                    COUNT(DISTINCT second_assistant) as total_second_assistants,
                    COUNT(DISTINCT scrub_nurse_1) as total_scrub_nurses,
                    COUNT(DISTINCT circulating_nurse_1) as total_circulating_nurses,
                    COUNT(DISTINCT perfusionist) as total_perfusionists
                FROM surgery_records 
                {where_clause}
            ''', params)
            
            overview = cursor.fetchone()
            
            return {
                'total_anesthesiologists': overview[0] or 0,
                'total_surgeons': overview[1] or 0,
                'total_first_assistants': overview[2] or 0,
                'total_second_assistants': overview[3] or 0,
                'total_scrub_nurses': overview[4] or 0,
                'total_circulating_nurses': overview[5] or 0,
                'total_perfusionists': overview[6] or 0
            }
    
    def get_anesthesiologist_performance(self, start_date: str = None, end_date: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """获取麻醉医生绩效统计"""
        logger.info(f"获取麻醉医生绩效统计 (Top {limit})")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    anesthesiologist,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT DATE(surgery_date)) as work_days,
                    COUNT(DISTINCT department) as departments_served,
                    COUNT(DISTINCT surgery_type) as surgery_types,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration,
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complication_cases,
                    COUNT(CASE WHEN postoperative_analgesia IS NOT NULL AND postoperative_analgesia != '' AND postoperative_analgesia != '否' THEN 1 END) as analgesia_cases
                FROM surgery_records 
                {where_clause}
                AND anesthesiologist IS NOT NULL AND anesthesiologist != ''
                GROUP BY anesthesiologist
                ORDER BY surgery_count DESC
                LIMIT ?
            ''', params + [limit])
            
            result = []
            for i, row in enumerate(cursor.fetchall(), 1):
                surgery_count = row[1]
                work_days = row[2]
                complication_cases = row[7]
                analgesia_cases = row[8]
                
                result.append({
                    'rank': i,
                    'anesthesiologist': row[0],
                    'surgery_count': surgery_count,
                    'work_days': work_days,
                    'departments_served': row[3],
                    'surgery_types': row[4],
                    'avg_duration': row[5] or 0,
                    'total_duration': row[6] or 0,
                    'avg_surgeries_per_day': round(surgery_count / max(work_days, 1), 2),
                    'complication_rate': round((complication_cases / max(surgery_count, 1)) * 100, 2),
                    'analgesia_rate': round((analgesia_cases / max(surgery_count, 1)) * 100, 2)
                })
            
            return result
    
    def get_surgeon_performance(self, start_date: str = None, end_date: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """获取外科医生绩效统计"""
        logger.info(f"获取外科医生绩效统计 (Top {limit})")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            cursor = conn.execute(f'''
                SELECT 
                    surgeon,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT DATE(surgery_date)) as work_days,
                    COUNT(DISTINCT department) as departments_served,
                    COUNT(DISTINCT surgery_name) as surgery_types,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration,
                    COUNT(CASE WHEN complications IS NOT NULL AND complications != '' AND complications != '无' THEN 1 END) as complication_cases,
                    COUNT(CASE WHEN first_assistant IS NOT NULL AND first_assistant != '' THEN 1 END) as with_assistant_cases
                FROM surgery_records 
                {where_clause}
                AND surgeon IS NOT NULL AND surgeon != ''
                GROUP BY surgeon
                ORDER BY surgery_count DESC
                LIMIT ?
            ''', params + [limit])
            
            result = []
            for i, row in enumerate(cursor.fetchall(), 1):
                surgery_count = row[1]
                work_days = row[2]
                complication_cases = row[7]
                with_assistant_cases = row[8]
                
                result.append({
                    'rank': i,
                    'surgeon': row[0],
                    'surgery_count': surgery_count,
                    'work_days': work_days,
                    'departments_served': row[3],
                    'surgery_types': row[4],
                    'avg_duration': row[5] or 0,
                    'total_duration': row[6] or 0,
                    'avg_surgeries_per_day': round(surgery_count / max(work_days, 1), 2),
                    'complication_rate': round((complication_cases / max(surgery_count, 1)) * 100, 2),
                    'assistant_usage_rate': round((with_assistant_cases / max(surgery_count, 1)) * 100, 2)
                })
            
            return result
    
    def get_nurse_workload(self, start_date: str = None, end_date: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """获取护士工作量统计"""
        logger.info("获取护士工作量统计")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 洗手护士统计
            scrub_cursor = conn.execute(f'''
                SELECT 
                    scrub_nurse_1 as nurse_name,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT DATE(surgery_date)) as work_days,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration
                FROM surgery_records 
                {where_clause}
                AND scrub_nurse_1 IS NOT NULL AND scrub_nurse_1 != ''
                GROUP BY scrub_nurse_1
                ORDER BY surgery_count DESC
                LIMIT 15
            ''', params)
            
            scrub_nurses = []
            for i, row in enumerate(scrub_cursor.fetchall(), 1):
                scrub_nurses.append({
                    'rank': i,
                    'nurse_name': row[0],
                    'surgery_count': row[1],
                    'work_days': row[2],
                    'avg_duration': row[3] or 0,
                    'total_duration': row[4] or 0,
                    'avg_surgeries_per_day': round(row[1] / max(row[2], 1), 2)
                })
            
            # 巡回护士统计
            circulating_cursor = conn.execute(f'''
                SELECT 
                    circulating_nurse_1 as nurse_name,
                    COUNT(*) as surgery_count,
                    COUNT(DISTINCT DATE(surgery_date)) as work_days,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration,
                    SUM(CAST(duration_minutes AS REAL)) as total_duration
                FROM surgery_records 
                {where_clause}
                AND circulating_nurse_1 IS NOT NULL AND circulating_nurse_1 != ''
                GROUP BY circulating_nurse_1
                ORDER BY surgery_count DESC
                LIMIT 15
            ''', params)
            
            circulating_nurses = []
            for i, row in enumerate(circulating_cursor.fetchall(), 1):
                circulating_nurses.append({
                    'rank': i,
                    'nurse_name': row[0],
                    'surgery_count': row[1],
                    'work_days': row[2],
                    'avg_duration': row[3] or 0,
                    'total_duration': row[4] or 0,
                    'avg_surgeries_per_day': round(row[1] / max(row[2], 1), 2)
                })
            
            return {
                'scrub_nurses': scrub_nurses,
                'circulating_nurses': circulating_nurses
            }
    
    def get_team_collaboration_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取团队协作分析"""
        logger.info("获取团队协作分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 医生-麻醉师合作频次
            doctor_anesthesia_cursor = conn.execute(f'''
                SELECT 
                    surgeon,
                    anesthesiologist,
                    COUNT(*) as collaboration_count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration
                FROM surgery_records 
                {where_clause}
                AND surgeon IS NOT NULL AND surgeon != ''
                AND anesthesiologist IS NOT NULL AND anesthesiologist != ''
                GROUP BY surgeon, anesthesiologist
                HAVING COUNT(*) >= 3
                ORDER BY collaboration_count DESC
                LIMIT 20
            ''', params)
            
            doctor_anesthesia_pairs = []
            for row in doctor_anesthesia_cursor.fetchall():
                doctor_anesthesia_pairs.append({
                    'surgeon': row[0],
                    'anesthesiologist': row[1],
                    'collaboration_count': row[2],
                    'avg_duration': row[3] or 0
                })
            
            # 医生-助手合作频次
            doctor_assistant_cursor = conn.execute(f'''
                SELECT 
                    surgeon,
                    first_assistant,
                    COUNT(*) as collaboration_count,
                    ROUND(AVG(CAST(duration_minutes AS REAL)), 2) as avg_duration
                FROM surgery_records 
                {where_clause}
                AND surgeon IS NOT NULL AND surgeon != ''
                AND first_assistant IS NOT NULL AND first_assistant != ''
                GROUP BY surgeon, first_assistant
                HAVING COUNT(*) >= 3
                ORDER BY collaboration_count DESC
                LIMIT 20
            ''', params)
            
            doctor_assistant_pairs = []
            for row in doctor_assistant_cursor.fetchall():
                doctor_assistant_pairs.append({
                    'surgeon': row[0],
                    'first_assistant': row[1],
                    'collaboration_count': row[2],
                    'avg_duration': row[3] or 0
                })
            
            # 团队完整性分析
            team_completeness_cursor = conn.execute(f'''
                SELECT 
                    COUNT(*) as total_surgeries,
                    COUNT(CASE WHEN surgeon IS NOT NULL AND surgeon != '' THEN 1 END) as with_surgeon,
                    COUNT(CASE WHEN anesthesiologist IS NOT NULL AND anesthesiologist != '' THEN 1 END) as with_anesthesiologist,
                    COUNT(CASE WHEN first_assistant IS NOT NULL AND first_assistant != '' THEN 1 END) as with_first_assistant,
                    COUNT(CASE WHEN scrub_nurse_1 IS NOT NULL AND scrub_nurse_1 != '' THEN 1 END) as with_scrub_nurse,
                    COUNT(CASE WHEN circulating_nurse_1 IS NOT NULL AND circulating_nurse_1 != '' THEN 1 END) as with_circulating_nurse,
                    COUNT(CASE WHEN surgeon IS NOT NULL AND surgeon != '' 
                                   AND anesthesiologist IS NOT NULL AND anesthesiologist != ''
                                   AND scrub_nurse_1 IS NOT NULL AND scrub_nurse_1 != ''
                                   AND circulating_nurse_1 IS NOT NULL AND circulating_nurse_1 != ''
                              THEN 1 END) as complete_team
                FROM surgery_records {where_clause}
            ''', params)
            
            team_data = team_completeness_cursor.fetchone()
            total = team_data[0]
            
            team_completeness = {
                'total_surgeries': total,
                'surgeon_rate': round((team_data[1] / max(total, 1)) * 100, 2),
                'anesthesiologist_rate': round((team_data[2] / max(total, 1)) * 100, 2),
                'first_assistant_rate': round((team_data[3] / max(total, 1)) * 100, 2),
                'scrub_nurse_rate': round((team_data[4] / max(total, 1)) * 100, 2),
                'circulating_nurse_rate': round((team_data[5] / max(total, 1)) * 100, 2),
                'complete_team_rate': round((team_data[6] / max(total, 1)) * 100, 2)
            }
            
            return {
                'doctor_anesthesia_pairs': doctor_anesthesia_pairs,
                'doctor_assistant_pairs': doctor_assistant_pairs,
                'team_completeness': team_completeness
            }
    
    def get_staff_specialization_analysis(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取人员专业化分析"""
        logger.info("获取人员专业化分析")
        
        with self.db.get_connection() as conn:
            where_clause, params = self._build_date_filter(start_date, end_date)
            
            # 麻醉医生专业化程度
            anesthesia_specialization_cursor = conn.execute(f'''
                SELECT 
                    anesthesiologist,
                    COUNT(DISTINCT anesthesia_method) as method_variety,
                    COUNT(DISTINCT department) as department_variety,
                    COUNT(*) as total_cases,
                    GROUP_CONCAT(DISTINCT anesthesia_method) as methods
                FROM surgery_records 
                {where_clause}
                AND anesthesiologist IS NOT NULL AND anesthesiologist != ''
                AND anesthesia_method IS NOT NULL AND anesthesia_method != ''
                GROUP BY anesthesiologist
                HAVING COUNT(*) >= 5
                ORDER BY method_variety DESC, total_cases DESC
                LIMIT 15
            ''', params)
            
            anesthesia_specialization = []
            for row in anesthesia_specialization_cursor.fetchall():
                anesthesia_specialization.append({
                    'anesthesiologist': row[0],
                    'method_variety': row[1],
                    'department_variety': row[2],
                    'total_cases': row[3],
                    'specialization_score': round(row[3] / max(row[1] * row[2], 1), 2),  # 案例数 / (方法数 * 科室数)
                    'methods': row[4].split(',') if row[4] else []
                })
            
            # 外科医生专业化程度
            surgeon_specialization_cursor = conn.execute(f'''
                SELECT 
                    surgeon,
                    COUNT(DISTINCT surgery_name) as surgery_variety,
                    COUNT(DISTINCT department) as department_variety,
                    COUNT(*) as total_cases
                FROM surgery_records 
                {where_clause}
                AND surgeon IS NOT NULL AND surgeon != ''
                AND surgery_name IS NOT NULL AND surgery_name != ''
                GROUP BY surgeon
                HAVING COUNT(*) >= 5
                ORDER BY surgery_variety DESC, total_cases DESC
                LIMIT 15
            ''', params)
            
            surgeon_specialization = []
            for row in surgeon_specialization_cursor.fetchall():
                surgeon_specialization.append({
                    'surgeon': row[0],
                    'surgery_variety': row[1],
                    'department_variety': row[2],
                    'total_cases': row[3],
                    'specialization_score': round(row[3] / max(row[1] * row[2], 1), 2)
                })
            
            return {
                'anesthesia_specialization': anesthesia_specialization,
                'surgeon_specialization': surgeon_specialization
            }
    
    def _build_date_filter(self, start_date: str = None, end_date: str = None) -> Tuple[str, List]:
        """构建日期过滤条件"""
        conditions = []
        params = []
        
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
    
    def _build_where_clause(self, start_date: str = None, end_date: str = None, additional_conditions: List[str] = None) -> Tuple[str, List]:
        """构建完整的WHERE子句"""
        conditions = []
        params = []
        
        # 添加日期条件
        if start_date and end_date:
            conditions.append("surgery_date BETWEEN ? AND ?")
            params = [start_date, end_date]
        elif start_date:
            conditions.append("surgery_date >= ?")
            params = [start_date]
        elif end_date:
            conditions.append("surgery_date <= ?")
            params = [end_date]
        
        # 添加额外条件
        if additional_conditions:
            conditions.extend(additional_conditions)
        
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        else:
            where_clause = ""
        
        return where_clause, params
