"""
核心数据处理模块
整合数据清洗、统计计数、月份分割功能
"""
import pandas as pd
import re
import os
import uuid
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from config.settings import ANESTHESIA_MAPPING, OUTPUT_DIR
from utils.logger import logger
from utils.performance import timing_decorator, memory_monitor, PerformanceContext
from utils.error_handler import (
    safe_execute, DataProcessingError, ValidationError,
    validate_file, validate_dataframe, ErrorContext
)
from utils.data_privacy import DataPrivacyProcessor, get_default_privacy_config


class AnesthesiaDataProcessor:
    """麻醉数据处理器"""
    
    def __init__(self):
        self.session_id = str(uuid.uuid4())
        self.processed_data = None
        self.statistics = {}
        self.privacy_processor = DataPrivacyProcessor()
        self.privacy_summary = None
        
    def remove_brackets_and_content(self, text: str) -> str:
        """
        移除括号及其内容
        
        Args:
            text: 输入文本
            
        Returns:
            清理后的文本
        """
        if pd.isna(text):
            return ""
        
        text = str(text)
        # 移除各种括号及其内容
        text = re.sub(r'\（.*?\)', '', text)
        text = re.sub(r'\（.*?\）', '', text)
        text = re.sub(r'\(.*?\)', '', text)
        text = re.sub(r'\(.*?\）', '', text)
        return text.strip()
    
    def split_anesthesia_method(self, text: str) -> Tuple[str, str]:
        """
        分割麻醉方法为主要方式和复合方式

        Args:
            text: 麻醉方法文本

        Returns:
            (主要麻醉方式, 复合麻醉方式)
        """
        if not text or pd.isna(text):
            return None, None

        text = str(text)

        # 检查是否包含+号分隔符
        if '+' in text:
            parts = text.split('+', 1)
            primary = parts[0].strip()
            compound = parts[1].strip() if len(parts) > 1 else ""
        else:
            # 使用符号分割
            parts = re.split(r'(\W+)', text)
            primary = parts[0] if parts else ""
            compound = ''.join(parts[1:]).strip() if len(parts) > 1 else ""

        return primary, compound if compound else ""
    
    def standardize_anesthesia_term(self, text: str) -> str:
        """
        标准化麻醉术语
        
        Args:
            text: 原始麻醉术语
            
        Returns:
            标准化后的术语
        """
        if not text or pd.isna(text):
            return ""
        
        text = str(text)
        
        # 使用配置中的映射
        for key, value in ANESTHESIA_MAPPING.items():
            if key in text:
                return value
        
        return text
    
    @timing_decorator
    def clean_anesthesia_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗麻醉数据
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        logger.info("开始清洗麻醉数据")
        
        # 创建副本避免修改原数据
        cleaned_df = df.copy()
        
        # 清理麻醉方法字段
        if '麻醉方法' in cleaned_df.columns:
            cleaned_df['麻醉方法1'] = cleaned_df['麻醉方法'].apply(self.remove_brackets_and_content)
            
            # 分割主要和复合麻醉方式
            split_result = cleaned_df['麻醉方法1'].apply(self.split_anesthesia_method)
            cleaned_df[['left_column', 'right_column']] = pd.DataFrame(
                split_result.tolist(), index=cleaned_df.index
            )
            
            # 标准化术语
            cleaned_df['主要麻醉方式'] = cleaned_df['left_column'].apply(self.standardize_anesthesia_term)
            cleaned_df['复合麻醉方式'] = cleaned_df['right_column'].apply(self.standardize_anesthesia_term)
            
            # 清理临时列
            cleaned_df.drop(['麻醉方法1', 'left_column', 'right_column'], axis=1, inplace=True)
        
        logger.info(f"数据清洗完成，处理了 {len(cleaned_df)} 条记录")
        return cleaned_df

    @timing_decorator
    def apply_data_privacy(self, df: pd.DataFrame, privacy_config: Dict[str, Any] = None) -> pd.DataFrame:
        """
        应用数据脱敏处理

        Args:
            df: 原始数据DataFrame
            privacy_config: 脱敏配置，如果为None则使用默认配置

        Returns:
            脱敏后的DataFrame
        """
        if privacy_config is None:
            privacy_config = get_default_privacy_config()

        logger.info("开始应用数据脱敏处理")

        # 应用脱敏处理
        anonymized_df = self.privacy_processor.anonymize_dataframe(df, privacy_config)

        # 保存脱敏摘要
        self.privacy_summary = self.privacy_processor.get_privacy_summary()

        logger.info("数据脱敏处理完成")
        return anonymized_df

    def preview_data(self, file_path: str, max_rows: int = 10) -> Dict[str, Any]:
        """
        预览数据文件

        Args:
            file_path: 文件路径
            max_rows: 最大预览行数

        Returns:
            预览结果字典
        """
        logger.info(f"开始预览文件: {file_path}")

        try:
            # 验证文件
            validate_file(file_path)

            # 读取文件
            with ErrorContext("读取Excel文件进行预览"):
                df = pd.read_excel(file_path)
                logger.info(f"成功读取文件，共 {len(df)} 条记录")

            # 验证数据
            validate_dataframe(df)

            # 获取基本信息
            file_info = {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'columns': df.columns.tolist(),
                'file_size': os.path.getsize(file_path)
            }

            # 获取预览数据
            preview_df = df.head(max_rows)

            # 转换为可序列化的格式
            preview_data = []
            for index, row in preview_df.iterrows():
                row_data = {}
                for col in df.columns:
                    value = row[col]
                    if pd.isna(value):
                        row_data[col] = None
                    else:
                        row_data[col] = str(value)
                preview_data.append(row_data)

            # 检查必需列
            required_columns = ['手术日期', '麻醉方法']
            missing_columns = [col for col in required_columns if col not in df.columns]

            # 检查可选的敏感列
            sensitive_columns = []
            potential_sensitive = ['患者姓名', '患者ID', '住院号', '病案号', '手机号', '联系电话', '身份证号']
            for col in potential_sensitive:
                if col in df.columns:
                    sensitive_columns.append(col)

            result = {
                'success': True,
                'file_info': file_info,
                'preview_data': preview_data,
                'missing_columns': missing_columns,
                'sensitive_columns': sensitive_columns,
                'has_required_columns': len(missing_columns) == 0,
                'preview_rows': len(preview_data)
            }

            logger.info(f"文件预览完成: {file_path}")
            return result

        except Exception as e:
            logger.error(f"预览文件时发生错误: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    @timing_decorator
    def calculate_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        计算统计数据

        Args:
            df: 清洗后的数据DataFrame

        Returns:
            统计结果字典
        """
        logger.info("开始计算统计数据")

        stats = {}

        # 标记复合麻醉（主要麻醉为全身麻醉，且复合麻醉为神经阻滞麻醉）
        df['复合麻醉'] = df.apply(
            lambda row: '复合麻醉' if (
                row.get('主要麻醉方式') == '全身麻醉' and
                row.get('复合麻醉方式') == '神经阻滞麻醉'
            ) else None,
            axis=1
        )

        # 更新麻醉方式：当全身麻醉且复合麻醉方式为神经阻滞麻醉时，将其标记为复合麻醉
        df['麻醉方式'] = df['主要麻醉方式']
        df.loc[df['复合麻醉'] == '复合麻醉', '麻醉方式'] = '复合麻醉'

        # 统计复合麻醉
        compound_anaesthesia_count = df['复合麻醉'].value_counts().get('复合麻醉', 0)

        # 将全身麻醉的记录排除复合麻醉后的统计
        df['麻醉方式'] = df.apply(
            lambda row: row['麻醉方式'] if row['复合麻醉'] is None else None,
            axis=1
        )

        # 统计其他麻醉方式（不包括复合麻醉）
        anesthesia_counts = df['麻醉方式'].value_counts()

        # 统计全身麻醉总数（全身麻醉 + 复合麻醉）
        total_general_anaesthesia = anesthesia_counts.get('全身麻醉', 0) + compound_anaesthesia_count

        # 统计手术室麻醉总数
        total_operating_room_anaesthesia = (
            anesthesia_counts.get('全身麻醉', 0) +
            anesthesia_counts.get('椎管内麻醉', 0) +
            anesthesia_counts.get('神经阻滞麻醉', 0) +
            anesthesia_counts.get('静脉麻醉', 0) +
            compound_anaesthesia_count
        )

        # 统计门诊（无主要麻醉和复合麻醉方式）
        df['门诊'] = df.apply(
            lambda row: '门诊' if (
                pd.isna(row.get('主要麻醉方式')) and
                pd.isna(row.get('复合麻醉方式'))
            ) else None,
            axis=1
        )
        outpatient_counts = df['门诊'].value_counts()

        # 统计术后镇痛和手术类型
        postoperative_analgesia_counts = df.get('术后镇痛', pd.Series()).value_counts()
        surgery_type_counts = df.get('手术类型', pd.Series()).value_counts()

        # 组织统计结果，转换为标准Python类型
        stats['anesthesia'] = {
            'basic_counts': {k: int(v) for k, v in anesthesia_counts.to_dict().items()},
            'compound_count': int(compound_anaesthesia_count),
            'total_general': int(total_general_anaesthesia),
            'total_operating_room': int(total_operating_room_anaesthesia)
        }

        stats['outpatient'] = {k: int(v) for k, v in outpatient_counts.to_dict().items()}
        stats['analgesia'] = {k: int(v) for k, v in postoperative_analgesia_counts.to_dict().items()}
        stats['surgery_type'] = {k: int(v) for k, v in surgery_type_counts.to_dict().items()}

        self.statistics = stats
        logger.info("统计数据计算完成")
        return stats

    def split_by_month(self, df: pd.DataFrame, date_column: str = '手术日期') -> Dict[str, pd.DataFrame]:
        """
        按月份分割数据

        Args:
            df: 数据DataFrame
            date_column: 日期列名

        Returns:
            按月份分组的数据字典
        """
        logger.info("开始按月份分割数据")

        if date_column not in df.columns:
            logger.warning(f"未找到日期列 '{date_column}'")
            return {}

        # 提取月份
        df['month'] = df[date_column].apply(lambda x: str(x)[5:7] if pd.notna(x) else '00')

        # 按月份分组
        monthly_data = {}
        grouped = df.groupby('month')

        for month, group in grouped:
            if month != '00':  # 排除无效日期
                monthly_data[month] = group.drop('month', axis=1)
                logger.info(f"月份 {month}: {len(group)} 条记录")

        logger.info(f"数据分割完成，共 {len(monthly_data)} 个月份")
        return monthly_data

    def save_cleaned_data(self, df: pd.DataFrame, filename: str) -> str:
        """
        保存清洗后的数据

        Args:
            df: 清洗后的数据
            filename: 原文件名

        Returns:
            保存的文件路径
        """
        output_file = OUTPUT_DIR / f"{self.session_id}_{filename}_anesthesia_standardized.xlsx"
        df.to_excel(output_file, index=False)
        logger.info(f"清洗后数据已保存: {output_file}")
        return str(output_file)

    def save_statistics(self, stats: Dict[str, Any], filename: str) -> str:
        """
        保存统计结果到Excel

        Args:
            stats: 统计数据
            filename: 原文件名

        Returns:
            保存的文件路径
        """
        from openpyxl import Workbook
        from openpyxl.styles import Alignment, Border, Side, PatternFill, Font

        output_file = OUTPUT_DIR / f"{self.session_id}_{filename}_anesthesia_standardized_count.xlsx"

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "统计结果"

        # 设置列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 15

        # 定义样式
        thin_border = Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )
        section_font = Font(bold=True, size=11)
        section_fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
        header_font = Font(bold=True)
        header_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        total_font = Font(bold=True)
        total_fill = PatternFill(start_color="FFD966", end_color="FFD966", fill_type="solid")

        row = 1

        # 麻醉方式统计
        ws.cell(row=row, column=1, value="麻醉方式统计")
        ws.cell(row=row, column=1).font = section_font
        ws.cell(row=row, column=1).fill = section_fill
        ws.merge_cells(f'A{row}:B{row}')
        ws.cell(row=row, column=1).alignment = Alignment(horizontal='center')
        row += 1

        ws.cell(row=row, column=1, value="麻醉方式")
        ws.cell(row=row, column=2, value="例数")
        for col in range(1, 3):
            cell = ws.cell(row=row, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center')
            cell.border = thin_border
        row += 1

        # 基本麻醉方式
        anesthesia_types = ['全身麻醉', '椎管内麻醉', '局部麻醉', '神经阻滞麻醉', '静脉麻醉']
        for atype in anesthesia_types:
            ws.cell(row=row, column=1, value=atype)
            ws.cell(row=row, column=2, value=stats['anesthesia']['basic_counts'].get(atype, 0))
            for col in range(1, 3):
                cell = ws.cell(row=row, column=col)
                cell.alignment = Alignment(horizontal='center')
                cell.border = thin_border
            row += 1

        # 复合麻醉
        ws.cell(row=row, column=1, value="复合麻醉")
        ws.cell(row=row, column=2, value=stats['anesthesia']['compound_count'])
        for col in range(1, 3):
            cell = ws.cell(row=row, column=col)
            cell.alignment = Alignment(horizontal='center')
            cell.border = thin_border
        row += 1

        # 总计行
        for total_type, total_value in [
            ("全身麻醉总数", stats['anesthesia']['total_general']),
            ("手术室麻醉总数", stats['anesthesia']['total_operating_room'])
        ]:
            ws.cell(row=row, column=1, value=total_type)
            ws.cell(row=row, column=2, value=total_value)
            for col in range(1, 3):
                cell = ws.cell(row=row, column=col)
                cell.font = total_font
                cell.fill = total_fill
                cell.alignment = Alignment(horizontal='center')
                cell.border = thin_border
            row += 1

        row += 1  # 空行

        # 其他统计部分（门诊、术后镇痛、手术类型）
        for section_name, section_data in [
            ("门诊统计", stats['outpatient']),
            ("术后镇痛统计", stats['analgesia']),
            ("手术类型统计", stats['surgery_type'])
        ]:
            if section_data:
                ws.cell(row=row, column=1, value=section_name)
                ws.cell(row=row, column=1).font = section_font
                ws.cell(row=row, column=1).fill = section_fill
                ws.merge_cells(f'A{row}:B{row}')
                ws.cell(row=row, column=1).alignment = Alignment(horizontal='center')
                row += 1

                ws.cell(row=row, column=1, value="类型")
                ws.cell(row=row, column=2, value="例数")
                for col in range(1, 3):
                    cell = ws.cell(row=row, column=col)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal='center')
                    cell.border = thin_border
                row += 1

                for item_type, count in section_data.items():
                    if pd.notna(item_type) and str(item_type).strip():
                        ws.cell(row=row, column=1, value=str(item_type))
                        ws.cell(row=row, column=2, value=count)
                        for col in range(1, 3):
                            cell = ws.cell(row=row, column=col)
                            cell.alignment = Alignment(horizontal='center')
                            cell.border = thin_border
                        row += 1

                row += 1  # 空行

        wb.save(output_file)
        logger.info(f"统计结果已保存: {output_file}")
        return str(output_file)

    def save_monthly_data(self, monthly_data: Dict[str, pd.DataFrame], filename: str) -> List[str]:
        """
        保存按月份分割的数据

        Args:
            monthly_data: 按月份分组的数据
            filename: 原文件名

        Returns:
            保存的文件路径列表
        """
        saved_files = []

        for month, data in monthly_data.items():
            output_file = OUTPUT_DIR / f"{self.session_id}_{filename}_{month}.xlsx"
            data.to_excel(output_file, index=False)
            saved_files.append(str(output_file))
            logger.info(f"月份 {month} 数据已保存: {output_file}")

        return saved_files

    @timing_decorator
    @memory_monitor
    def process_file(self, file_path: str, split_by_month: bool = True,
                     enable_privacy: bool = True, privacy_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理单个Excel文件的完整流程

        Args:
            file_path: 文件路径
            split_by_month: 是否按月份分割
            enable_privacy: 是否启用数据脱敏
            privacy_config: 脱敏配置，如果为None则使用默认配置

        Returns:
            处理结果字典
        """
        logger.info(f"开始处理文件: {file_path}")

        try:
            # 验证文件
            validate_file(file_path)

            # 读取文件
            with ErrorContext("读取Excel文件"):
                df = pd.read_excel(file_path)
                logger.info(f"成功读取文件，共 {len(df)} 条记录")

            # 验证数据
            validate_dataframe(df)

            # 获取文件名（不含扩展名）
            filename = Path(file_path).stem

            # 数据清洗
            cleaned_df = self.clean_anesthesia_data(df)

            # 数据脱敏处理
            if enable_privacy:
                cleaned_df = self.apply_data_privacy(cleaned_df, privacy_config)

            self.processed_data = cleaned_df

            # 保存清洗后的数据
            cleaned_file = self.save_cleaned_data(cleaned_df, filename)

            # 计算统计
            stats = self.calculate_statistics(cleaned_df.copy())

            # 保存统计结果
            stats_file = self.save_statistics(stats, filename)

            # 按月份分割（可选）
            monthly_files = []
            if split_by_month and '手术日期' in cleaned_df.columns:
                monthly_data = self.split_by_month(cleaned_df.copy())
                monthly_files = self.save_monthly_data(monthly_data, filename)

            result = {
                'success': True,
                'session_id': self.session_id,
                'original_file': file_path,
                'cleaned_file': cleaned_file,
                'statistics_file': stats_file,
                'monthly_files': monthly_files,
                'statistics': stats,
                'record_count': len(cleaned_df),
                'privacy_enabled': enable_privacy,
                'privacy_summary': self.privacy_summary if enable_privacy else None
            }

            logger.info(f"文件处理完成: {file_path}")
            return result

        except Exception as e:
            logger.error(f"处理文件时发生错误: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'session_id': self.session_id
            }
