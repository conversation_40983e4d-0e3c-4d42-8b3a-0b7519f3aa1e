<!-- SPA版本的患者详情页面 -->
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-base-content">患者详情</h1>
            <p class="text-base-content/60 mt-1">查看患者的详细信息和手术记录</p>
        </div>
        <div class="flex space-x-2">
            <button class="btn btn-outline" onclick="window.history.back()">
                <i class="fas fa-arrow-left mr-2"></i>返回
            </button>
            <button class="btn btn-primary" onclick="editPatient()">
                <i class="fas fa-edit mr-2"></i>编辑患者
            </button>
        </div>
    </div>

    <!-- 患者基本信息 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-user text-primary mr-2"></i>基本信息
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">患者ID</span>
                    </label>
                    <div class="input input-bordered bg-base-200" id="patient-id">P{{ patient_id or '001' }}</div>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">姓名</span>
                    </label>
                    <div class="input input-bordered bg-base-200" id="patient-name">张三</div>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">性别</span>
                    </label>
                    <div class="input input-bordered bg-base-200" id="patient-gender">男</div>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">年龄</span>
                    </label>
                    <div class="input input-bordered bg-base-200" id="patient-age">45岁</div>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">联系电话</span>
                    </label>
                    <div class="input input-bordered bg-base-200" id="patient-phone">138****1234</div>
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">身份证号</span>
                    </label>
                    <div class="input input-bordered bg-base-200" id="patient-id-card">320***********1234</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 手术记录 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <div class="flex items-center justify-between mb-4">
                <h2 class="card-title">
                    <i class="fas fa-procedures text-primary mr-2"></i>手术记录
                </h2>
                <button class="btn btn-outline btn-sm" onclick="refreshSurgeries()">
                    <i class="fas fa-sync-alt mr-2"></i>刷新
                </button>
            </div>
            
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>手术日期</th>
                            <th>手术名称</th>
                            <th>主刀医生</th>
                            <th>麻醉方式</th>
                            <th>手术状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="surgeries-table">
                        <tr>
                            <td>2024-01-15</td>
                            <td>胆囊切除术</td>
                            <td>王医生</td>
                            <td>全身麻醉</td>
                            <td><span class="badge badge-success">已完成</span></td>
                            <td>
                                <button class="btn btn-ghost btn-xs" onclick="viewSurgery(1)">
                                    <i class="fas fa-eye mr-1"></i>查看
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>2023-12-20</td>
                            <td>阑尾切除术</td>
                            <td>李医生</td>
                            <td>局部麻醉</td>
                            <td><span class="badge badge-success">已完成</span></td>
                            <td>
                                <button class="btn btn-ghost btn-xs" onclick="viewSurgery(2)">
                                    <i class="fas fa-eye mr-1"></i>查看
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 麻醉记录 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">
                <i class="fas fa-heartbeat text-primary mr-2"></i>麻醉记录
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                <div class="stat bg-base-200 rounded-lg">
                    <div class="stat-figure text-primary">
                        <i class="fas fa-syringe text-2xl"></i>
                    </div>
                    <div class="stat-title">总麻醉次数</div>
                    <div class="stat-value text-primary">2</div>
                    <div class="stat-desc">最近一次：2024-01-15</div>
                </div>
                
                <div class="stat bg-base-200 rounded-lg">
                    <div class="stat-figure text-secondary">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                    <div class="stat-title">平均麻醉时长</div>
                    <div class="stat-value text-secondary">2.5小时</div>
                    <div class="stat-desc">范围：1.5-3.5小时</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 患者详情页面功能
function editPatient() {
    const patientId = {{ patient_id or 1 }};
    if (window.router) {
        window.router.navigate(`/patients/${patientId}/edit`);
    } else {
        window.location.href = `/patients/${patientId}/edit`;
    }
}

function refreshSurgeries() {
    themeManager.showNotification('手术记录已刷新', 'success');
    // 这里可以添加实际的刷新逻辑
}

function viewSurgery(surgeryId) {
    themeManager.showNotification(`查看手术记录 #${surgeryId}`, 'info');
    // 这里可以添加查看手术详情的逻辑
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('患者详情页面加载完成');
    
    // 模拟加载患者数据
    const patientId = {{ patient_id or 1 }};
    loadPatientData(patientId);
});

function loadPatientData(patientId) {
    // 模拟患者数据
    const mockData = {
        id: `P${String(patientId).padStart(3, '0')}`,
        name: '张三',
        gender: '男',
        age: 45,
        phone: '138****1234',
        idCard: '320***********1234'
    };
    
    // 更新页面数据
    document.getElementById('patient-id').textContent = mockData.id;
    document.getElementById('patient-name').textContent = mockData.name;
    document.getElementById('patient-gender').textContent = mockData.gender;
    document.getElementById('patient-age').textContent = `${mockData.age}岁`;
    document.getElementById('patient-phone').textContent = mockData.phone;
    document.getElementById('patient-id-card').textContent = mockData.idCard;
}
</script>
