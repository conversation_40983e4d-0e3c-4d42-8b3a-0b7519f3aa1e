# 麻醉质控数据管理系统

一个用于麻醉质控数据管理和分析的Web应用系统。

## ✨ 功能特性

- 📊 **数据导入和预览** - 支持Excel文件导入，实时预览数据
- 🔍 **数据管理** - 数据查看、搜索、筛选、排序和分页
- 📈 **统计分析** - 多维度统计分析和可视化图表
- 🎯 **快速筛选** - 支持今天、本周、本月等快速时间筛选
- 🛡️ **隐私保护** - 内置数据脱敏和隐私保护机制
- 📋 **数据质量** - 自动数据质量检查和评分

## 🛠️ 技术栈

- **后端**: Python Flask
- **前端**: HTML5, CSS3, JavaScript ES6, Bootstrap 5
- **数据库**: SQLite
- **数据处理**: Pandas, NumPy
- **图表**: Chart.js

## 🚀 快速开始

### 1. 环境准备
```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境 (Windows)
.venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source .venv/bin/activate
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动应用
```bash
# 使用启动脚本（推荐）
python start.py

# 或直接启动
python src/web/app.py
```

### 4. 访问应用
打开浏览器访问: **http://localhost:5000**

## 📁 项目结构

```
AnesthesiaQCDataManagement/
├── src/                    # 源代码目录
│   ├── config/            # 配置文件
│   ├── core/              # 核心功能
│   ├── database/          # 数据库相关
│   ├── services/          # 业务服务
│   ├── statistics/        # 统计分析模块
│   ├── utils/             # 工具函数
│   └── web/               # Web应用
│       ├── app.py         # Flask应用主文件
│       ├── static/        # 静态资源
│       └── templates/     # HTML模板
├── data/                  # 数据目录
├── uploads/              # 上传文件目录
├── logs/                 # 日志目录
├── start.py             # 启动脚本
├── requirements.txt     # 依赖列表
└── README.md           # 项目说明
```

## 📖 使用指南

### 🔄 数据导入
1. 点击 **"数据导入"** 菜单
2. 选择Excel文件（支持.xlsx, .xls格式）
3. 预览数据内容和结构
4. 确认导入设置
5. 点击导入按钮完成数据导入

### 📊 数据管理
1. 点击 **"数据管理"** 菜单
2. 使用快速筛选选择时间范围
3. 使用搜索框进行关键词搜索
4. 点击列标题进行排序
5. 使用分页浏览大量数据

### 📈 统计分析
1. 点击 **"统计分析"** 菜单
2. 选择分析时间范围
3. 查看多维度统计图表

### 🎯 快速筛选功能
- 今天 / 昨天
- 本周 / 上周  
- 本月 / 上个月
- 本季度 / 上季度
- 今年 / 去年

## 🔧 开发说明

### 数据库
- 使用SQLite数据库，自动初始化
- 数据模型定义在 `src/database/models.py`

### 日志系统
- 日志文件保存在 `logs/` 目录
- 按日期自动轮转

## 📝 许可证

MIT License
